# 🚀 Credit Migration Guide: 7 → 30 Credits

## Overview

This guide explains how to migrate existing free users from 7 credits to 30 credits after the system update.

## 📋 What Changed

- **New free users**: Now receive 30 credits instead of 7
- **Existing free users**: Need to be migrated to have 30 available credits
- **Code updated**: All hardcoded "7" values changed to "30" in the codebase

## 🎯 Migration Options

### Option 1: SQL Migration (Recommended for Supabase)

Execute the following in your Supabase SQL Editor:

```sql
-- Update all free users to have 30 available credits
UPDATE user_profiles 
SET credits = 30 + total_credits_used
WHERE subscription_status = 'free' 
  AND credits < 30 + total_credits_used;
```

**What this does:**
- Finds all free users who have less than 30 available credits
- Sets their total credits to `30 + total_credits_used`
- This ensures they have exactly 30 available credits

### Option 2: Node.js Script

For programmatic migration with logging and verification:

```bash
# Install dependencies (if not already done)
npm install @supabase/supabase-js dotenv

# Run the migration script
node scripts/migrate-credits.js
```

**Features of the Node.js script:**
- ✅ Shows current state before migration
- ✅ Only migrates users who need it
- ✅ Provides detailed logging
- ✅ Verifies migration results
- ✅ Safe to run multiple times

## 🔍 Verification Queries

Check current free user credits:

```sql
SELECT 
  email,
  subscription_status,
  credits,
  total_credits_used,
  (credits - total_credits_used) as available_credits
FROM user_profiles 
WHERE subscription_status = 'free'
ORDER BY available_credits;
```

Count users with 30+ available credits:

```sql
SELECT 
  COUNT(*) as users_with_30_plus_credits
FROM user_profiles 
WHERE subscription_status = 'free' 
  AND (credits - total_credits_used) >= 30;
```

## 📊 Migration Logic

The migration preserves user credit usage history:

| Before Migration | After Migration | Explanation |
|------------------|-----------------|-------------|
| 7 total, 0 used = 7 available | 30 total, 0 used = 30 available | New user gets full 30 |
| 7 total, 3 used = 4 available | 33 total, 3 used = 30 available | Used credits preserved |
| 7 total, 7 used = 0 available | 37 total, 7 used = 30 available | User gets fresh 30 credits |

## 🚨 Important Notes

1. **Safe to run multiple times**: The migration only updates users who need it
2. **Preserves usage history**: `total_credits_used` remains unchanged
3. **Pro users unaffected**: Only free users are migrated
4. **Backward compatible**: New code works with both old and new credit amounts

## 🧪 Testing

To test the migration on a staging environment:

1. Create test users with 7 credits
2. Have them use some credits (e.g., 3 credits)
3. Run the migration
4. Verify they have 30 available credits

## 📈 Expected Results

After migration, all free users should have:
- ✅ **30 available credits** minimum
- ✅ **Preserved usage history** 
- ✅ **Unchanged subscription status**
- ✅ **No data loss**

## 🆘 Rollback (if needed)

If you need to rollback the migration:

```sql
-- CAUTION: Only run if you need to rollback
UPDATE user_profiles 
SET credits = 7 + total_credits_used
WHERE subscription_status = 'free' 
  AND credits = 30 + total_credits_used;
```

## ✅ Post-Migration Checklist

- [ ] Verify all free users have 30+ available credits
- [ ] Test new user registration (should get 30 credits)
- [ ] Test credit deduction still works correctly
- [ ] Update any hardcoded references to "7 credits" in UI/docs
- [ ] Monitor for any credit-related issues

---

**Need help?** Check the migration script logs or run the verification queries above. 