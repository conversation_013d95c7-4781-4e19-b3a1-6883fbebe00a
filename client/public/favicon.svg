<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 32 32">
  <defs>
    <linearGradient id="gradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#6366f1;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#8b5cf6;stop-opacity:1" />
    </linearGradient>
  </defs>
  
  <!-- Background circle -->
  <circle cx="16" cy="16" r="15" fill="url(#gradient)" stroke="#4f46e5" stroke-width="2"/>
  
  <!-- Document icon -->
  <rect x="8" y="6" width="12" height="14" rx="1" fill="white" opacity="0.9"/>
  <rect x="9" y="8" width="8" height="1" fill="#6366f1" opacity="0.7"/>
  <rect x="9" y="10" width="10" height="1" fill="#6366f1" opacity="0.7"/>
  <rect x="9" y="12" width="7" height="1" fill="#6366f1" opacity="0.7"/>
  
  <!-- Chat bubble -->
  <circle cx="22" cy="22" r="6" fill="white" stroke="#6366f1" stroke-width="1.5"/>
  <circle cx="20" cy="21" r="1" fill="#6366f1"/>
  <circle cx="22" cy="21" r="1" fill="#6366f1"/>
  <circle cx="24" cy="21" r="1" fill="#6366f1"/>
</svg>
