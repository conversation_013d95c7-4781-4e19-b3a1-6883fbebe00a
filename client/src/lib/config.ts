/**
 * Configuration utilities for handling dynamic URLs
 * This ensures the app works in both localhost and production environments
 */

/**
 * Get the base URL for the application
 * In development: uses VITE_APP_URL or fallback to window.location.origin
 * In production: should use the environment variable set during deployment
 */
export function getBaseUrl(): string {
  // Check if we have the environment variable (preferred)
  const envUrl = import.meta.env.VITE_APP_URL;
  
  if (envUrl) {
    // Remove trailing slash if present
    return envUrl.replace(/\/$/, '');
  }
  
  // Fallback to current origin (for backward compatibility)
  if (typeof window !== 'undefined') {
    return window.location.origin;
  }
  
  // Server-side fallback (shouldn't happen in client code, but just in case)
  return 'http://localhost:8080';
}

/**
 * Generate API endpoint URL for custom chat UIs
 */
export function generateApiUrl(): string {
  const baseUrl = getBaseUrl();
  return `${baseUrl}/api/embed/chat/ask`;
}

/**
 * Generate sample API request code for custom UIs
 */
export function generateApiSample(params: {
  projectId?: number;
  documentId?: number;
  token?: string;
}): string {
  const apiUrl = generateApiUrl();
  const requestBody: any = {
    question: "Your question here"
  };
  
  if (params.projectId) {
    requestBody.projectId = params.projectId;
  }
  
  if (params.documentId) {
    requestBody.documentId = params.documentId;
  }
  
  if (params.token) {
    requestBody.token = params.token;
  }
  
  return `// Custom UI API Integration
const response = await fetch('${apiUrl}', {
  method: 'POST',
  headers: { 'Content-Type': 'application/json' },
  body: JSON.stringify(${JSON.stringify(requestBody, null, 2)})
});

// Handle streaming response
const reader = response.body.getReader();
const decoder = new TextDecoder();
let fullResponse = "";

while (true) {
  const { done, value } = await reader.read();
  if (done) break;
  
  const chunk = decoder.decode(value);
  const lines = chunk.split('\\n');
  
  for (const line of lines) {
    if (line.startsWith('data: ')) {
      const data = line.slice(6);
      if (data === '[DONE]') break;
      
      try {
        const parsed = JSON.parse(data);
        if (parsed.content) {
          fullResponse += parsed.content;
          // Update your UI with partial response
        }
      } catch (e) {
        // Ignore parsing errors
      }
    }
  }
}

console.log('Complete response:', fullResponse);`;
}

/**
 * Generate embed URL for documents or projects
 */
export function generateEmbedUrl(params: {
  projectId?: number;
  documentId?: number;
  token?: string;
  minimal?: boolean;
  theme?: string;
  hideHeader?: boolean;
}): string {
  const baseUrl = getBaseUrl();
  const urlParams = new URLSearchParams();
  
  if (params.projectId) {
    urlParams.set('projectId', params.projectId.toString());
  }
  
  if (params.documentId) {
    urlParams.set('documentId', params.documentId.toString());
  }
  
  if (params.token) {
    urlParams.set('token', params.token);
  }
  
  if (params.minimal) {
    urlParams.set('minimal', 'true');
  }
  
  if (params.theme && params.theme !== 'default') {
    urlParams.set('theme', params.theme);
  }
  
  if (params.hideHeader) {
    urlParams.set('hideHeader', 'true');
  }
  
  return `${baseUrl}/embed-chat?${urlParams.toString()}`;
}

/**
 * Generate iframe embed code
 */
export function generateEmbedCode(params: {
  projectId?: number;
  documentId?: number;
  token?: string;
  minimal?: boolean;
  theme?: string;
  hideHeader?: boolean;
  width?: string;
  height?: string;
}): string {
  const embedUrl = generateEmbedUrl(params);
  const width = params.width || '100%';
  const height = params.height || '600';
  const borderColor = params.theme === 'dark' ? '#374151' : '#e2e8f0';
  const title = params.projectId ? 'Project Chat Widget' : 'Document Chat Widget';
  
  return `<iframe src="${embedUrl}" width="${width}" height="${height}" style="border: 1px solid ${borderColor}; border-radius: 8px;" title="${title}"></iframe>`;
}

/**
 * Generate cURL request example for API testing
 */
export function generateCurlSample(params: {
  projectId?: number;
  documentId?: number;
  token?: string;
}): string {
  const apiUrl = generateApiUrl();
  const requestBody: any = {
    question: "What is this document about?"
  };
  
  if (params.projectId) {
    requestBody.projectId = params.projectId;
  }
  
  if (params.documentId) {
    requestBody.documentId = params.documentId;
  }
  
  if (params.token) {
    requestBody.token = params.token;
  }
  
  return `curl -X POST "${apiUrl}" \\
  -H "Content-Type: application/json" \\
  -d '${JSON.stringify(requestBody, null, 2)}'`;
} 