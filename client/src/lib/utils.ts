import { type ClassValue, clsx } from "clsx";
import { twMerge } from "tailwind-merge";

export function cn(...inputs: ClassValue[]) {
  return twMerge(clsx(inputs));
}

export function formatFileSize(bytes: number): string {
  if (bytes === 0) return '0 Bytes';
  
  const k = 1024;
  const sizes = ['Bytes', 'KB', 'MB', 'GB'];
  const i = Math.floor(Math.log(bytes) / Math.log(k));
  
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
}

export function formatTimestamp(date: Date): string {
  const now = new Date();
  const diffInMinutes = Math.floor((now.getTime() - date.getTime()) / (1000 * 60));
  
  if (diffInMinutes < 1) return 'Just now';
  if (diffInMinutes < 60) return `${diffInMinutes} minute${diffInMinutes > 1 ? 's' : ''} ago`;
  
  const diffInHours = Math.floor(diffInMinutes / 60);
  if (diffInHours < 24) return `${diffInHours} hour${diffInHours > 1 ? 's' : ''} ago`;
  
  return date.toLocaleDateString();
}

export function getFileIcon(contentType: string): string {
  // PDF
  if (contentType === 'application/pdf') return 'fas fa-file-pdf';
  
  // Microsoft Office
  if (contentType.includes('wordprocessingml') || contentType === 'application/msword') return 'fas fa-file-word';
  if (contentType.includes('presentationml') || contentType === 'application/vnd.ms-powerpoint') return 'fas fa-file-powerpoint';
  if (contentType.includes('spreadsheetml') || contentType === 'application/vnd.ms-excel') return 'fas fa-file-excel';
  
  // Text and code files
  if (contentType === 'text/plain' || contentType === 'text/markdown') return 'fas fa-file-alt';
  if (contentType === 'text/html' || contentType === 'application/xml' || contentType === 'text/xml') return 'fas fa-file-code';
  if (contentType === 'text/csv') return 'fas fa-file-csv';
  
  // Images
  if (contentType.startsWith('image/')) return 'fas fa-file-image';
  
  // Other formats
  // EPUB files are no longer supported (compressed format)
  if (contentType === 'application/rtf') return 'fas fa-file-alt';
  
  return 'fas fa-file';
}

export function getFileIconColor(contentType: string): string {
  // PDF
  if (contentType === 'application/pdf') return 'text-red-600';
  
  // Microsoft Office
  if (contentType.includes('wordprocessingml') || contentType === 'application/msword') return 'text-blue-700';
  if (contentType.includes('presentationml') || contentType === 'application/vnd.ms-powerpoint') return 'text-orange-600';
  if (contentType.includes('spreadsheetml') || contentType === 'application/vnd.ms-excel') return 'text-green-600';
  
  // Text and code files
  if (contentType === 'text/plain') return 'text-blue-600';
  if (contentType === 'text/html' || contentType === 'application/xml' || contentType === 'text/xml') return 'text-purple-600';
  if (contentType === 'text/csv') return 'text-emerald-600';
  if (contentType === 'text/markdown') return 'text-gray-700';
  
  // Images
  if (contentType.startsWith('image/')) return 'text-pink-600';
  
  // Other formats
  // EPUB files are no longer supported (compressed format)
  if (contentType === 'application/rtf') return 'text-slate-600';
  
  return 'text-gray-600';
}
