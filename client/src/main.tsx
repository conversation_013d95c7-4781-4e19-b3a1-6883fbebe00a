import React from "react";
import { createRoot } from "react-dom/client";
import App from "./App";
import "./index.css";

// Fix HMR registration issues
if (typeof window !== 'undefined') {
  // Ensure proper HMR setup
  if (import.meta.hot) {
    import.meta.hot.accept();
  }
}

const container = document.getElementById("root");
if (!container) {
  throw new Error("Root container not found");
}

const root = createRoot(container);
root.render(<App />);
