import { useState, useEffect, useRef } from "react";
import { Card, CardContent } from "@/components/ui/card";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { Button } from "@/components/ui/button";
import { FileUpload } from "@/components/file-upload";
import { ProcessingPipeline } from "@/components/processing-pipeline";
import { DocumentSummary } from "@/components/document-summary";
import { ChatInterface } from "@/components/chat-interface";
import { useQuery } from "@tanstack/react-query";
import { FileText, AlertTriangle } from "lucide-react";
import type { Document } from "@shared/schema";

export default function Home() {
  const [currentDocument, setCurrentDocument] = useState<Document | null>(null);
  const [questionToSend, setQuestionToSend] = useState<string>("");

  // Poll document status when processing
  const { data: documentStatus } = useQuery<Document>({
    queryKey: ['/api/documents', currentDocument?.id],
    enabled: !!currentDocument && ['uploading', 'parsing', 'indexing'].includes(currentDocument.status),
    refetchInterval: 2000,
  });

  // Update document status from polling
  useEffect(() => {
    if (documentStatus) {
      setCurrentDocument(documentStatus);
    }
  }, [documentStatus]);

  const handleUploadSuccess = (document: Document) => {
    setCurrentDocument(document);
  };

  const handleNewUpload = () => {
    setCurrentDocument(null);
  };

  const getProcessingStep = (status: string) => {
    switch (status) {
      case 'uploading': return 'upload';
      case 'parsing': return 'parsing';
      case 'indexing': return 'indexing';
      case 'ready': return 'ready';
      case 'error': return 'error';
      default: return 'upload';
    }
  };

  const handleQuestionClick = (question: string) => {
    setQuestionToSend(question);
  };

  const handleQuestionSent = () => {
    setQuestionToSend("");
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-50 to-slate-100">
      {/* Header */}
      <header className="bg-white shadow-sm border-b border-slate-200">
        <div className="max-w-6xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center h-16">
            <div className="flex items-center space-x-3">
              <div className="w-10 h-10 bg-blue-600 rounded-lg flex items-center justify-center">
                <FileText className="text-white text-lg" />
              </div>
              <div>
                <h1 className="text-xl font-semibold text-slate-800">DocChat AI</h1>
                <p className="text-sm text-slate-500">Intelligent Document Analysis</p>
              </div>
            </div>
            <div className="flex items-center space-x-4">
              <div className="text-sm text-slate-600">
                Welcome to DocChat AI
              </div>
            </div>
          </div>
        </div>
      </header>

      {/* Main Content */}
      <main className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        
        {/* Processing Pipeline */}
        {currentDocument && (
          <ProcessingPipeline currentStep={getProcessingStep(currentDocument.status)} />
        )}

        {/* Upload Section */}
        {!currentDocument && (
          <Card className="mb-8">
            <CardContent className="p-8">
              <div className="text-center mb-6">
                <h2 className="text-2xl font-bold text-slate-800 mb-2">
                  Upload Your Document
                </h2>
                <p className="text-slate-600">
                  Upload PDF or text files to start intelligent document analysis
                </p>
              </div>
              
              <FileUpload onUploadSuccess={handleUploadSuccess} />
            </CardContent>
          </Card>
        )}

        {/* Document Summary and Chat */}
        {currentDocument && currentDocument.status === 'ready' && (
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
            <DocumentSummary 
              document={currentDocument} 
              onQuestionClick={handleQuestionClick}
            />
            <ChatInterface 
              project={{ 
                id: currentDocument.projectId, 
                name: 'Document Chat', 
                description: null, 
                userId: currentDocument.userId, 
                indexId: null, 
                status: 'active', 
                createdAt: new Date(), 
                updatedAt: new Date() 
              }}
              document={currentDocument} 
              questionToSend={questionToSend}
              onQuestionSent={handleQuestionSent}
            />
          </div>
        )}

        {/* Processing States */}
        {currentDocument && ['uploading', 'parsing', 'indexing'].includes(currentDocument.status) && (
          <Card className="mb-8">
            <CardContent className="p-8 text-center">
              <div className="mb-4">
                <div className="w-16 h-16 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-4">
                  <FileText className="w-8 h-8 text-blue-600" />
                </div>
                <h3 className="text-lg font-medium text-slate-700 mb-2">Processing Document</h3>
                <p className="text-slate-500">
                  {currentDocument.status === 'uploading' && 'Uploading your document...'}
                  {currentDocument.status === 'parsing' && 'Parsing document content...'}
                  {currentDocument.status === 'indexing' && 'Creating searchable index...'}
                </p>
              </div>
            </CardContent>
          </Card>
        )}

        {/* Error State */}
        {currentDocument && currentDocument.status === 'error' && (
          <Alert className="mb-8 border-red-200 bg-red-50">
            <AlertTriangle className="h-4 w-4 text-red-600" />
            <AlertDescription className="text-red-700">
              <strong>Processing Error:</strong> {currentDocument.errorMessage || 'An error occurred while processing your document.'}
              <Button
                variant="outline"
                size="sm"
                onClick={handleNewUpload}
                className="ml-4"
              >
                Try Again
              </Button>
            </AlertDescription>
          </Alert>
        )}

        {/* Upload Another Document button removed */}
      </main>
    </div>
  );
}