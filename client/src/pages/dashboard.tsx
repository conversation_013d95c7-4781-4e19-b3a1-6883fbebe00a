import { useState, useEffect } from "react";
import { useLocation } from "wouter";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from "@/components/ui/dialog";
import { AlertDialog, AlertDialogAction, AlertDialogCancel, AlertDialogContent, AlertDialogDescription, AlertDialogFooter, AlertDialogHeader, AlertDialogTitle, AlertDialogTrigger } from "@/components/ui/alert-dialog";

import { Textarea } from "@/components/ui/textarea";
import { Badge } from "@/components/ui/badge";
import { useAuth } from "@/components/auth-provider";
import { useQuery, useMutation, useQueryClient } from "@tanstack/react-query";
import { useToast } from "@/hooks/use-toast";
import { SubscriptionModal } from "@/components/subscription-modal";
import { 
  Plus, 
  FileText, 
  MessageSquare, 
  Calendar, 
  Crown,
  Star,
  FolderOpen,
  Users,
  Target,
  TrendingUp,
  Clock,
  Upload,
  LogOut,
  User,
  Trash2,

} from "lucide-react";
import { formatDistanceToNow } from "date-fns";
import type { Project, ProjectWithCounts, CreateProjectInput } from "@shared/schema";
import { apiRequest } from "@/lib/queryClient";

interface DashboardStats {
  totalProjects: number;
  totalDocuments: number;
  totalMessages: number;
  activeProjects: number;
}

interface UserProfile {
  id: string;
  email: string;
  subscriptionStatus: string;
  credits: number;
  totalCreditsUsed: number;
  creditsRemaining: number;
  projectsCreated: number;
  documentsUploaded: number;
  chatMessagesUsed: number;
  createdAt: string;
  updatedAt: string;
}

interface UserLimits {
  canCreateProject: boolean;
  canUploadDocument: boolean;
  canUseChat: boolean;
  creditsRemaining: number;
  subscriptionStatus: string;
}

export default function Dashboard() {
  const [showCreateModal, setShowCreateModal] = useState(false);
  const [showSubscriptionModal, setShowSubscriptionModal] = useState(false);
  const [subscriptionTrigger, setSubscriptionTrigger] = useState<'CHAT_LIMIT_REACHED' | 'PROJECT_LIMIT_REACHED' | 'UPLOAD_LIMIT_REACHED' | 'INSUFFICIENT_CREDITS'>('INSUFFICIENT_CREDITS');
  const [projectName, setProjectName] = useState("");
  const [projectDescription, setProjectDescription] = useState("");
  const [location, setLocation] = useLocation();
  const { user, signOut, handleAuthError } = useAuth();
  const { toast } = useToast();
  const queryClient = useQueryClient();

  // Fetch user's projects with counts
  const { 
    data: projects = [], 
    isLoading: projectsLoading, 
    error: projectsError,
    isError: isProjectsError
  } = useQuery<ProjectWithCounts[]>({
    queryKey: ["/api/projects/with-counts"],
    enabled: !!user,
    retry: (failureCount, error: any) => {
      // Don't retry on auth errors
      if (handleAuthError?.(error)) {
        return false;
      }
      return failureCount < 2;
    }
  });

  // Fetch dashboard stats
  const { 
    data: stats, 
    error: statsError,
    isError: isStatsError
  } = useQuery<DashboardStats>({
    queryKey: ["/api/dashboard/stats"],
    enabled: !!user,
    retry: (failureCount, error: any) => {
      // Don't retry on auth errors
      if (handleAuthError?.(error)) {
        return false;
      }
      return failureCount < 2;
    }
  });

  // Fetch user profile
  const { 
    data: userProfile, 
    error: profileError,
    isError: isProfileError
  } = useQuery<UserProfile>({
    queryKey: ["/api/user/profile"],
    enabled: !!user,
    retry: (failureCount, error: any) => {
      // Don't retry on auth errors
      if (handleAuthError?.(error)) {
        return false;
      }
      return failureCount < 2;
    }
  });

  // Fetch user limits
  const { 
    data: userLimits, 
    error: limitsError,
    isError: isLimitsError
  } = useQuery<UserLimits>({
    queryKey: ["/api/user/limits"],
    enabled: !!user,
    retry: (failureCount, error: any) => {
      // Don't retry on auth errors
      if (handleAuthError?.(error)) {
        return false;
      }
      return failureCount < 2;
    }
  });

  // Handle errors after queries complete
  useEffect(() => {
    if (isProjectsError && projectsError) {
      const wasHandled = handleAuthError?.(projectsError);
      if (!wasHandled) {
        console.error('Projects fetch error:', projectsError);
        toast({
          title: "Failed to load projects",
          description: "Please refresh the page",
          variant: "destructive",
        });
      }
    }
  }, [isProjectsError, projectsError, handleAuthError, toast]);

  useEffect(() => {
    if (isStatsError && statsError) {
      const wasHandled = handleAuthError?.(statsError);
      if (!wasHandled) {
        console.error('Stats fetch error:', statsError);
        toast({
          title: "Failed to load statistics",
          description: "Please refresh the page",
          variant: "destructive",
        });
      }
    }
  }, [isStatsError, statsError, handleAuthError, toast]);

  useEffect(() => {
    if (isProfileError && profileError) {
      const wasHandled = handleAuthError?.(profileError);
      if (!wasHandled) {
        console.error('Profile fetch error:', profileError);
      }
    }
  }, [isProfileError, profileError, handleAuthError]);

  useEffect(() => {
    if (isLimitsError && limitsError) {
      const wasHandled = handleAuthError?.(limitsError);
      if (!wasHandled) {
        console.error('Limits fetch error:', limitsError);
      }
    }
  }, [isLimitsError, limitsError, handleAuthError]);

  // Debug logging
  // console.log("Dashboard render - Projects:", projects, "Stats:", stats, "User:", user);

  // Create project mutation
  const createProjectMutation = useMutation({
    mutationFn: async (projectData: CreateProjectInput & { redirect?: boolean }) => {
      const { redirect, ...data } = projectData;
      const response = await apiRequest("POST", "/api/projects", data);
      const project = await response.json();
      return { project, redirect };
    },
    onSuccess: ({ project, redirect }) => {
      queryClient.invalidateQueries({ queryKey: ["/api/projects/with-counts"] });
      queryClient.invalidateQueries({ queryKey: ["/api/dashboard/stats"] });
      queryClient.invalidateQueries({ queryKey: ["/api/user/profile"] });
      queryClient.invalidateQueries({ queryKey: ["/api/user/limits"] });
      setShowCreateModal(false);
      setProjectName("");
      setProjectDescription("");
      
      if (redirect) {
        // Navigate to project page for quick upload
        setLocation(`/project/${project.id}`);
        toast({
          title: "Project created",
          description: "Ready to upload your documents!",
        });
      } else {
        toast({
          title: "Project created",
          description: "Your new project is ready for documents!",
        });
      }
    },
    onError: (error: any) => {
      const wasHandled = handleAuthError?.(error);
      if (!wasHandled) {
        // Handle specific rate limiting errors
        if (error.response?.data?.code === 'PROJECT_LIMIT_REACHED') {
          setSubscriptionTrigger('PROJECT_LIMIT_REACHED');
          setShowSubscriptionModal(true);
        } else if (error.response?.data?.code === 'INSUFFICIENT_CREDITS') {
          setSubscriptionTrigger('INSUFFICIENT_CREDITS');
          setShowSubscriptionModal(true);
        } else {
          toast({
            title: "Failed to create project",
            description: error instanceof Error ? error.message : "Please try again",
            variant: "destructive",
          });
        }
      }
    },
  });

  // Delete project mutation
  const deleteProjectMutation = useMutation({
    mutationFn: async (projectId: number) => {
      const response = await apiRequest("DELETE", `/api/projects/${projectId}`);
      return response.json();
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["/api/projects/with-counts"] });
      queryClient.invalidateQueries({ queryKey: ["/api/dashboard/stats"] });
      toast({
        title: "Project deleted",
        description: "The project and all its data have been permanently deleted.",
      });
    },
    onError: (error: any) => {
      const wasHandled = handleAuthError?.(error);
      if (!wasHandled) {
        toast({
          title: "Failed to delete project",
          description: error instanceof Error ? error.message : "Please try again",
          variant: "destructive",
        });
      }
    },
  });

  const handleCreateProject = (e: React.FormEvent) => {
    e.preventDefault();
    if (!projectName?.trim()) return;

    createProjectMutation.mutate({
      name: projectName.trim(),
      description: projectDescription?.trim() || undefined,
    });
  };

  const handleDeleteProject = (projectId: number) => {
    deleteProjectMutation.mutate(projectId);
  };

  const handleSignOut = async () => {
    try {
      // Clear all cached data before signing out
      queryClient.clear();
      
      // Clear any local storage items if any
      localStorage.clear();
      sessionStorage.clear();
      
      // Sign out from Supabase
      await signOut();
      
      // Navigate to home page
      setLocation("/");
      
      toast({
        title: "Signed out successfully",
        description: "Come back anytime!",
      });
    } catch (error) {
      console.error('Sign out error:', error);
      // Force logout on any error
      const wasHandled = handleAuthError?.(error);
      if (!wasHandled) {
        // Force navigation to home on any error
        setLocation("/");
      }
    }
  };

  const getProjectStatusColor = (status: string) => {
    switch (status) {
      case "active": return "bg-green-100 text-green-800";
      case "archived": return "bg-gray-100 text-gray-800";
      default: return "bg-blue-100 text-blue-800";
    }
  };

  if (projectsLoading) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-indigo-50 via-white to-purple-50 flex items-center justify-center">
        <div className="text-center">
          <div className="w-16 h-16 bg-indigo-100 rounded-full flex items-center justify-center mx-auto mb-4">
            <FileText className="w-8 h-8 text-indigo-600 animate-pulse" />
          </div>
          <p className="text-lg text-slate-600">Loading your projects...</p>
        </div>
      </div>
    );
  }

  const safeProjects = Array.isArray(projects) ? projects : [];

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-50 via-white to-blue-50">
      {/* Header */}
      <div className="bg-gradient-to-r from-indigo-600 via-purple-600 to-pink-600 text-white">
        <div className="container mx-auto px-4 py-6">
          {/* Navigation Bar */}
          <div className="flex justify-between items-center mb-8">
            {/* Left: Logo & User Info */}
            <div className="flex items-center space-x-4">
              <div className="flex items-center space-x-3">
                <div className="w-8 h-8 bg-white/20 rounded-full flex items-center justify-center">
                  <User className="w-4 h-4" />
                </div>
                <div className="hidden sm:block">
                  <p className="text-sm text-white/80">Welcome back</p>
                  <p className="text-sm font-medium text-white">
                    {user?.email || 'User'}
                  </p>
                </div>
              </div>
              
              {/* Beta Badge */}
              <div className="hidden lg:flex items-center space-x-2 bg-orange-500/20 backdrop-blur-sm rounded-full px-3 py-1 border border-orange-400/30">
                <div className="w-3 h-3 bg-orange-400 rounded-full flex items-center justify-center">
                  <span className="text-orange-900 text-xs font-bold">β</span>
                </div>
                <span className="text-orange-100 text-xs font-medium">Beta</span>
              </div>
            </div>

            {/* Right: Actions */}
            <div className="flex items-center space-x-3">
              {/* Credits/Plan Status */}
              {userProfile && (
                <div className="hidden sm:flex items-center space-x-2 bg-white/10 rounded-lg px-3 py-2 border border-white/20">
                  {userProfile.subscriptionStatus === 'pro' ? (
                    <>
                      <Crown className="w-4 h-4 text-yellow-300" />
                      <span className="text-white font-medium text-sm">Pro</span>
                    </>
                  ) : (
                    <>
                      <Star className="w-4 h-4 text-blue-300" />
                      <span className="text-white text-sm">
                        {userProfile.creditsRemaining} credits
                      </span>
                    </>
                  )}
                </div>
              )}

              {/* Upgrade Button */}
              {userProfile?.subscriptionStatus === 'free' && (
                <Button
                  size="sm"
                  className="bg-yellow-500 hover:bg-yellow-600 text-yellow-900 font-medium border-0"
                  onClick={() => {
                    setSubscriptionTrigger('INSUFFICIENT_CREDITS');
                    setShowSubscriptionModal(true);
                  }}
                >
                  <Crown className="w-4 h-4 mr-1" />
                  Upgrade
                </Button>
              )}

              {/* New Project Button */}
              <Dialog open={showCreateModal} onOpenChange={setShowCreateModal}>
                <DialogTrigger asChild>
                  <Button
                    size="sm"
                    className="bg-white/10 hover:bg-white/20 text-white border border-white/30"
                    disabled={userLimits && !userLimits.canCreateProject}
                  >
                    <Plus className="w-4 h-4 mr-1" />
                    New Project
                  </Button>
                </DialogTrigger>
                <DialogContent className="mx-4 max-w-md sm:max-w-lg">
                  <DialogHeader>
                    <DialogTitle>Create New Project</DialogTitle>
                  </DialogHeader>
                  <form onSubmit={handleCreateProject} className="space-y-4">
                    <div>
                      <Label htmlFor="projectName">Project Name</Label>
                      <Input
                        id="projectName"
                        value={projectName}
                        onChange={(e) => setProjectName(e.target.value)}
                        placeholder="e.g., Legal Research, Product Documentation"
                        required
                      />
                    </div>
                    <div>
                      <Label htmlFor="projectDescription">Description (Optional)</Label>
                      <Textarea
                        id="projectDescription"
                        value={projectDescription}
                        onChange={(e) => setProjectDescription(e.target.value)}
                        placeholder="Brief description of your project..."
                        rows={3}
                      />
                    </div>
                    <div className="flex justify-end space-x-2">
                      <Button 
                        type="button" 
                        variant="outline"
                        onClick={() => setShowCreateModal(false)}
                      >
                        Cancel
                      </Button>
                      <Button 
                        type="submit"
                        disabled={createProjectMutation.isPending}
                        className="bg-gradient-to-r from-indigo-600 to-purple-600"
                      >
                        {createProjectMutation.isPending ? "Creating..." : "Create Project"}
                      </Button>
                    </div>
                  </form>
                </DialogContent>
              </Dialog>

              {/* Sign Out Button */}
              <Button
                onClick={handleSignOut}
                variant="ghost"
                size="sm"
                className="text-white/80 hover:text-white hover:bg-white/10 border border-white/20"
              >
                <LogOut className="w-4 h-4" />
              </Button>
            </div>
          </div>

          {/* Main Title Section */}
          <div className="text-center">
            <h1 className="text-4xl sm:text-5xl lg:text-6xl font-bold bg-gradient-to-r from-white to-blue-100 bg-clip-text text-transparent mb-4">
              Your AI Workspace
            </h1>
            <p className="text-lg sm:text-xl text-white/90 max-w-2xl mx-auto">
              Manage your document intelligence projects. Upload, analyze, and chat with your documents.
            </p>
          </div>
        </div>
      </div>

      {/* Stats Cards */}
      {stats && (
        <div className="container mx-auto px-4 py-6 lg:py-8">
          <div className="grid grid-cols-2 lg:grid-cols-4 gap-3 sm:gap-4 lg:gap-6 mb-6 lg:mb-8">
            <Card className="stats-card card-shine card-glow card-inset card-floating">
              <CardContent className="stats-card-content">
                <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between">
                  <div className="mb-2 lg:mb-0">
                    <p className="stats-label">Total Projects</p>
                    <p className="stats-value">{stats.totalProjects}</p>
                  </div>
                  <FolderOpen className="w-7 h-7 lg:w-8 lg:h-8 text-indigo-600 stats-icon self-end lg:self-auto" />
                </div>
              </CardContent>
            </Card>
            <Card className="stats-card card-shine card-glow card-inset card-floating">
              <CardContent className="stats-card-content">
                <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between">
                  <div className="mb-2 lg:mb-0">
                    <p className="stats-label">Documents</p>
                    <p className="stats-value">{stats.totalDocuments}</p>
                  </div>
                  <FileText className="w-7 h-7 lg:w-8 lg:h-8 text-blue-600 stats-icon self-end lg:self-auto" />
                </div>
              </CardContent>
            </Card>
            <Card className="stats-card card-shine card-glow card-inset card-floating">
              <CardContent className="stats-card-content">
                <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between">
                  <div className="mb-2 lg:mb-0">
                    <p className="stats-label">AI Conversations</p>
                    <p className="stats-value">{stats.totalMessages}</p>
                  </div>
                  <MessageSquare className="w-7 h-7 lg:w-8 lg:h-8 text-purple-600 stats-icon self-end lg:self-auto" />
                </div>
              </CardContent>
            </Card>
            <Card className="stats-card card-shine card-glow card-inset card-floating">
              <CardContent className="stats-card-content">
                <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between">
                  <div className="mb-2 lg:mb-0">
                    <p className="stats-label">Active Projects</p>
                    <p className="stats-value">{stats.activeProjects}</p>
                  </div>
                  <TrendingUp className="w-7 h-7 lg:w-8 lg:h-8 text-green-600 stats-icon self-end lg:self-auto" />
                </div>
              </CardContent>
            </Card>
          </div>
        </div>
      )}

      {/* Projects Grid */}
      <div className="container mx-auto px-4 pb-8 lg:pb-12">
        {safeProjects.length === 0 ? (
          <div className="text-center py-8 lg:py-12">
            <div className="w-16 h-16 lg:w-20 lg:h-20 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-4 lg:mb-6">
              <FolderOpen className="w-8 h-8 lg:w-10 lg:h-10 text-gray-400" />
            </div>
            <h3 className="text-lg lg:text-xl font-semibold text-gray-700 mb-2">No projects yet</h3>
            <p className="text-sm lg:text-base text-gray-500 mb-6 lg:mb-8 px-4">
              Create your first project to start uploading and analyzing documents with AI.
            </p>
            
            <div className="flex justify-center px-4">
              <Button 
                onClick={() => setShowCreateModal(true)}
                className="bg-gradient-to-r from-indigo-600 to-purple-600 w-full sm:w-auto"
                size="lg"
                disabled={userLimits && !userLimits.canCreateProject}
                title={userLimits && !userLimits.canCreateProject ? 
                  "You've reached the project creation limit. Upgrade to Pro for unlimited projects!" : 
                  "Create your first project"
                }
              >
                <Plus className="w-4 h-4 mr-2" />
                Create Your First Project
              </Button>
            </div>
          </div>
        ) : (
          <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4 lg:gap-6">
            {safeProjects.map((project) => (
              <Card key={project.id} className="project-card card-shine card-glow card-inset card-floating card-paper">
                <CardHeader className="project-card-header">
                  <div className="flex items-start justify-between">
                    <div className="flex-1 min-w-0">
                      <CardTitle className="project-card-title truncate">
                        {project.name}
                      </CardTitle>
                      <div className="flex items-center gap-2">
                        <Badge className={`status-badge-enhanced ${project.status === 'active' ? 'status-active' : 'status-inactive'}`}>
                          {project.status}
                        </Badge>
                      </div>
                    </div>
                    <AlertDialog>
                      <AlertDialogTrigger asChild>
                        <Button 
                          variant="ghost" 
                          size="sm" 
                          className="p-1.5 text-red-500 hover:text-red-600 hover:bg-red-50 shrink-0 ml-3 rounded-lg"
                        >
                          <Trash2 className="w-4 h-4" />
                        </Button>
                      </AlertDialogTrigger>
                      <AlertDialogContent>
                        <AlertDialogHeader>
                          <AlertDialogTitle>Delete Project</AlertDialogTitle>
                          <AlertDialogDescription>
                            Are you sure you want to delete "{project.name}"? This will permanently delete:
                          </AlertDialogDescription>
                          <ul className="list-disc list-inside mt-2 space-y-1 text-sm text-muted-foreground">
                            <li>{project.documentCount} document{project.documentCount !== 1 ? 's' : ''}</li>
                            <li>{project.chatCount} chat message{project.chatCount !== 1 ? 's' : ''}</li>
                            <li>All project data and settings</li>
                          </ul>
                          <p className="mt-3 font-medium text-red-600 text-sm">This action cannot be undone.</p>
                        </AlertDialogHeader>
                        <AlertDialogFooter className="flex-col sm:flex-row space-y-2 sm:space-y-0">
                          <AlertDialogCancel className="w-full sm:w-auto">Cancel</AlertDialogCancel>
                          <AlertDialogAction
                            onClick={() => handleDeleteProject(project.id)}
                            className="bg-red-600 hover:bg-red-700 w-full sm:w-auto"
                            disabled={deleteProjectMutation.isPending}
                          >
                            {deleteProjectMutation.isPending ? "Deleting..." : "Delete Project"}
                          </AlertDialogAction>
                        </AlertDialogFooter>
                      </AlertDialogContent>
                    </AlertDialog>
                  </div>
                  {project.description && (
                    <p className="project-card-description line-clamp-2">{project.description}</p>
                  )}
                </CardHeader>
                <CardContent className="project-card-content">
                  <div className="project-timeline">
                    <Calendar className="w-4 h-4 project-timeline-icon" />
                    <span>Created {formatDistanceToNow(new Date(project.createdAt))} ago</span>
                  </div>
                  
                  {/* Stats row */}
                  <div className="project-stats-grid">
                    <div className="flex gap-4">
                      <div className="project-stat-item">
                        <FileText className="w-3.5 h-3.5" />
                        <span className="stat-number">{project.documentCount}</span>
                        <span className="hidden sm:inline">
                          doc{project.documentCount !== 1 ? 's' : ''}
                        </span>
                      </div>
                      <div className="project-stat-item">
                        <MessageSquare className="w-3.5 h-3.5" />
                        <span className="stat-number">{project.chatCount}</span>
                        <span className="hidden sm:inline">
                          chat{project.chatCount !== 1 ? 's' : ''}
                        </span>
                      </div>
                    </div>
                    {project.readyDocumentCount < project.documentCount && (
                      <div className="ready-indicator">
                        <span>{project.readyDocumentCount} ready</span>
                      </div>
                    )}
                  </div>
                  
                  {/* Action buttons */}
                  <div className="project-actions">
                    <div className="flex flex-col sm:flex-row gap-2.5">
                      <Button 
                        variant="outline" 
                        size="sm"
                        onClick={() => setLocation(`/project/${project.id}`)}
                        className="btn-outline-enhanced w-full sm:flex-1 text-sm"
                      >
                        View Project
                      </Button>
                      <Button 
                        size="sm"
                        className="btn-primary-enhanced w-full sm:flex-1 text-sm"
                        onClick={() => setLocation(`/project/${project.id}`)}
                      >
                        <Upload className="w-3.5 h-3.5 mr-1.5" />
                        Upload
                      </Button>
                    </div>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        )}
      </div>

      {/* Subscription Modal */}
      <SubscriptionModal
        isOpen={showSubscriptionModal}
        onClose={() => setShowSubscriptionModal(false)}
        triggerReason={subscriptionTrigger}
        creditsRemaining={userProfile?.creditsRemaining}
      />
    </div>
  );
} 