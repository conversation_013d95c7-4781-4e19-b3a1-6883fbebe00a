import { useState, useEffect, useRef } from "react";
import { usePara<PERSON>, useLocation } from "wouter";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { Badge } from "@/components/ui/badge";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { AlertDialog, AlertDialogAction, AlertDialogCancel, AlertDialogContent, AlertDialogDescription, AlertDialogFooter, AlertDialogHeader, AlertDialogTitle, AlertDialogTrigger } from "@/components/ui/alert-dialog";
import { FileUpload } from "@/components/file-upload";
import { ProcessingPipeline } from "@/components/processing-pipeline";
import { ChatInterface } from "@/components/chat-interface";
import { EmbedSecurityManager } from "@/components/embed-security-manager";
import { DocumentSummary } from "@/components/document-summary";
import { useAuth } from "@/components/auth-provider";
import { useQuery, useQueryClient, useMutation } from "@tanstack/react-query";
import { useToast } from "@/hooks/use-toast";
import { 
  ArrowLeft, 
  FileText, 
  MessageSquare, 
  Upload,
  AlertTriangle,
  Calendar,
  Database,
  Users,
  Settings,
  CheckCircle,
  Sparkles,
  LogOut,
  User,
  ChevronDown,
  ChevronRight,
  ChevronUp,
  Trash2
} from "lucide-react";
import { formatDistanceToNow } from "date-fns";
import type { Project, Document } from "@shared/schema";
import { apiRequest } from "@/lib/queryClient";

export default function ProjectPage() {
  const { projectId } = useParams<{ projectId: string }>();
  const [location, setLocation] = useLocation();
  const { user, signOut, handleAuthError } = useAuth();
  const { toast } = useToast();
  const queryClient = useQueryClient();
  const chatInterfaceRef = useRef<HTMLDivElement>(null);
  const [documents, setDocuments] = useState<Document[]>([]);
  const [currentDocument, setCurrentDocument] = useState<Document | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [project, setProject] = useState<any>(null);
  const [subscriptionModalOpen, setSubscriptionModalOpen] = useState(false);
  const [embedModalOpen, setEmbedModalOpen] = useState(false);
  const [showSecurityModal, setShowSecurityModal] = useState(false);
  const [shouldFocusChat, setShouldFocusChat] = useState(false);
  const [activeTab, setActiveTab] = useState("documents");
  const [isDeleting, setIsDeleting] = useState<number | null>(null);
  const [expandedSummaries, setExpandedSummaries] = useState<Set<number>>(new Set());
  const [showAllDocs, setShowAllDocs] = useState(false);
  const [hasAutoSwitched, setHasAutoSwitched] = useState(false);
  const [hasAutoExpandedFirst, setHasAutoExpandedFirst] = useState(false);

  // Fetch project details
  const { 
    data: projectData, 
    isLoading: projectLoading,
    error: projectError,
    isError: isProjectError
  } = useQuery<Project>({
    queryKey: [`/api/projects/${projectId}`],
    enabled: !!user && !!projectId,
    retry: (failureCount, error: any) => {
      if (handleAuthError?.(error)) {
        return false;
      }
      return failureCount < 2;
    }
  });

  // Fetch project documents
  const { 
    data: documentsData = [], 
    isLoading: documentsLoading,
    error: documentsError,
    isError: isDocumentsError
  } = useQuery<Document[]>({
    queryKey: [`/api/projects/${projectId}/documents`],
    enabled: !!user && !!projectId,
    retry: (failureCount, error: any) => {
      if (handleAuthError?.(error)) {
        return false;
      }
      return failureCount < 2;
    }
  });

  // Handle errors after queries complete
  useEffect(() => {
    if (isProjectError && projectError) {
      const wasHandled = handleAuthError?.(projectError);
      if (!wasHandled) {
        console.error('Project fetch error:', projectError);
      }
    }
  }, [isProjectError, projectError, handleAuthError]);

  useEffect(() => {
    if (isDocumentsError && documentsError) {
      const wasHandled = handleAuthError?.(documentsError);
      if (!wasHandled) {
        console.error('Documents fetch error:', documentsError);
      }
    }
  }, [isDocumentsError, documentsError, handleAuthError]);

  useEffect(() => {
    if (projectData) {
      setProject(projectData);
    }
    if (documentsData) {
      setDocuments(documentsData);
    }
    setIsLoading(false);
  }, [projectData, documentsData]);

  // Auto-switch to chat tab when ready documents are available (only once)
  const readyDocuments = documents.filter(doc => doc.status === 'ready');
  useEffect(() => {
    if (readyDocuments.length > 0 && !hasAutoSwitched && activeTab === "documents") {
      setActiveTab("chat");
      setHasAutoSwitched(true);
    }
  }, [readyDocuments.length, hasAutoSwitched, activeTab]);

  // Keep first document AI summary open when switching to documents tab (only once)
  useEffect(() => {
    if (activeTab === "documents" && readyDocuments.length > 0 && !hasAutoExpandedFirst) {
      const firstDocumentId = readyDocuments[0]?.id;
      if (firstDocumentId) {
        setExpandedSummaries(prev => {
          const newSet = new Set(prev);
          newSet.add(firstDocumentId);
          return newSet;
        });
        setHasAutoExpandedFirst(true);
      }
    } else if (activeTab !== "documents") {
      // Reset flag when leaving documents tab so it will auto-expand again next time
      setHasAutoExpandedFirst(false);
    }
  }, [activeTab, readyDocuments, hasAutoExpandedFirst]);

  // Poll document status when processing
  const { data: documentStatus } = useQuery<Document>({
    queryKey: [`/api/documents/${currentDocument?.id}`],
    enabled: !!currentDocument && ['uploading', 'parsing', 'indexing'].includes(currentDocument.status),
    refetchInterval: 1000,
    retry: (failureCount, error: any) => {
      if (handleAuthError?.(error)) {
        return false;
      }
      return failureCount < 2;
    }
  });

  // Update document status from polling
  useEffect(() => {
    if (documentStatus) {
      const wasProcessing = currentDocument && ['uploading', 'parsing', 'indexing'].includes(currentDocument.status);
      const isNowReady = documentStatus.status === 'ready';
      
      setCurrentDocument(documentStatus);
      
      // Auto-switch to chat when document becomes ready
      if (wasProcessing && isNowReady) {
        // Invalidate and refetch the documents list to ensure readyDocuments is updated
        queryClient.invalidateQueries({
          queryKey: [`/api/projects/${projectId}/documents`]
        });
        
        setTimeout(() => {
          setActiveTab("chat");
          toast({
            title: "Document ready!",
            description: "Your document is now ready for AI chat. Start asking questions!",
            duration: 5000,
          });
        }, 2000); // Small delay to show completion
      }
    }
  }, [documentStatus, currentDocument, toast, queryClient, projectId]);

  const handleUploadSuccess = (document: Document) => {
    setCurrentDocument(document);
  };

  const handleNewUpload = () => {
    setCurrentDocument(null);
  };

  const toggleSummaryExpansion = (documentId: number) => {
    setExpandedSummaries(prev => {
      const newSet = new Set(prev);
      if (newSet.has(documentId)) {
        newSet.delete(documentId);
      } else {
        newSet.add(documentId);
      }
      return newSet;
    });
  };

  const handleStartChatting = () => {
    setActiveTab("chat");
    // Small delay to ensure tab switch completes before focusing and scrolling
    setTimeout(() => {
      setShouldFocusChat(true);
      // Scroll to the AI Document Assistant section
      if (chatInterfaceRef.current) {
        chatInterfaceRef.current.scrollIntoView({ 
          behavior: 'smooth', 
          block: 'start',
          inline: 'nearest'
        });
      }
    }, 150); // Slightly longer delay to ensure tab content is rendered
  };

  const handleChatFocused = () => {
    setShouldFocusChat(false);
  };

  const handleSignOut = async () => {
    try {
      // Clear all cached data before signing out
      queryClient.clear();
      
      // Clear any local storage items if any
      localStorage.clear();
      sessionStorage.clear();
      
      // Sign out from Supabase
      await signOut();
      
      // Navigate to home page
      setLocation("/");
      
      toast({
        title: "Signed out successfully",
        description: "Come back anytime!",
      });
    } catch (error) {
      console.error('Sign out error:', error);
      // Force logout on any error
      const wasHandled = handleAuthError?.(error);
      if (!wasHandled) {
        setLocation("/");
      }
    }
  };

  const getProcessingStep = (status: string) => {
    switch (status) {
      case 'uploading': return 'upload';
      case 'parsing': return 'parsing';
      case 'indexing': return 'indexing';
      case 'ready': return 'ready';
      case 'error': return 'error';
      default: return 'upload';
    }
  };

  const getStatusBadge = (status: string) => {
    switch (status) {
      case 'ready':
        return <Badge className="bg-green-100 text-green-800">Ready</Badge>;
      case 'uploading':
        return <Badge className="bg-blue-100 text-blue-800">Uploading</Badge>;
      case 'parsing':
        return <Badge className="bg-yellow-100 text-yellow-800">Parsing</Badge>;
      case 'indexing':
        return <Badge className="bg-purple-100 text-purple-800">Indexing</Badge>;
      case 'error':
        return <Badge className="bg-red-100 text-red-800">Error</Badge>;
      default:
        return <Badge className="bg-gray-100 text-gray-800">{status}</Badge>;
    }
  };

  const deleteDocumentMutation = useMutation({
    mutationFn: async (documentId: number) => {
      const response = await apiRequest('DELETE', `/api/documents/${documentId}`);
      if (!response.ok) {
        const error = await response.json();
        throw new Error(error.message || 'Failed to delete document');
      }
      return response.json();
    },
    onSuccess: () => {
      toast({
        title: "Document deleted",
        description: "Document and its embeddings have been deleted successfully.",
      });
      // Invalidate relevant queries
      queryClient.invalidateQueries({ queryKey: [`/api/projects/${projectId}/documents`] });
      queryClient.invalidateQueries({ queryKey: [`/api/projects/${projectId}`] });
      queryClient.invalidateQueries({ queryKey: ['/api/dashboard/stats'] });
    },
    onError: (error: Error) => {
      toast({
        title: "Delete failed",
        description: error.message,
        variant: "destructive",
      });
    },
  });

  const [documentToDelete, setDocumentToDelete] = useState<Document | null>(null);

  const handleDeleteDocument = (document: Document) => {
    setDocumentToDelete(document);
  };

  const confirmDeleteDocument = () => {
    if (documentToDelete) {
      deleteDocumentMutation.mutate(documentToDelete.id);
      setDocumentToDelete(null);
    }
  };

  if (projectLoading || isLoading) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-indigo-50 via-white to-purple-50 flex items-center justify-center">
        <div className="text-center">
          <div className="w-16 h-16 bg-indigo-100 rounded-full flex items-center justify-center mx-auto mb-4">
            <FileText className="w-8 h-8 text-indigo-600 animate-pulse" />
          </div>
          <p className="text-lg text-slate-600">Loading project...</p>
        </div>
      </div>
    );
  }

  if (!project) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-indigo-50 via-white to-purple-50 flex items-center justify-center">
        <div className="text-center">
          <h2 className="text-2xl font-bold text-gray-700 mb-2">Project not found</h2>
          <p className="text-gray-500 mb-4">The project you're looking for doesn't exist.</p>
          <Button onClick={() => setLocation("/dashboard")}>
            <ArrowLeft className="w-4 h-4 mr-2" />
            Back to Dashboard
          </Button>
        </div>
      </div>
    );
  }

  const processingDocuments = documents.filter(doc => ['uploading', 'parsing', 'indexing'].includes(doc.status));
  const errorDocuments = documents.filter(doc => doc.status === 'error');

  return (
    <div className="bg-gradient-to-br from-indigo-50 via-white to-purple-50">
      {/* Header */}
      <div className="bg-gradient-to-r from-indigo-600 via-purple-600 to-pink-600 text-white">
        <div className="container mx-auto px-4 py-4 lg:py-8">
          <div className="flex flex-col space-y-4 lg:space-y-0 lg:flex-row lg:items-center lg:justify-between mb-4 lg:mb-6">
            <Button
              variant="ghost"
              onClick={() => setLocation("/dashboard")}
              className="text-white hover:bg-white/10 self-start"
            >
              <ArrowLeft className="w-4 h-4 mr-2" />
              Back to Dashboard
            </Button>
            
            <div className="flex items-center justify-between w-full lg:w-auto">
              <div className="flex items-center space-x-3">
                <div className="w-8 h-8 bg-white/20 rounded-full flex items-center justify-center">
                  <User className="w-4 h-4" />
                </div>
                <div>
                  <p className="text-xs sm:text-sm text-white/90 truncate max-w-[120px] sm:max-w-none">
                    {user?.email}
                  </p>
                </div>
              </div>
              
              <Button
                onClick={handleSignOut}
                variant="ghost"
                size="sm"
                className="text-white hover:bg-white/10 border border-white/20 text-xs lg:text-sm ml-4"
              >
                <LogOut className="w-3 h-3 lg:w-4 lg:h-4 mr-2" />
                Sign Out
              </Button>
            </div>
          </div>
          
          <div className="text-center">
            <h1 className="text-2xl sm:text-3xl lg:text-4xl font-bold text-white mb-2">{project.name}</h1>
            {project.description && (
              <p className="text-base sm:text-lg lg:text-xl text-white/90 mb-3 lg:mb-4 px-2">{project.description}</p>
            )}
            <div className="flex flex-col sm:flex-row items-center justify-center space-y-2 sm:space-y-0 sm:space-x-4 lg:space-x-6 text-white/80">
              <span className="flex items-center text-sm lg:text-base">
                <FileText className="w-3 h-3 lg:w-4 lg:h-4 mr-2" />
                {documents.length} documents
              </span>
              <span className="flex items-center text-sm lg:text-base">
                <Calendar className="w-3 h-3 lg:w-4 lg:h-4 mr-2" />
                <span className="truncate">Created {formatDistanceToNow(new Date(project.createdAt))} ago</span>
              </span>
              {readyDocuments.length > 0 && (
                <span className="flex items-center text-sm lg:text-base">
                  <CheckCircle className="w-3 h-3 lg:w-4 lg:h-4 mr-2" />
                  {readyDocuments.length} ready for chat
                </span>
              )}
            </div>
          </div>
        </div>
      </div>

      {/* Main Content */}
      <main className="mx-auto px-4 py-6 lg:py-8">
        {/* Processing Pipeline */}
        {currentDocument && (
          <div className="mb-6 lg:mb-8">
            <ProcessingPipeline currentStep={getProcessingStep(currentDocument.status)} />
            
            {/* Pipeline Completion Message */}
            {currentDocument.status === 'ready' && (
              <Card className="mt-4 border-green-200 bg-green-50">
                <CardContent className="p-4 lg:p-6">
                  <div className="text-center mb-4">
                    <div className="flex items-center justify-center mb-3 lg:mb-4">
                      <CheckCircle className="w-6 h-6 lg:w-8 lg:h-8 text-green-600 mr-3" />
                      <Sparkles className="w-6 h-6 lg:w-8 lg:h-8 text-yellow-500" />
                    </div>
                    <h3 className="text-base lg:text-lg font-semibold text-green-800 mb-2">
                      Document Processing Complete!
                    </h3>
                    <p className="text-sm lg:text-base text-green-700 mb-3 lg:mb-4">
                      Your document is now indexed and ready for AI-powered analysis.
                    </p>
                  </div>
                  
                  {/* Quick Actions */}
                  <div className="flex flex-col sm:flex-row gap-3 justify-center items-center">
                    <Button 
                      onClick={handleStartChatting}
                      className="bg-green-600 hover:bg-green-700 w-full sm:w-auto"
                    >
                      <MessageSquare className="w-4 h-4 mr-2" />
                      Start Chatting with AI
                    </Button>
                    
                    <Button
                      variant="outline"
                      onClick={() => {
                        setActiveTab("documents");
                        // Expand the document summary
                        setExpandedSummaries(prev => {
                          const newSet = new Set(prev);
                          newSet.add(currentDocument.id);
                          return newSet;
                        });
                        // Scroll to the summary after a brief delay
                        setTimeout(() => {
                          const element = document.getElementById(`summary-${currentDocument.id}`);
                          if (element) {
                            element.scrollIntoView({ behavior: 'smooth', block: 'start' });
                          }
                        }, 100);
                      }}
                      className="group text-green-600 border-green-200 hover:bg-green-50 hover:border-green-300 w-full sm:w-auto"
                    >
                      <Sparkles className="w-4 h-4 mr-2 group-hover:text-green-700" />
                      View Insights
                    </Button>
                  </div>
                  
                  {/* Quick Summary Preview */}
                  {(() => {
                    const summary = currentDocument.summary as any;
                    return summary?.quick_summary && (
                      <div className="mt-4 p-3 bg-white rounded-lg border border-green-200">
                        <h4 className="text-sm font-medium text-green-800 mb-2 flex items-center">
                          <FileText className="w-4 h-4 mr-2" />
                          Quick Summary
                        </h4>
                        <p className="text-sm text-gray-700 leading-relaxed">
                          {summary.quick_summary}
                        </p>
                      </div>
                    );
                  })()}
                </CardContent>
              </Card>
            )}
          </div>
        )}

        <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
          <TabsList className="grid w-full grid-cols-3 mb-4 sm:mb-6 lg:mb-8 h-auto p-1">
            <TabsTrigger value="documents" className="flex flex-col sm:flex-row items-center p-2 sm:p-3 text-xs sm:text-sm">
              <FileText className="w-3 h-3 sm:w-4 sm:h-4 sm:mr-2 mb-1 sm:mb-0" />
              <span className="text-center">
                <span className="hidden sm:inline">Documents </span>
                <span className="sm:hidden">Docs </span>
                <span className="text-xs">({documents.length})</span>
              </span>
            </TabsTrigger>
            <TabsTrigger 
              value="chat" 
              className="flex flex-col sm:flex-row items-center p-2 sm:p-3 text-xs sm:text-sm"
              disabled={documents.length === 0 || readyDocuments.length === 0}
            >
              <MessageSquare className="w-3 h-3 sm:w-4 sm:h-4 sm:mr-2 mb-1 sm:mb-0" />
              <span className="text-center flex flex-col sm:flex-row items-center">
                <span className="hidden sm:inline">AI Chat</span>
                <span className="sm:hidden">Chat</span>
                {documents.length === 0 ? (
                  // No documents uploaded yet
                  null
                ) : readyDocuments.length > 0 ? (
                  <Badge className="ml-0 sm:ml-2 mt-1 sm:mt-0 bg-green-100 text-green-800 text-xs">
                    Ready
                  </Badge>
                ) : (
                  <Badge className="ml-0 sm:ml-2 mt-1 sm:mt-0 bg-gray-100 text-gray-600 text-xs">
                    <span className="hidden sm:inline">Processing</span>
                    <span className="sm:hidden">Locked</span>
                  </Badge>
                )}
              </span>
            </TabsTrigger>
            <TabsTrigger 
              value="embed" 
              className="flex flex-col sm:flex-row items-center p-2 sm:p-3 text-xs sm:text-sm"
              disabled={documents.length === 0}
            >
              <Settings className="w-3 h-3 sm:w-4 sm:h-4 sm:mr-2 mb-1 sm:mb-0" />
              <span className="text-center">
                <span className="hidden sm:inline">Embed & Security</span>
                <span className="sm:hidden">Embed</span>
              </span>
            </TabsTrigger>
          </TabsList>

          <TabsContent value="documents">
            <div className="space-y-6">
              {/* Document Summaries for Ready Documents - Collapsible */}
              {readyDocuments.length > 0 && (
                <Card className="border-slate-200 bg-white shadow-sm">
                  <CardHeader className="pb-4">
                    <div className="flex items-center justify-between">
                      <CardTitle className="text-lg font-semibold text-gray-900">
                        Documents ({readyDocuments.length})
                      </CardTitle>
                      {readyDocuments.length > 4 && (
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => setShowAllDocs(!showAllDocs)}
                          className="text-indigo-600 hover:text-indigo-700 hover:bg-indigo-50"
                        >
                          {showAllDocs ? (
                            <>
                              <ChevronUp className="w-4 h-4 mr-1" />
                              Show Less
                            </>
                          ) : (
                            <>
                              <ChevronDown className="w-4 h-4 mr-1" />
                              See All
                            </>
                          )}
                        </Button>
                      )}
                    </div>
                  </CardHeader>
                  <CardContent className="pt-0">
                    <div className={`space-y-2 ${showAllDocs && readyDocuments.length > 4 ? 'max-h-96 overflow-y-auto pr-2' : ''}`}>
                {(showAllDocs ? readyDocuments : readyDocuments.slice(0, 4)).map((doc) => {
                  const isExpanded = expandedSummaries.has(doc.id);
                  const summary = doc.summary as any;
                  
                  return (
                    <Card key={`summary-${doc.id}`} id={`summary-${doc.id}`} className={`border-slate-200 transition-all duration-300 hover:border-indigo-300 hover:shadow-lg hover:shadow-indigo-100 ${!isExpanded ? 'hover:-translate-y-1' : ''} group`}>
                      <CardHeader 
                        className="cursor-pointer hover:bg-gradient-to-r hover:from-indigo-50 hover:to-purple-50 transition-all duration-300 p-4 sm:p-6 relative overflow-hidden"
                        onClick={() => toggleSummaryExpansion(doc.id)}
                      >
                        {/* Subtle animated background on hover */}
                        <div className="absolute inset-0 bg-gradient-to-r from-indigo-500/5 to-purple-500/5 opacity-0 group-hover:opacity-100 transition-opacity duration-300" />
                        
                        <div className="flex flex-col space-y-3 sm:flex-row sm:items-center sm:justify-between sm:space-y-0 relative z-10">
                          <div className="flex items-center space-x-3 min-w-0 flex-1">
                            <div className="w-8 h-8 sm:w-10 sm:h-10 bg-gradient-to-br from-indigo-500 to-purple-600 rounded-lg flex items-center justify-center flex-shrink-0 star-container group-hover:scale-110 transition-transform duration-300">
                              <Sparkles className="w-4 h-4 sm:w-5 sm:h-5 text-white star-shine" />
                            </div>
                            <div className="min-w-0 flex-1">
                              <CardTitle className="text-base sm:text-lg font-semibold text-gray-900 truncate group-hover:text-indigo-700 transition-colors duration-300">
                                📄 {doc.filename} - AI Analysis
                              </CardTitle>
                              {summary?.quick_summary && (
                                <p className="text-xs sm:text-sm text-gray-600 mt-1 line-clamp-2 sm:line-clamp-1 group-hover:text-gray-700 transition-colors duration-300">
                                  {summary.quick_summary}
                                </p>
                              )}
                              {!isExpanded && (
                                <p className="text-xs text-indigo-600 mt-2 opacity-0 group-hover:opacity-100 transition-all duration-300 transform translate-y-1 group-hover:translate-y-0 font-medium">
                                  ✨ Click to see AI insights and analysis
                                </p>
                              )}
                            </div>
                          </div>
                          <div className="flex items-center justify-between sm:justify-end space-x-3">
                            <Badge className="bg-indigo-100 text-indigo-800 text-xs group-hover:bg-indigo-200 group-hover:text-indigo-900 transition-colors duration-300">
                              Ready for Chat
                            </Badge>
                            <div className={`flex items-center border border-indigo-200 rounded-lg p-2 transition-all duration-300 group-hover:border-indigo-400 group-hover:bg-indigo-100 ${!isExpanded ? 'group-hover:animate-pulse' : ''}`}>
                              {isExpanded ? (
                                <ChevronUp className="w-4 h-4 sm:w-5 sm:h-5 text-gray-500 group-hover:text-indigo-600 transition-all duration-300 group-hover:scale-110" />
                              ) : (
                                <ChevronDown className="w-4 h-4 sm:w-5 sm:h-5 text-gray-500 group-hover:text-indigo-600 transition-all duration-300 group-hover:scale-110 group-hover:animate-bounce" />
                              )}
                            </div>
                          </div>
                        </div>
                      </CardHeader>
                      
                      {isExpanded && (
                        <CardContent className="pt-0 animate-in slide-in-from-top-2 duration-300 px-4 sm:px-6">
                          <div className="border-t border-slate-200 pt-4">
                            <DocumentSummary 
                              document={doc} 
                              onQuestionClick={(question) => {
                                setActiveTab("chat");
                                setTimeout(() => {
                                  setShouldFocusChat(true);
                                  // Scroll to the AI Document Assistant section
                                  if (chatInterfaceRef.current) {
                                    chatInterfaceRef.current.scrollIntoView({ 
                                      behavior: 'smooth', 
                                      block: 'start',
                                      inline: 'nearest'
                                    });
                                  }
                                }, 150);
                                // Question will be automatically sent via questionToSend prop in ChatInterface
                              }}
                            />
                          </div>
                        </CardContent>
                      )}
                    </Card>
                  );
                })}
                    </div>
                  </CardContent>
                </Card>
              )}

              {/* Upload Section */}
              {!currentDocument && (
                <Card>
                  <CardHeader className="p-4 sm:p-6">
                    <CardTitle className="flex items-center text-base sm:text-lg">
                      <Upload className="w-4 h-4 sm:w-5 sm:h-5 mr-2" />
                      Upload Documents
                    </CardTitle>
                  </CardHeader>
                  <CardContent className="p-4 sm:p-6 pt-0">
                    <FileUpload 
                      onUploadSuccess={handleUploadSuccess}
                      projectId={parseInt(projectId!)}
                    />
                  </CardContent>
                </Card>
              )}

              {/* Processing State */}
              {currentDocument && ['uploading', 'parsing', 'indexing'].includes(currentDocument.status) && (
                <Card>
                  <CardContent className="p-6 sm:p-8 text-center">
                    <div className="mb-4">
                      <div className="w-12 h-12 sm:w-16 sm:h-16 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-4">
                        <FileText className="w-6 h-6 sm:w-8 sm:h-8 text-blue-600" />
                      </div>
                      <h3 className="text-base sm:text-lg font-medium text-slate-700 mb-2">Processing Document</h3>
                      <p className="text-sm sm:text-base text-slate-500">
                        {currentDocument.status === 'uploading' && 'Uploading your document...'}
                        {currentDocument.status === 'parsing' && 'Parsing document content...'}
                        {currentDocument.status === 'indexing' && 'Creating searchable index...'}
                      </p>
                      <p className="text-xs text-slate-400 mt-2">
                        Once complete, you'll be able to chat with this document!
                      </p>
                    </div>
                  </CardContent>
                </Card>
              )}

              {/* Error State */}
              {currentDocument && currentDocument.status === 'error' && (
                <Alert className="border-red-200 bg-red-50">
                  <AlertTriangle className="h-4 w-4 text-red-600" />
                  <AlertDescription className="text-red-700">
                    <strong>Processing Error:</strong> {currentDocument.errorMessage || 'An error occurred while processing your document.'}
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={handleNewUpload}
                      className="ml-4"
                    >
                      Try Again
                    </Button>
                  </AlertDescription>
                </Alert>
              )}

              {/* Documents List */}
              {documents.length > 0 && (
                <Card>
                  <CardHeader className="p-4 sm:p-6">
                    <CardTitle className="text-base sm:text-lg">Project Documents</CardTitle>
                  </CardHeader>
                  <CardContent className="p-4 sm:p-6 pt-0">
                    <div className="space-y-3 sm:space-y-4">
                      {documents.map((doc) => (
                        <div
                          key={doc.id}
                          className="flex flex-col sm:flex-row sm:items-center sm:justify-between p-3 sm:p-4 border rounded-lg hover:bg-gray-50 space-y-3 sm:space-y-0"
                        >
                          <div className="flex items-center space-x-3 sm:space-x-4 min-w-0 flex-1">
                            <FileText className="w-6 h-6 sm:w-8 sm:h-8 text-blue-600 flex-shrink-0" />
                            <div className="min-w-0 flex-1">
                              <h4 className="font-medium text-gray-900 text-sm sm:text-base truncate">{doc.filename}</h4>
                              <div className="flex flex-col sm:flex-row sm:items-center sm:space-x-4 text-xs sm:text-sm text-gray-500 space-y-1 sm:space-y-0">
                                <span>{formatBytes(doc.filesize)}</span>
                                <span className="hidden sm:inline">•</span>
                                <span>{doc.contentType}</span>
                                <span className="hidden sm:inline">•</span>
                                <span>Uploaded {formatDistanceToNow(new Date(doc.createdAt))} ago</span>
                              </div>
                            </div>
                          </div>
                          <div className="flex items-center justify-between sm:justify-end space-x-3 flex-shrink-0">
                            {getStatusBadge(doc.status)}
                            {doc.status === 'ready' && doc.pageCount && (
                              <span className="text-xs sm:text-sm text-gray-500">
                                {doc.pageCount} pages
                              </span>
                            )}
                            {doc.status === 'ready' && doc.wordCount && (
                              <span className="text-xs sm:text-sm text-gray-500">
                                {doc.wordCount.toLocaleString()} words
                              </span>
                            )}
                            <Button
                              variant="ghost"
                              size="sm"
                              onClick={() => handleDeleteDocument(doc)}
                              className="text-red-600 hover:text-red-700 hover:bg-red-50 p-2"
                              disabled={deleteDocumentMutation.isPending}
                            >
                              <Trash2 className="w-4 h-4" />
                            </Button>
                          </div>
                        </div>
                      ))}
                    </div>
                  </CardContent>
                </Card>
              )}
            </div>
          </TabsContent>

          <TabsContent value="chat">
            {readyDocuments.length > 0 ? (
              <div className="space-y-4">


                {/* Quick Summary Section */}
                {(() => {
                  // Filter documents that have completed AI summary processing
                  const documentsWithSummary = readyDocuments.filter(doc => {
                    const summary = doc.summary as any;
                    return summary?.quick_summary;
                  });
                  
                  // Sort by creation date (newest first) and get the latest or all documents
                  const sortedDocuments = documentsWithSummary.sort((a, b) => 
                    new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime()
                  );
                  const displayDocuments = showAllDocs ? sortedDocuments : sortedDocuments.slice(0, 1);
                  
                  return documentsWithSummary.length > 0 && (
                    <Card className="border-emerald-200 bg-emerald-50">
                      <CardHeader className="pb-3">
                        <div className="flex items-center justify-between">
                          <CardTitle className="flex items-center text-lg">
                            <CheckCircle className="w-5 h-5 text-emerald-600 mr-2" />
                            Available Documents
                            <Badge variant="secondary" className="ml-2 text-xs bg-emerald-100 text-emerald-800">
                              {documentsWithSummary.length} Ready
                            </Badge>
                          </CardTitle>
                          {documentsWithSummary.length > 1 && (
                            <Button
                              variant="ghost"
                              size="sm"
                              onClick={() => setShowAllDocs(!showAllDocs)}
                              className="text-emerald-600 hover:text-emerald-700 hover:bg-emerald-100 text-xs"
                            >
                              {showAllDocs ? (
                                <>
                                  <ChevronDown className="w-3 h-3 mr-1" />
                                  Show Less
                                </>
                              ) : (
                                <>
                                  <ChevronRight className="w-3 h-3 mr-1" />
                                  See All ({documentsWithSummary.length})
                                </>
                              )}
                            </Button>
                          )}
                        </div>
                      </CardHeader>
                      <CardContent className="pt-0">
                        <div className={`space-y-3 ${showAllDocs && documentsWithSummary.length > 4 ? 'max-h-80 overflow-y-auto pr-2' : ''}`}>
                          {displayDocuments.map((doc) => {
                            const summary = doc.summary as any;
                            
                            return (
                              <div key={doc.id} className="bg-white rounded-lg p-4 border border-emerald-200">
                                <div className="flex items-start space-x-3">
                                  <FileText className="w-5 h-5 text-emerald-600 mt-0.5 flex-shrink-0" />
                                  <div className="flex-1">
                                    <h4 className="font-medium text-gray-900 text-sm mb-1">
                                      {doc.filename}
                                    </h4>
                                    <p className="text-gray-700 text-sm leading-relaxed mb-3">
                                      {summary.quick_summary}
                                    </p>
                                    <div className="flex items-center space-x-2">
                                      <Button
                                        size="sm"
                                        variant="outline"
                                        onClick={() => {
                                          setActiveTab("documents");
                                          // Expand the document summary
                                          setExpandedSummaries(prev => {
                                            const newSet = new Set(prev);
                                            newSet.add(doc.id);
                                            return newSet;
                                          });
                                          // Scroll to the summary after a brief delay
                                          setTimeout(() => {
                                            const element = document.getElementById(`summary-${doc.id}`);
                                            if (element) {
                                              element.scrollIntoView({ behavior: 'smooth', block: 'start' });
                                            }
                                          }, 100);
                                        }}
                                        className="group text-emerald-600 border-emerald-200 hover:bg-emerald-50 hover:border-emerald-300 text-xs btn-animated-border z-10"
                                      >
                                        <Sparkles className="w-3 h-3 mr-1 group-hover:text-emerald-700" />
                                        View Insights
                                      </Button>
                                      <Badge variant="outline" className="text-xs text-gray-600 border-gray-300">
                                        {formatBytes(doc.filesize)}
                                      </Badge>
                                    </div>
                                  </div>
                                </div>
                              </div>
                            );
                          })}
                        </div>
                      </CardContent>
                    </Card>
                  );
                })()}
                
                <ChatInterface 
                  project={project} 
                  hasDocuments={documents.length > 0} 
                  shouldFocus={shouldFocusChat}
                  onFocused={handleChatFocused}
                  documents={readyDocuments}
                  onNavigateToApiIntegration={() => {
                    setActiveTab("embed");
                    // Smooth scroll to the API Integration section after a small delay
                    setTimeout(() => {
                      const apiSection = document.querySelector('[data-api-integration]') as HTMLElement;
                      if (apiSection) {
                        apiSection.scrollIntoView({ behavior: 'smooth', block: 'start' });
                      }
                    }, 100);
                  }}
                  ref={chatInterfaceRef}
                />
              </div>
            ) : (
              <Card>
                <CardContent className="p-12 text-center">
                  <div className="w-20 h-20 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-6">
                    <MessageSquare className="w-10 h-10 text-gray-400" />
                  </div>
                  <h3 className="text-xl font-semibold text-gray-700 mb-2">Chat Available at Step 4</h3>
                  <p className="text-gray-500 mb-6">
                    Documents must complete all 4 pipeline steps (Upload → Parse → Index → Query) before AI chat becomes available.
                    {documents.length > 0 && (
                      <>
                        <br />
                        <strong className="text-gray-700">
                          {documents.filter(doc => doc.status === 'ready').length} of {documents.length} documents completed
                        </strong>
                      </>
                    )}
                  </p>
                  <Button 
                    onClick={() => setActiveTab("documents")}
                    className="bg-gradient-to-r from-indigo-600 to-purple-600"
                  >
                    <Upload className="w-4 h-4 mr-2" />
                    {documents.length === 0 ? 'Upload Documents' : 'View Documents'}
                  </Button>
                </CardContent>
              </Card>
            )}
          </TabsContent>

          <TabsContent value="embed">
            <EmbedSecurityManager projectId={project.id} />
          </TabsContent>
        </Tabs>
      </main>

      {/* Move AlertDialog here, outside of main/tabs/cards */}
      <AlertDialog open={!!documentToDelete} onOpenChange={() => setDocumentToDelete(null)}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>Delete Document</AlertDialogTitle>
            <AlertDialogDescription>
              Are you sure you want to delete "{documentToDelete?.filename}"? This will remove the document and all its embeddings from the vector database. This action cannot be undone.
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter className="flex-col sm:flex-row space-y-2 sm:space-y-0">
            <AlertDialogCancel className="w-full sm:w-auto">Cancel</AlertDialogCancel>
            <AlertDialogAction
              onClick={confirmDeleteDocument}
              className="bg-red-600 hover:bg-red-700 w-full sm:w-auto"
              disabled={deleteDocumentMutation.isPending}
            >
              {deleteDocumentMutation.isPending ? "Deleting..." : "Delete"}
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </div>
  );
}

// Helper function for formatting bytes
function formatBytes(bytes: number): string {
  if (bytes === 0) return '0 Bytes';
  const k = 1024;
  const sizes = ['Bytes', 'KB', 'MB', 'GB'];
  const i = Math.floor(Math.log(bytes) / Math.log(k));
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
} 