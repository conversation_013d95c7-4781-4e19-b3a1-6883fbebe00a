import { useState, useEffect } from "react";
import { <PERSON>, <PERSON><PERSON>ontent, <PERSON>Header } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Textarea } from "@/components/ui/textarea";
import { useToast } from "@/hooks/use-toast";
import { Send, Bot, Copy, FileText, FolderOpen, AlertCircle } from "lucide-react";
import { formatTimestamp } from "@/lib/utils";
import ReactMarkdown from "react-markdown";

interface EmbedDocument {
  id: number;
  filename: string;
  status: string;
  createdAt: string;
}

interface EmbedProject {
  id: number;
  name: string;
  description: string;
  documentCount: number;
  readyDocumentCount: number;
  createdAt: string;
}

interface ChatMessage {
  id: string;
  question: string;
  response: string;
  timestamp: string;
}

export default function EmbedChat() {
  const [question, setQuestion] = useState("");
  const [messages, setMessages] = useState<ChatMessage[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [isAsking, setIsAsking] = useState(false);
  const [document, setDocument] = useState<EmbedDocument | null>(null);
  const [project, setProject] = useState<EmbedProject | null>(null);
  const [error, setError] = useState<string | null>(null);
  const { toast } = useToast();
  
  // Get parameters from URL
  const urlParams = new URLSearchParams(window.location.search);
  const documentId = parseInt(urlParams.get('documentId') || '0') || null;
  const projectId = parseInt(urlParams.get('projectId') || '0') || null;
  const isMinimal = urlParams.get('minimal') === 'true';
  const theme = urlParams.get('theme') || 'default';
  const hideHeader = urlParams.get('hideHeader') === 'true';
  const token = urlParams.get('token') || null;

  // Fetch data on component mount
  useEffect(() => {
    const fetchData = async () => {
      if (!documentId && !projectId) {
        setError("No document or project ID provided");
        return;
      }

      setIsLoading(true);
      try {
        if (documentId) {
          // Fetch document
          const docResponse = await fetch(`/api/embed/documents/${documentId}`);
          if (!docResponse.ok) {
            throw new Error("Document not found");
          }
          const docData = await docResponse.json();
          setDocument(docData);

          // Note: Chat history is NOT fetched for embeds - each embed session starts fresh
        } else if (projectId) {
          // Fetch project
          const projResponse = await fetch(`/api/embed/projects/${projectId}`);
          if (!projResponse.ok) {
            throw new Error("Project not found");
          }
          const projData = await projResponse.json();
          setProject(projData);

          // Note: Chat history is NOT fetched for embeds - each embed session starts fresh
        }
      } catch (err) {
        setError(err instanceof Error ? err.message : "Failed to load data");
      } finally {
        setIsLoading(false);
      }
    };

    fetchData();
  }, [documentId, projectId]);

  // Handle streaming response
  const handleStreamingResponse = async (questionText: string) => {
    setIsAsking(true);
    
    const tempId = Date.now().toString();
    const newMessage: ChatMessage = {
      id: tempId,
      question: questionText,
      response: "",
      timestamp: new Date().toISOString(),
    };
    
    setMessages(prev => [...prev, newMessage]);
    
    try {
      const requestBody: any = {
        question: questionText,
      };
      
      // Only include non-null IDs
      if (documentId !== null) {
        requestBody.documentId = documentId;
      }
      if (projectId !== null) {
        requestBody.projectId = projectId;
      }
      
      // Include token if provided
      if (token) {
        requestBody.token = token;
      }

      const response = await fetch('/api/embed/chat/ask', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(requestBody),
      });

      if (!response.ok) {
        // Handle specific error responses
        if (response.status === 403) {
          const errorData = await response.json();
          throw new Error(errorData.message || 'Access denied');
        }
        throw new Error('Failed to get response');
      }

      const reader = response.body?.getReader();
      const decoder = new TextDecoder();
      let fullResponse = "";

      if (reader) {
        while (true) {
          const { done, value } = await reader.read();
          if (done) break;

          const chunk = decoder.decode(value);
          const lines = chunk.split('\n');

          for (const line of lines) {
            if (line.startsWith('data: ')) {
              const data = line.slice(6);
              if (data === '[DONE]') {
                break;
              }
              try {
                const parsed = JSON.parse(data);
                if (parsed.content) {
                  fullResponse += parsed.content;
                  setMessages(prev => 
                    prev.map(msg => 
                      msg.id === tempId 
                        ? { ...msg, response: fullResponse }
                        : msg
                    )
                  );
                }
              } catch (e) {
                // Ignore parsing errors
              }
            }
          }
        }
      }
    } catch (error) {
      console.error('Streaming error:', error);
      const errorMessage = error instanceof Error ? error.message : "Sorry, I encountered an error while processing your question. Please try again.";
      
      setMessages(prev => 
        prev.map(msg => 
          msg.id === tempId 
            ? { ...msg, response: errorMessage }
            : msg
        )
      );
      
      // Show appropriate toast based on error type
      const isAuthError = errorMessage.includes("Embed not authorized") || errorMessage.includes("domain") || errorMessage.includes("whitelist");
      toast({
        title: isAuthError ? "Domain Authorization Error" : "Error",
        description: errorMessage,
        variant: "destructive",
      });
    } finally {
      setIsAsking(false);
    }
  };

  const handleSendMessage = () => {
    if (!question.trim() || isAsking) return;
    const questionText = question.trim();
    setQuestion("");
    handleStreamingResponse(questionText);
  };

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === "Enter" && !e.shiftKey) {
      e.preventDefault();
      handleSendMessage();
    }
  };

  const copyToClipboard = async (text: string) => {
    try {
      await navigator.clipboard.writeText(text);
      toast({
        title: "Copied to clipboard",
        description: "Response has been copied to your clipboard.",
      });
    } catch (error) {
      toast({
        title: "Failed to copy",
        description: "Could not copy to clipboard.",
        variant: "destructive",
      });
    }
  };

  // Theme styles
  const getThemeClasses = () => {
    switch (theme) {
      case 'dark':
        return {
          container: 'bg-gray-900 text-white',
          card: 'bg-gray-800 border-gray-700',
          input: 'bg-gray-700 border-gray-600 text-white',
          button: 'bg-blue-600 hover:bg-blue-700',
        };
      case 'minimal':
        return {
          container: 'bg-white',
          card: 'bg-gray-50 border-gray-200',
          input: 'bg-white border-gray-300',
          button: 'bg-gray-800 hover:bg-gray-900',
        };
      default:
        return {
          container: 'bg-gradient-to-br from-slate-50 to-blue-50',
          card: 'bg-white border-slate-200',
          input: 'bg-white border-slate-200',
          button: 'bg-blue-600 hover:bg-blue-700',
        };
    }
  };

  const themeClasses = getThemeClasses();

  if (isLoading) {
    return (
      <div className={`flex items-center justify-center min-h-screen ${themeClasses.container}`}>
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto mb-4"></div>
          <p className="text-gray-600">Loading...</p>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className={`flex items-center justify-center min-h-screen ${themeClasses.container}`}>
        <div className="text-center">
          <AlertCircle className="w-12 h-12 text-red-500 mx-auto mb-4" />
          <h2 className="text-xl font-semibold text-gray-800 mb-2">Error</h2>
          <p className="text-gray-600">{error}</p>
        </div>
      </div>
    );
  }

  const isReady = (document && document.status === 'ready') || (project && project.readyDocumentCount > 0);

  if (!isReady) {
    return (
      <div className={`flex items-center justify-center min-h-screen ${themeClasses.container}`}>
        <div className="text-center">
          <div className="animate-pulse w-12 h-12 bg-yellow-400 rounded-full mx-auto mb-4 flex items-center justify-center">
            <FileText className="w-6 h-6 text-white" />
          </div>
          <h2 className="text-xl font-semibold text-gray-800 mb-2">Processing...</h2>
          <p className="text-gray-600">
            {document ? `${document.filename} is being processed` : "Project documents are being processed"}. 
            Please check back in a few minutes.
          </p>
        </div>
      </div>
    );
  }

  return (
    <div className={`${isMinimal ? 'p-2' : 'p-6'} ${themeClasses.container} min-h-screen`}>
      <div className="max-w-4xl mx-auto">
        {/* Header - hide if requested */}
        {!hideHeader && !isMinimal && (
          <Card className="border-indigo-200 bg-gradient-to-r from-indigo-50 to-blue-50 mb-6">
            <CardHeader className="pb-4">
              <div className="flex flex-col space-y-4 sm:flex-row sm:items-start sm:justify-between sm:space-y-0">
                <div className="flex items-center space-x-3">
                  <div className="w-10 h-10 sm:w-12 sm:h-12 bg-gradient-to-br from-indigo-600 to-blue-600 rounded-xl flex items-center justify-center animate-sparkle-pulse">
                    {project ? (
                      <FolderOpen className="w-5 h-5 sm:w-7 sm:h-7 text-white animate-sparkle" />
                    ) : (
                      <FileText className="w-5 h-5 sm:w-7 sm:h-7 text-white animate-sparkle" />
                    )}
                  </div>
                  <div className="flex-1 min-w-0">
                    <h1 className="text-lg sm:text-xl font-bold text-slate-800 flex flex-col sm:flex-row sm:items-center">
                      <span className="truncate">
                        {project ? `Chat with ${project.name}` : `Chat with ${document?.filename}`}
                      </span>
                    </h1>
                    <p className="text-sm sm:text-base text-slate-600">
                      {project 
                        ? `Ask questions about the ${project.readyDocumentCount} documents in this project`
                        : "Ask questions about your document content"
                      }
                    </p>
                  </div>
                </div>
              </div>
            </CardHeader>
          </Card>
        )}

        {/* Chat Messages */}
        <div className="space-y-4 mb-6 max-h-96 overflow-y-auto">
          {messages.length === 0 ? (
            <div className="text-center py-8">
              <Bot className="w-12 h-12 text-slate-400 mx-auto mb-4" />
              <p className="text-slate-600">
                {project 
                  ? "Start a fresh conversation about your project documents"
                  : "Start a fresh conversation about your document"
                }
              </p>
            </div>
          ) : (
            messages.map((message) => (
              <div key={message.id} className="space-y-4">
                {/* User Question */}
                <div className="flex justify-end">
                  <Card className={`max-w-2xl bg-blue-600 text-white border-0`}>
                    <CardContent className="p-4">
                      <p className="font-medium">{message.question}</p>
                      <span className="text-xs text-blue-100 mt-2 block">
                        {formatTimestamp(new Date(message.timestamp))}
                      </span>
                    </CardContent>
                  </Card>
                </div>

                {/* AI Response */}
                <div className="flex justify-start">
                  <Card className={`max-w-3xl ${themeClasses.card}`}>
                    <CardContent className="p-4">
                      <div className="flex items-start space-x-3">
                        <div className="w-8 h-8 bg-gradient-to-br from-purple-500 to-blue-600 rounded-full flex items-center justify-center">
                          <Bot className="w-4 h-4 text-white" />
                        </div>
                        <div className="flex-1">
                          <div className="prose prose-slate prose-sm max-w-none text-slate-700">
                            <ReactMarkdown
                              components={{
                                h1: ({ children }) => <h1 className="text-lg font-bold text-slate-800 mb-2">{children}</h1>,
                                h2: ({ children }) => <h2 className="text-base font-semibold text-slate-800 mb-2">{children}</h2>,
                                h3: ({ children }) => <h3 className="text-sm font-medium text-slate-700 mb-1">{children}</h3>,
                                p: ({ children }) => <p className="mb-2 last:mb-0 leading-relaxed text-sm">{children}</p>,
                                strong: ({ children }) => <strong className="font-semibold text-slate-800">{children}</strong>,
                                ul: ({ children }) => <ul className="list-disc list-inside mb-2 space-y-1">{children}</ul>,
                                ol: ({ children }) => <ol className="list-decimal list-inside mb-2 space-y-1">{children}</ol>,
                                li: ({ children }) => <li className="text-slate-700 text-sm">{children}</li>,
                                code: ({ children }) => <code className="bg-slate-100 px-1 py-0.5 rounded text-xs font-mono text-slate-800">{children}</code>,
                              }}
                            >
                              {message.response}
                            </ReactMarkdown>
                          </div>
                          <div className="flex items-center justify-end mt-3">
                            <Button
                              variant="ghost"
                              size="sm"
                              onClick={() => copyToClipboard(message.response)}
                              className="text-slate-500 hover:text-slate-700 h-8 px-2"
                            >
                              <Copy className="w-3 h-3 mr-1" />
                              Copy
                            </Button>
                          </div>
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                </div>
              </div>
            ))
          )}
        </div>

        {/* Input Section */}
        <Card className={`border-0 shadow-lg ${themeClasses.card}`}>
          <CardContent className="p-4">
            <div className="flex space-x-3">
              <div className="flex-1">
                <Textarea
                  value={question}
                  onChange={(e) => setQuestion(e.target.value)}
                  onKeyPress={handleKeyPress}
                  placeholder={project 
                    ? "Ask a question about your project documents..."
                    : "Ask a question about your document..."
                  }
                  className={`min-h-[60px] resize-none focus:border-blue-500 ${themeClasses.input}`}
                  disabled={isAsking}
                />
              </div>
              <div className="flex flex-col justify-end">
                <Button
                  onClick={handleSendMessage}
                  disabled={!question.trim() || isAsking}
                  className={`text-white px-6 py-3 ${themeClasses.button}`}
                >
                  {isAsking ? (
                    <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin" />
                  ) : (
                    <Send className="w-4 h-4" />
                  )}
                </Button>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
}