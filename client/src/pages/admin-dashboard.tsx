import { useState, useEffect } from "react";
import { useLocation } from "wouter";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON><PERSON><PERSON>, CardTit<PERSON> } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Textarea } from "@/components/ui/textarea";
import { useToast } from "@/hooks/use-toast";
import { Shield, Users, Activity, CreditCard, Search, LogOut, AlertTriangle, CheckCircle, XCircle, MessageSquare, FileText, HelpCircle } from "lucide-react";

interface SystemStats {
  timestamp: string;
  uploads: {
    totalUploads: number;
    dailyUploads: number;
    errorRate: number;
  };
  ai: {
    totalRequests: number;
    dailyRequests: number;
    averageResponseTime: number;
  };
}

interface UserInfo {
  user: {
    id: string;
    email: string;
    created_at: string;
  };
  profile: {
    subscriptionStatus: string;
    credits: number;
    creditsRemaining: number;
    totalCreditsUsed: number;
    projectsCreated: number;
    documentsUploaded: number;
    chatMessagesUsed: number;
  };
}

interface SystemHealth {
  timestamp: string;
  database: string;
  llamaCloud: string;
  openRouter: string;
  supabase: string;
}

interface SecurityAlert {
  type: string;
  severity: string;
  message: string;
  timestamp: string;
}

interface UserListItem {
  id: string;
  email: string;
  created_at: string;
  last_sign_in_at: string;
  profile: any;
}

export default function AdminDashboard() {
  const [, setLocation] = useLocation();
  const { toast } = useToast();
  const [activeTab, setActiveTab] = useState("overview");
  const [stats, setStats] = useState<SystemStats | null>(null);
  const [userEmail, setUserEmail] = useState("");
  const [userInfo, setUserInfo] = useState<UserInfo | null>(null);
  const [subscriptionStatus, setSubscriptionStatus] = useState("");
  const [durationMonths, setDurationMonths] = useState("1");
  const [isLoading, setIsLoading] = useState(false);
  const [users, setUsers] = useState<UserListItem[]>([]);
  const [systemHealth, setSystemHealth] = useState<SystemHealth | null>(null);
  const [securityAlerts, setSecurityAlerts] = useState<SecurityAlert[]>([]);
  const [recentActivity, setRecentActivity] = useState<any>(null);
  
  // User Support states
  const [supportUserEmail, setSupportUserEmail] = useState("");
  const [supportUserId, setSupportUserId] = useState("");
  const [userProjects, setUserProjects] = useState<any[]>([]);
  const [selectedProject, setSelectedProject] = useState<any>(null);
  const [chatQuestion, setChatQuestion] = useState("");
  const [chatResponse, setChatResponse] = useState("");
  const [isChatting, setIsChatting] = useState(false);
  
  // Full chat history states
  const [fullChatHistory, setFullChatHistory] = useState<any[]>([]);
  const [showFullChat, setShowFullChat] = useState(false);
  const [isLoadingChat, setIsLoadingChat] = useState(false);
  const [chatPagination, setChatPagination] = useState({ total: 0, limit: 20, offset: 0, hasMore: false });

  const adminKey = sessionStorage.getItem("admin-key");

  useEffect(() => {
    if (!adminKey) {
      setLocation("/admin");
      return;
    }
    loadData();
  }, [adminKey, setLocation]);

  const makeAdminRequest = async (url: string, options: RequestInit = {}) => {
    const response = await fetch(url, {
      ...options,
      headers: {
        ...options.headers,
        "x-admin-key": adminKey!,
        "Content-Type": "application/json",
      },
    });

    if (!response.ok) {
      throw new Error(`HTTP ${response.status}: ${response.statusText}`);
    }

    return response;
  };

  const loadData = async () => {
    try {
      const [statsRes, healthRes, alertsRes, activityRes] = await Promise.all([
        makeAdminRequest("/api/admin/upload-stats"),
        makeAdminRequest("/api/admin/system-health"),
        makeAdminRequest("/api/admin/security-alerts"),
        makeAdminRequest("/api/admin/recent-activity")
      ]);

      const [statsData, healthData, alertsData, activityData] = await Promise.all([
        statsRes.json(),
        healthRes.json(),
        alertsRes.json(),
        activityRes.json()
      ]);

      setStats(statsData);
      setSystemHealth(healthData);
      setSecurityAlerts(alertsData.alerts || []);
      setRecentActivity(activityData);
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to load admin data",
        variant: "destructive",
      });
    }
  };

  const loadUsers = async () => {
    try {
      const response = await makeAdminRequest("/api/admin/users?page=1&limit=50");
      const data = await response.json();
      setUsers(data.users || []);
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to load users",
        variant: "destructive",
      });
    }
  };

  const searchUser = async () => {
    if (!userEmail.trim()) return;

    setIsLoading(true);
    try {
      const response = await makeAdminRequest(`/api/admin/user/${encodeURIComponent(userEmail.trim())}`);
      const data = await response.json();
      setUserInfo(data);
      setSubscriptionStatus(data.profile.subscriptionStatus);
    } catch (error) {
      toast({
        title: "User not found",
        description: "No user found with that email",
        variant: "destructive",
      });
      setUserInfo(null);
    } finally {
      setIsLoading(false);
    }
  };

  const updateSubscription = async () => {
    if (!userEmail.trim() || !subscriptionStatus) return;

    setIsLoading(true);
    try {
      await makeAdminRequest("/api/admin/subscription/update", {
        method: "POST",
        body: JSON.stringify({
          email: userEmail.trim(),
          subscriptionStatus,
          durationMonths: parseInt(durationMonths),
        }),
      });

      toast({
        title: "Subscription updated",
        description: `User subscription updated to ${subscriptionStatus}`,
      });

      await searchUser();
    } catch (error) {
      toast({
        title: "Update failed",
        description: "Failed to update subscription",
        variant: "destructive",
      });
    } finally {
      setIsLoading(false);
    }
  };

  const handleLogout = () => {
    sessionStorage.removeItem("admin-key");
    setLocation("/admin");
  };

  // User Support methods
  const loadUserProjects = async () => {
    if (!supportUserId.trim()) return;

    setIsLoading(true);
    try {
      const response = await makeAdminRequest(`/api/admin/user/${encodeURIComponent(supportUserId.trim())}/projects`);
      const data = await response.json();
      setUserProjects(data.projects || []);
      
      toast({
        title: "Projects loaded",
        description: `Found ${data.projects.length} projects for user`,
      });
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to load user projects",
        variant: "destructive",
      });
      setUserProjects([]);
    } finally {
      setIsLoading(false);
    }
  };

  const loadProjectDetails = async (projectId: number) => {
    if (!supportUserId.trim()) return;

    setIsLoading(true);
    try {
      const response = await makeAdminRequest(`/api/admin/user/${encodeURIComponent(supportUserId.trim())}/projects/${projectId}`);
      const data = await response.json();
      setSelectedProject(data);
      
      toast({
        title: "Project loaded",
        description: `Project "${data.project.name}" loaded successfully`,
      });
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to load project details",
        variant: "destructive",
      });
      setSelectedProject(null);
    } finally {
      setIsLoading(false);
    }
  };

  const chatWithProject = async () => {
    if (!supportUserId.trim() || !selectedProject || !chatQuestion.trim()) return;

    setIsChatting(true);
    setChatResponse("");
    
    try {
      const response = await fetch(`/api/admin/user/${encodeURIComponent(supportUserId.trim())}/projects/${selectedProject.project.id}/chat`, {
        method: "POST",
        headers: {
          "x-admin-key": adminKey!,
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          question: chatQuestion,
        }),
      });

      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }

      const reader = response.body?.getReader();
      if (!reader) throw new Error("No response body");

      let fullResponse = "";
      while (true) {
        const { done, value } = await reader.read();
        if (done) break;

        const text = new TextDecoder().decode(value);
        const lines = text.split('\n');
        
        for (const line of lines) {
          if (line.startsWith('data: ')) {
            try {
              const data = JSON.parse(line.slice(6));
              if (data.type === 'chunk') {
                fullResponse += data.chunk;
                setChatResponse(fullResponse);
              } else if (data.type === 'complete') {
                toast({
                  title: "Debug chat completed",
                  description: "Admin debug session saved",
                });
                // Refresh chat history after admin debug
                if (showFullChat) {
                  loadFullChatHistory();
                }
              } else if (data.type === 'error') {
                throw new Error(data.error);
              }
            } catch (e) {
              // Ignore parsing errors for incomplete JSON
            }
          }
        }
      }
    } catch (error) {
      toast({
        title: "Chat failed",
        description: "Failed to chat with project",
        variant: "destructive",
      });
    } finally {
      setIsChatting(false);
    }
  };

  const loadFullChatHistory = async (offset = 0) => {
    if (!supportUserId.trim() || !selectedProject) return;

    setIsLoadingChat(true);
    try {
      const response = await makeAdminRequest(
        `/api/admin/user/${encodeURIComponent(supportUserId.trim())}/projects/${selectedProject.project.id}/chat-history?limit=20&offset=${offset}`
      );
      const data = await response.json();
      
      if (offset === 0) {
        setFullChatHistory(data.messages);
      } else {
        setFullChatHistory(prev => [...prev, ...data.messages]);
      }
      
      setChatPagination(data.pagination);
      
      toast({
        title: "Chat history loaded",
        description: `Loaded ${data.messages.length} messages`,
      });
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to load chat history",
        variant: "destructive",
      });
    } finally {
      setIsLoadingChat(false);
    }
  };

  const toggleFullChatView = () => {
    if (!showFullChat && selectedProject) {
      loadFullChatHistory();
    }
    setShowFullChat(!showFullChat);
  };

  const getHealthIcon = (status: string) => {
    switch (status) {
      case "healthy":
        return <CheckCircle className="w-4 h-4 text-green-600" />;
      case "error":
        return <XCircle className="w-4 h-4 text-red-600" />;
      default:
        return <AlertTriangle className="w-4 h-4 text-yellow-600" />;
    }
  };

  const getSeverityColor = (severity: string) => {
    switch (severity) {
      case "critical": return "text-red-600 bg-red-50";
      case "warning": return "text-yellow-600 bg-yellow-50";
      case "info": return "text-blue-600 bg-blue-50";
      default: return "text-gray-600 bg-gray-50";
    }
  };

  if (!adminKey) return null;

  return (
    <div className="min-h-screen bg-gray-50 p-4">
      <div className="max-w-7xl mx-auto space-y-6">
        {/* Header */}
        <div className="flex justify-between items-center">
          <div className="flex items-center space-x-3">
            <div className="w-8 h-8 bg-red-100 rounded-full flex items-center justify-center">
              <Shield className="w-4 h-4 text-red-600" />
            </div>
            <h1 className="text-2xl font-bold text-gray-900">Admin Dashboard</h1>
          </div>
          <Button variant="outline" onClick={handleLogout}>
            <LogOut className="w-4 h-4 mr-2" />
            Logout
          </Button>
        </div>

        {/* Navigation Tabs */}
        <div className="flex space-x-2 bg-white p-1 rounded-lg border">
          {[
            { key: "overview", label: "Overview" },
            { key: "users", label: "Users" },
            { key: "support", label: "User Support" },
            { key: "activity", label: "Activity" },
            { key: "security", label: "Security" },
            { key: "health", label: "Health" }
          ].map((tab) => (
            <button
              key={tab.key}
              onClick={() => setActiveTab(tab.key)}
              className={`px-4 py-2 rounded-md text-sm font-medium transition-colors ${
                activeTab === tab.key
                  ? "bg-red-600 text-white"
                  : "text-gray-600 hover:bg-gray-100"
              }`}
            >
              {tab.label}
            </button>
          ))}
        </div>

        {/* Overview Tab */}
        {activeTab === "overview" && (
          <div className="space-y-6">
            {/* System Stats */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center">
                  <Activity className="w-5 h-5 mr-2" />
                  System Statistics
                </CardTitle>
              </CardHeader>
              <CardContent>
                {stats ? (
                  <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                    <div className="bg-blue-50 p-4 rounded-lg">
                      <h3 className="font-medium text-blue-900">Uploads</h3>
                      <p className="text-2xl font-bold text-blue-600">{stats.uploads.totalUploads}</p>
                      <p className="text-sm text-blue-700">Total uploads</p>
                    </div>
                    <div className="bg-green-50 p-4 rounded-lg">
                      <h3 className="font-medium text-green-900">AI Requests</h3>
                      <p className="text-2xl font-bold text-green-600">{stats.ai.totalRequests}</p>
                      <p className="text-sm text-green-700">Total requests</p>
                    </div>
                    <div className="bg-purple-50 p-4 rounded-lg">
                      <h3 className="font-medium text-purple-900">Performance</h3>
                      <p className="text-2xl font-bold text-purple-600">{stats.ai.averageResponseTime}ms</p>
                      <p className="text-sm text-purple-700">Avg response time</p>
                    </div>
                  </div>
                ) : (
                  <p className="text-gray-500">Loading stats...</p>
                )}
              </CardContent>
            </Card>

            {/* Recent Activity Summary */}
            {recentActivity && (
              <Card>
                <CardHeader>
                  <CardTitle>Recent Activity (7 days)</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                    <div className="text-center">
                      <p className="text-2xl font-bold text-blue-600">{recentActivity.activitySummary.newUsers}</p>
                      <p className="text-sm text-gray-600">New Users</p>
                    </div>
                    <div className="text-center">
                      <p className="text-2xl font-bold text-green-600">{recentActivity.activitySummary.newProjects}</p>
                      <p className="text-sm text-gray-600">New Projects</p>
                    </div>
                    <div className="text-center">
                      <p className="text-2xl font-bold text-purple-600">{recentActivity.activitySummary.dailyUploads}</p>
                      <p className="text-sm text-gray-600">Daily Uploads</p>
                    </div>
                    <div className="text-center">
                      <p className="text-2xl font-bold text-orange-600">{recentActivity.activitySummary.dailyAiRequests}</p>
                      <p className="text-sm text-gray-600">Daily AI Requests</p>
                    </div>
                  </div>
                </CardContent>
              </Card>
            )}
          </div>
        )}

        {/* Users Tab */}
        {activeTab === "users" && (
          <div className="space-y-6">
            {/* User Search */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center">
                  <Users className="w-5 h-5 mr-2" />
                  User Search
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="flex space-x-2">
                  <div className="flex-1">
                    <Label htmlFor="userEmail">User Email</Label>
                    <Input
                      id="userEmail"
                      type="email"
                      value={userEmail}
                      onChange={(e) => setUserEmail(e.target.value)}
                      placeholder="Enter user email"
                    />
                  </div>
                  <div className="flex items-end">
                    <Button onClick={searchUser} disabled={isLoading}>
                      <Search className="w-4 h-4 mr-2" />
                      Search
                    </Button>
                  </div>
                </div>

                {userInfo && (
                  <div className="bg-gray-50 p-4 rounded-lg space-y-3">
                    <h3 className="font-medium">User Information</h3>
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
                      <div>
                        <p><strong>Email:</strong> {userInfo.user.email}</p>
                        <p><strong>User ID:</strong> {userInfo.user.id}</p>
                        <p><strong>Created:</strong> {new Date(userInfo.user.created_at).toLocaleDateString()}</p>
                      </div>
                      <div>
                        <p><strong>Subscription:</strong> {userInfo.profile.subscriptionStatus}</p>
                        <p><strong>Credits Remaining:</strong> {userInfo.profile.creditsRemaining}</p>
                        <p><strong>Total Credits Used:</strong> {userInfo.profile.totalCreditsUsed}</p>
                      </div>
                    </div>
                    <div className="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm">
                      <div>
                        <p><strong>Projects:</strong> {userInfo.profile.projectsCreated}</p>
                      </div>
                      <div>
                        <p><strong>Documents:</strong> {userInfo.profile.documentsUploaded}</p>
                      </div>
                      <div>
                        <p><strong>Chat Messages:</strong> {userInfo.profile.chatMessagesUsed}</p>
                      </div>
                    </div>
                  </div>
                )}
              </CardContent>
            </Card>

            {/* Subscription Management */}
            {userInfo && (
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center">
                    <CreditCard className="w-5 h-5 mr-2" />
                    Subscription Management
                  </CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                    <div>
                      <Label htmlFor="subscriptionStatus">Subscription Status</Label>
                      <Select value={subscriptionStatus} onValueChange={setSubscriptionStatus}>
                        <SelectTrigger>
                          <SelectValue placeholder="Select status" />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="free">Free</SelectItem>
                          <SelectItem value="pro">Pro</SelectItem>
                          <SelectItem value="enterprise">Enterprise</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>
                    <div>
                      <Label htmlFor="durationMonths">Duration (Months)</Label>
                      <Select value={durationMonths} onValueChange={setDurationMonths}>
                        <SelectTrigger>
                          <SelectValue />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="1">1 Month</SelectItem>
                          <SelectItem value="3">3 Months</SelectItem>
                          <SelectItem value="6">6 Months</SelectItem>
                          <SelectItem value="12">12 Months</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>
                    <div className="flex items-end">
                      <Button 
                        onClick={updateSubscription} 
                        disabled={isLoading}
                        className="w-full"
                      >
                        Update Subscription
                      </Button>
                    </div>
                  </div>
                </CardContent>
              </Card>
            )}

            {/* All Users List */}
            <Card>
              <CardHeader>
                <CardTitle>All Users</CardTitle>
                <Button onClick={loadUsers} size="sm">Load Users</Button>
              </CardHeader>
              <CardContent>
                {users.length > 0 ? (
                  <div className="space-y-2 max-h-96 overflow-y-auto">
                    {users.map((user) => (
                      <div key={user.id} className="p-3 bg-gray-50 rounded-lg">
                        <div className="flex justify-between items-start mb-2">
                          <div className="flex-1">
                            <p className="font-medium">{user.email}</p>
                            <p className="text-sm text-gray-600">
                              Created: {new Date(user.created_at).toLocaleDateString()}
                            </p>
                            <p className="text-xs text-gray-500 font-mono break-all">
                              ID: {user.id}
                            </p>
                          </div>
                          <div className="text-right text-sm ml-4">
                            <p className="font-medium">{user.profile?.subscriptionStatus || 'N/A'}</p>
                            <p className="text-gray-600">Credits: {user.profile?.creditsRemaining || 0}</p>
                          </div>
                        </div>
                        <div className="flex justify-end">
                          <Button
                            size="sm"
                            variant="outline"
                            onClick={() => {
                              setSupportUserId(user.id);
                              setSupportUserEmail(user.email);
                              setActiveTab("support");
                            }}
                            className="text-xs"
                          >
                            Support Access
                          </Button>
                        </div>
                      </div>
                    ))}
                  </div>
                ) : (
                  <p className="text-gray-500">Click "Load Users" to view all users</p>
                )}
              </CardContent>
            </Card>
          </div>
        )}

        {/* User Support Tab */}
        {activeTab === "support" && (
          <div className="space-y-6">
            {/* User Project Access */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center">
                  <HelpCircle className="w-5 h-5 mr-2" />
                  User Support & Debugging
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <Label htmlFor="supportUserEmail">User Email (for reference)</Label>
                    <Input
                      id="supportUserEmail"
                      value={supportUserEmail}
                      onChange={(e) => setSupportUserEmail(e.target.value)}
                      placeholder="<EMAIL>"
                    />
                  </div>
                  <div>
                    <Label htmlFor="supportUserId">User ID (required)</Label>
                    <div className="flex space-x-2">
                      <Input
                        id="supportUserId"
                        value={supportUserId}
                        onChange={(e) => setSupportUserId(e.target.value)}
                        placeholder="User UUID"
                      />
                      <Button 
                        onClick={loadUserProjects} 
                        disabled={isLoading || !supportUserId.trim()}
                        size="sm"
                      >
                        Load Projects
                      </Button>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* User Projects List */}
            {userProjects.length > 0 && (
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center">
                    <FileText className="w-5 h-5 mr-2" />
                    User Projects ({userProjects.length})
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-2 max-h-64 overflow-y-auto">
                    {userProjects.map((project) => (
                      <div 
                        key={project.id} 
                        className={`p-3 rounded-lg border cursor-pointer transition-colors ${
                          selectedProject?.project.id === project.id 
                            ? 'bg-blue-50 border-blue-200' 
                            : 'bg-gray-50 hover:bg-gray-100'
                        }`}
                        onClick={() => loadProjectDetails(project.id)}
                      >
                        <div className="flex justify-between items-start">
                          <div>
                            <p className="font-medium">{project.name}</p>
                            <p className="text-sm text-gray-600">{project.description || 'No description'}</p>
                            <p className="text-xs text-gray-500">
                              Created: {new Date(project.createdAt).toLocaleDateString()}
                            </p>
                          </div>
                          <div className="text-right text-sm">
                            <p>📄 {project.documentCount} docs</p>
                            <p>✅ {project.readyDocumentCount} ready</p>
                            <p>💬 {project.chatCount} chats</p>
                          </div>
                        </div>
                      </div>
                    ))}
                  </div>
                </CardContent>
              </Card>
            )}

            {/* Project Details & Chat */}
            {selectedProject && (
              <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                {/* Project Details */}
                <Card>
                  <CardHeader>
                    <CardTitle>Project: {selectedProject.project.name}</CardTitle>
                  </CardHeader>
                  <CardContent className="space-y-4">
                    <div>
                      <h4 className="font-medium mb-2">Documents ({selectedProject.documents.length})</h4>
                      <div className="space-y-1 max-h-32 overflow-y-auto">
                        {selectedProject.documents.map((doc: any) => (
                          <div key={doc.id} className="flex justify-between text-sm p-2 bg-gray-50 rounded">
                            <span>{doc.filename}</span>
                            <span className={`px-2 py-1 rounded text-xs ${
                              doc.status === 'ready' ? 'bg-green-100 text-green-700' :
                              doc.status === 'error' ? 'bg-red-100 text-red-700' :
                              'bg-yellow-100 text-yellow-700'
                            }`}>
                              {doc.status}
                            </span>
                          </div>
                        ))}
                      </div>
                    </div>
                    
                    <div>
                      <div className="flex justify-between items-center mb-2">
                        <h4 className="font-medium">Chat Messages ({selectedProject.messages.length})</h4>
                        <Button
                          size="sm"
                          variant="outline"
                          onClick={toggleFullChatView}
                          disabled={isLoadingChat}
                        >
                          {showFullChat ? "Hide Full History" : "View Full History"}
                        </Button>
                      </div>
                      
                      {!showFullChat ? (
                        // Preview - show last 5 messages
                        <div className="space-y-1 max-h-32 overflow-y-auto">
                          {selectedProject.messages.slice(-5).map((msg: any) => (
                            <div key={msg.id} className="text-sm p-2 bg-gray-50 rounded">
                              <p className="font-medium">Q: {msg.question.substring(0, 50)}...</p>
                              <p className="text-gray-600">A: {msg.response.substring(0, 50)}...</p>
                              <p className="text-xs text-gray-500">
                                {new Date(msg.timestamp).toLocaleString()}
                                {msg.isAdminDebug && <span className="ml-2 text-red-600">(Admin Debug)</span>}
                              </p>
                            </div>
                          ))}
                        </div>
                      ) : (
                        // Full chat history
                        <div className="space-y-3 max-h-96 overflow-y-auto">
                          {fullChatHistory.map((msg: any) => (
                            <div key={msg.id} className={`p-3 rounded-lg border ${
                              msg.isAdminDebug ? 'bg-red-50 border-red-200' : 'bg-gray-50 border-gray-200'
                            }`}>
                              <div className="flex justify-between items-start mb-2">
                                <div className="text-xs text-gray-500">
                                  {new Date(msg.timestamp).toLocaleString()}
                                  {msg.isAdminDebug && <span className="ml-2 font-bold text-red-600">[ADMIN DEBUG]</span>}
                                </div>
                                <div className="text-xs text-gray-400">ID: {msg.id}</div>
                              </div>
                              
                              <div className="space-y-2">
                                <div>
                                  <div className="text-xs font-medium text-blue-600 mb-1">QUESTION:</div>
                                  <div className="text-sm bg-white p-2 rounded border">
                                    {msg.question}
                                  </div>
                                </div>
                                
                                <div>
                                  <div className="text-xs font-medium text-green-600 mb-1">RESPONSE:</div>
                                  <div className="text-sm bg-white p-2 rounded border max-h-48 overflow-y-auto">
                                    <pre className="whitespace-pre-wrap font-sans">{msg.response}</pre>
                                  </div>
                                </div>
                                
                                {msg.sourceReferences && (
                                  <div>
                                    <div className="text-xs font-medium text-purple-600 mb-1">SOURCES:</div>
                                    <div className="text-xs bg-white p-2 rounded border">
                                      <pre className="whitespace-pre-wrap">{JSON.stringify(msg.sourceReferences, null, 2)}</pre>
                                    </div>
                                  </div>
                                )}
                              </div>
                            </div>
                          ))}
                          
                          {chatPagination.hasMore && (
                            <div className="text-center">
                              <Button
                                size="sm"
                                variant="outline"
                                onClick={() => loadFullChatHistory(chatPagination.offset + chatPagination.limit)}
                                disabled={isLoadingChat}
                              >
                                {isLoadingChat ? "Loading..." : "Load More Messages"}
                              </Button>
                            </div>
                          )}
                        </div>
                      )}
                    </div>
                  </CardContent>
                </Card>

                {/* Admin Chat Interface */}
                <Card>
                  <CardHeader>
                    <CardTitle className="flex items-center">
                      <MessageSquare className="w-5 h-5 mr-2" />
                      Debug Chat
                    </CardTitle>
                  </CardHeader>
                  <CardContent className="space-y-4">
                    <div>
                      <Label htmlFor="chatQuestion">Ask a question to debug the issue:</Label>
                      <Textarea
                        id="chatQuestion"
                        value={chatQuestion}
                        onChange={(e) => setChatQuestion(e.target.value)}
                        placeholder="What seems to be the issue with this project?"
                        rows={3}
                      />
                    </div>
                    
                    <Button 
                      onClick={chatWithProject}
                      disabled={isChatting || !chatQuestion.trim()}
                      className="w-full"
                    >
                      {isChatting ? "Generating response..." : "Send Debug Question"}
                    </Button>

                    {chatResponse && (
                      <div className="mt-4">
                        <Label>AI Response:</Label>
                        <div className="mt-2 p-3 bg-gray-50 rounded-lg max-h-64 overflow-y-auto">
                          <pre className="whitespace-pre-wrap text-sm">{chatResponse}</pre>
                        </div>
                      </div>
                    )}
                  </CardContent>
                </Card>
              </div>
            )}
          </div>
        )}

        {/* Security Tab */}
        {activeTab === "security" && (
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center">
                <AlertTriangle className="w-5 h-5 mr-2" />
                Security Alerts
              </CardTitle>
            </CardHeader>
            <CardContent>
              {securityAlerts.length > 0 ? (
                <div className="space-y-3">
                  {securityAlerts.map((alert, index) => (
                    <div key={index} className={`p-3 rounded-lg ${getSeverityColor(alert.severity)}`}>
                      <div className="flex justify-between items-start">
                        <div>
                          <p className="font-medium">{alert.type.replace('_', ' ').toUpperCase()}</p>
                          <p className="text-sm">{alert.message}</p>
                        </div>
                        <span className="text-xs">{new Date(alert.timestamp).toLocaleTimeString()}</span>
                      </div>
                    </div>
                  ))}
                </div>
              ) : (
                <p className="text-green-600">No security alerts at this time</p>
              )}
            </CardContent>
          </Card>
        )}

        {/* Health Tab */}
        {activeTab === "health" && (
          <Card>
            <CardHeader>
              <CardTitle>System Health</CardTitle>
            </CardHeader>
            <CardContent>
              {systemHealth ? (
                <div className="space-y-3">
                  {Object.entries(systemHealth).map(([key, value]) => {
                    if (key === 'timestamp') return null;
                    return (
                      <div key={key} className="flex justify-between items-center p-3 bg-gray-50 rounded-lg">
                        <span className="font-medium capitalize">{key.replace(/([A-Z])/g, ' $1')}</span>
                        <div className="flex items-center space-x-2">
                          {getHealthIcon(value as string)}
                          <span className="capitalize">{value as string}</span>
                        </div>
                      </div>
                    );
                  })}
                  <div className="text-xs text-gray-500 mt-4">
                    Last checked: {new Date(systemHealth.timestamp).toLocaleString()}
                  </div>
                </div>
              ) : (
                <p className="text-gray-500">Loading health status...</p>
              )}
            </CardContent>
          </Card>
        )}

        {/* Activity Tab */}
        {activeTab === "activity" && recentActivity && (
          <div className="space-y-6">
            <Card>
              <CardHeader>
                <CardTitle>Recent Users (7 days)</CardTitle>
              </CardHeader>
              <CardContent>
                {recentActivity.recentUsers.length > 0 ? (
                  <div className="space-y-2 max-h-64 overflow-y-auto">
                    {recentActivity.recentUsers.map((user: any) => (
                      <div key={user.id} className="flex justify-between items-center p-2 bg-gray-50 rounded">
                        <span>{user.email}</span>
                        <span className="text-sm text-gray-600">
                          {new Date(user.created_at).toLocaleDateString()}
                        </span>
                      </div>
                    ))}
                  </div>
                ) : (
                  <p className="text-gray-500">No new users in the last 7 days</p>
                )}
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>Recent Projects (7 days)</CardTitle>
              </CardHeader>
              <CardContent>
                {recentActivity.recentProjects.length > 0 ? (
                  <div className="space-y-2 max-h-64 overflow-y-auto">
                    {recentActivity.recentProjects.map((project: any) => (
                      <div key={project.id} className="flex justify-between items-center p-2 bg-gray-50 rounded">
                        <span>{project.name}</span>
                        <span className="text-sm text-gray-600">
                          {new Date(project.created_at).toLocaleDateString()}
                        </span>
                      </div>
                    ))}
                  </div>
                ) : (
                  <p className="text-gray-500">No new projects in the last 7 days</p>
                )}
              </CardContent>
            </Card>
          </div>
        )}
      </div>
    </div>
  );
} 