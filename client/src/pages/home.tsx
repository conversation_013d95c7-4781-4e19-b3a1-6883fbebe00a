import React, { useState, useEffect } from "react";
import { Card, CardContent } from "@/components/ui/card";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { Button } from "@/components/ui/button";
import { FileUpload } from "@/components/file-upload";
import { ProcessingPipeline } from "@/components/processing-pipeline";
import { DocumentSummary } from "@/components/document-summary";
import { ChatInterface } from "@/components/chat-interface";
import { AuthModal } from "@/components/auth-modal";
import { useAuth } from "@/components/auth-provider";
import { useQuery } from "@tanstack/react-query";
import { FileText, AlertTriangle, User, LogOut } from "lucide-react";
import type { Document } from "@shared/schema";

export default function Home() {
  const [currentDocument, setCurrentDocument] = useState<Document | null>(null);
  const [showAuthModal, setShowAuthModal] = useState(false);
  const [questionToSend, setQuestionToSend] = useState<string>("");
  const { user, loading, signOut } = useAuth();

  // Show auth modal if user is not signed in (after loading completes)
  useEffect(() => {
    if (!loading && !user) {
      setShowAuthModal(true);
    }
  }, [user, loading]);

  // Poll document status when processing
  const { data: documentStatus } = useQuery<Document>({
    queryKey: [`/api/documents/${currentDocument?.id}`],
    enabled: !!currentDocument && ['uploading', 'parsing', 'indexing'].includes(currentDocument.status),
    refetchInterval: 1000, // Poll every second for faster updates
  });

  // Update document status from polling
  useEffect(() => {
    if (documentStatus) {
      console.log('Document status updated:', documentStatus.status);
      setCurrentDocument(documentStatus);
    }
  }, [documentStatus]);

  const handleUploadSuccess = (document: Document) => {
    setCurrentDocument(document);
  };

  const handleNewUpload = () => {
    setCurrentDocument(null);
  };

  const handleSignOut = async () => {
    try {
      await signOut();
      setCurrentDocument(null);
      setShowAuthModal(true);
    } catch (error) {
      console.error('Sign out error:', error);
    }
  };

  const getProcessingStep = (status: string) => {
    switch (status) {
      case 'uploading': return 'upload';
      case 'parsing': return 'parsing';
      case 'indexing': return 'indexing';
      case 'ready': return 'ready';
      case 'error': return 'error';
      default: return 'upload';
    }
  };

  const handleQuestionClick = (question: string) => {
    setQuestionToSend(question);
  };

  const handleQuestionSent = () => {
    setQuestionToSend("");
  };

  // Show loading state while checking authentication
  if (loading) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-indigo-50 via-white to-purple-50 flex items-center justify-center">
        <div className="text-center">
          <div className="w-16 h-16 bg-indigo-100 rounded-full flex items-center justify-center mx-auto mb-4">
            <FileText className="w-8 h-8 text-indigo-600 animate-pulse" />
          </div>
          <p className="text-lg text-slate-600">Loading...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-indigo-50 via-white to-purple-50">
      {/* AI-themed Header */}
      <div className="bg-gradient-to-r from-indigo-600 via-purple-600 to-pink-600 text-white">
        <div className="container mx-auto px-4 py-6">
          {/* Navigation Bar */}
          {user && (
            <div className="flex justify-between items-center mb-8">
              <div className="flex items-center space-x-4">
                <div className="flex items-center space-x-3">
                  <div className="w-8 h-8 bg-white/20 rounded-full flex items-center justify-center">
                    <User className="w-4 h-4" />
                  </div>
                  <div className="hidden sm:block">
                    <p className="text-sm text-white/80">Welcome back</p>
                    <p className="text-sm font-medium text-white">{user.email}</p>
                  </div>
                </div>
                
                {/* Beta Badge */}
                <div className="hidden lg:flex items-center space-x-2 bg-orange-500/20 backdrop-blur-sm rounded-full px-3 py-1 border border-orange-400/30">
                  <div className="w-3 h-3 bg-orange-400 rounded-full flex items-center justify-center">
                    <span className="text-orange-900 text-xs font-bold">β</span>
                  </div>
                  <span className="text-orange-100 text-xs font-medium">Beta</span>
                </div>
              </div>
              
              <Button
                onClick={handleSignOut}
                variant="ghost"
                size="sm"
                className="text-white/80 hover:text-white hover:bg-white/10 border border-white/20"
              >
                <LogOut className="w-4 h-4" />
              </Button>
            </div>
          )}
          
          {/* Hero Section */}
          <div className="text-center">
            <div className="flex flex-col sm:flex-row items-center justify-center mb-8">
              <div className="w-16 h-16 sm:w-20 sm:h-20 bg-white/20 rounded-2xl sm:rounded-3xl flex items-center justify-center mb-4 sm:mb-0 sm:mr-6 backdrop-blur-lg border border-white/20">
                <svg className="w-8 h-8 sm:w-10 sm:h-10" fill="currentColor" viewBox="0 0 24 24">
                  <path d="M12 2l3.09 6.26L22 9.27l-5 4.87 1.18 6.88L12 17.77l-6.18 3.25L7 14.14 2 9.27l6.91-1.01L12 2z"/>
                </svg>
              </div>
              <div className="text-center sm:text-left">
                <div className="flex items-center justify-center sm:justify-start gap-3 mb-2">
                  <h1 className="text-4xl sm:text-5xl lg:text-6xl font-bold bg-gradient-to-r from-white to-blue-100 bg-clip-text text-transparent">
                    DocChat AI
                  </h1>
                  {!user && (
                    <div className="lg:hidden flex items-center space-x-2 bg-orange-500/20 backdrop-blur-sm rounded-full px-3 py-1 border border-orange-400/30">
                      <div className="w-3 h-3 bg-orange-400 rounded-full flex items-center justify-center">
                        <span className="text-orange-900 text-xs font-bold">β</span>
                      </div>
                      <span className="text-orange-100 text-xs font-medium">Beta</span>
                    </div>
                  )}
                </div>
                <p className="text-lg sm:text-xl text-white/80">
                  Powered by Advanced RAG Technology
                </p>
              </div>
            </div>
            <p className="text-sm sm:text-base lg:text-xl text-white/90 max-w-4xl mx-auto leading-relaxed mb-6 lg:mb-8 px-2">
              Transform any document into an intelligent conversation partner. Upload PDFs, Word docs, Excel files, images and more. 
              Our AI analyzes your content and answers questions with pinpoint accuracy.
            </p>
            <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4 lg:gap-6 max-w-4xl mx-auto">
              <div className="bg-white/10 backdrop-blur-lg rounded-xl lg:rounded-2xl p-4 lg:p-6 border border-white/20">
                <div className="w-10 h-10 lg:w-12 lg:h-12 bg-white/20 rounded-lg lg:rounded-xl flex items-center justify-center mx-auto mb-3 lg:mb-4">
                  <svg className="w-5 h-5 lg:w-6 lg:h-6" fill="currentColor" viewBox="0 0 20 20">
                    <path d="M4 3a2 2 0 100 4h12a2 2 0 100-4H4z"/>
                    <path fillRule="evenodd" d="M3 8a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm0 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1z" clipRule="evenodd"/>
                  </svg>
                </div>
                <h3 className="text-sm lg:text-base font-semibold mb-2">Universal Support</h3>
                <p className="text-xs lg:text-sm text-white/80">PDF, Word, Excel, PowerPoint, and many other file formats</p>
              </div>
              <div className="bg-white/10 backdrop-blur-lg rounded-xl lg:rounded-2xl p-4 lg:p-6 border border-white/20">
                <div className="w-10 h-10 lg:w-12 lg:h-12 bg-white/20 rounded-lg lg:rounded-xl flex items-center justify-center mx-auto mb-3 lg:mb-4">
                  <svg className="w-5 h-5 lg:w-6 lg:h-6" fill="currentColor" viewBox="0 0 20 20">
                    <path d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"/>
                  </svg>
                </div>
                <h3 className="text-sm lg:text-base font-semibold mb-2">Instant Access</h3>
                <p className="text-xs lg:text-sm text-white/80">Start analyzing documents immediately</p>
              </div>
              <div className="bg-white/10 backdrop-blur-lg rounded-xl lg:rounded-2xl p-4 lg:p-6 border border-white/20 sm:col-span-2 lg:col-span-1">
                <div className="w-10 h-10 lg:w-12 lg:h-12 bg-white/20 rounded-lg lg:rounded-xl flex items-center justify-center mx-auto mb-3 lg:mb-4">
                  <svg className="w-5 h-5 lg:w-6 lg:h-6" fill="currentColor" viewBox="0 0 20 20">
                    <path d="M13 6a3 3 0 11-6 0 3 3 0 016 0zM18 8a2 2 0 11-4 0 2 2 0 014 0zM14 15a4 4 0 00-8 0v3h8v-3z"/>
                  </svg>
                </div>
                <h3 className="text-sm lg:text-base font-semibold mb-2">AI-Powered</h3>
                <p className="text-xs lg:text-sm text-white/80">Advanced language models provide accurate, contextual responses</p>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Main Content */}
      <main className="max-w-6xl mx-auto px-4 sm:px-6 lg:px-8 py-6 lg:py-12">
        
        {/* Processing Pipeline */}
        {currentDocument && (
          <div className="mb-6 lg:mb-8">
            <ProcessingPipeline currentStep={getProcessingStep(currentDocument.status)} />
          </div>
        )}

        {/* Upload Section - Only show for authenticated users */}
        {user && !currentDocument && (
          <Card className="mb-6 lg:mb-8">
            <CardContent className="p-4 sm:p-6 lg:p-8">
              <div className="text-center mb-4 lg:mb-6">
                <h2 className="text-xl lg:text-2xl font-bold text-slate-800 mb-2">
                  Upload Your Document
                </h2>
                <p className="text-sm lg:text-base text-slate-600">
                  Upload PDF or text files to start intelligent document analysis
                </p>
              </div>
              
              <FileUpload onUploadSuccess={handleUploadSuccess} />
            </CardContent>
          </Card>
        )}

        {/* Welcome message for unauthenticated users */}
        {!user && (
          <Card className="mb-6 lg:mb-8">
            <CardContent className="p-4 sm:p-6 lg:p-8 text-center">
              <div className="max-w-2xl mx-auto">
                <h2 className="text-xl lg:text-2xl font-bold text-slate-800 mb-4">
                  Welcome to DocChat AI
                </h2>
                <p className="text-sm lg:text-base text-slate-600 mb-6">
                  Please sign in to start uploading and analyzing your documents with our advanced AI technology.
                </p>
                <Button 
                  onClick={() => setShowAuthModal(true)}
                  className="bg-gradient-to-r from-indigo-600 to-purple-600 hover:from-indigo-700 hover:to-purple-700"
                >
                  Get Started
                </Button>
              </div>
            </CardContent>
          </Card>
        )}

        {/* Document Summary and Chat - Only for authenticated users */}
        {user && currentDocument && currentDocument.status === 'ready' && (
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
            <DocumentSummary 
              document={currentDocument} 
              onQuestionClick={handleQuestionClick}
            />
            <ChatInterface 
              project={{ 
                id: currentDocument.projectId, 
                name: 'Document Chat', 
                description: null, 
                userId: currentDocument.userId, 
                indexId: null, 
                status: 'active', 
                createdAt: new Date(), 
                updatedAt: new Date() 
              }}
              document={currentDocument} 
              questionToSend={questionToSend}
              onQuestionSent={handleQuestionSent}
            />
          </div>
        )}

        {/* Processing States - Only for authenticated users */}
        {user && currentDocument && ['uploading', 'parsing', 'indexing'].includes(currentDocument.status) && (
          <Card className="mb-8">
            <CardContent className="p-8 text-center">
              <div className="mb-4">
                <div className="w-16 h-16 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-4">
                  <FileText className="w-8 h-8 text-blue-600" />
                </div>
                <h3 className="text-lg font-medium text-slate-700 mb-2">Processing Document</h3>
                <p className="text-slate-500">
                  {currentDocument.status === 'uploading' && 'Uploading your document...'}
                  {currentDocument.status === 'parsing' && 'Parsing document content...'}
                  {currentDocument.status === 'indexing' && 'Creating searchable index...'}
                </p>
              </div>
            </CardContent>
          </Card>
        )}

        {/* Error State - Only for authenticated users */}
        {user && currentDocument && currentDocument.status === 'error' && (
          <Alert className="mb-8 border-red-200 bg-red-50">
            <AlertTriangle className="h-4 w-4 text-red-600" />
            <AlertDescription className="text-red-700">
              <strong>Processing Error:</strong> {currentDocument.errorMessage || 'An error occurred while processing your document.'}
              <Button
                variant="outline"
                size="sm"
                onClick={handleNewUpload}
                className="ml-4"
              >
                Try Again
              </Button>
            </AlertDescription>
          </Alert>
        )}

        {/* Upload Another Document button removed */}
      </main>

      {/* Authentication Modal */}
      <AuthModal 
        open={showAuthModal} 
        onOpenChange={(open) => setShowAuthModal(open)} 
      />
    </div>
  );
}