@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap');

@tailwind base;
@tailwind components;
@tailwind utilities;

:root {
  --background: hsl(0, 0%, 100%);
  --foreground: hsl(210, 40%, 8%);
  --muted: hsl(210, 40%, 96%);
  --muted-foreground: hsl(215, 16%, 47%);
  --popover: hsl(0, 0%, 100%);
  --popover-foreground: hsl(210, 40%, 8%);
  --card: hsl(0, 0%, 100%);
  --card-foreground: hsl(210, 40%, 8%);
  --border: hsl(214, 32%, 91%);
  --input: hsl(214, 32%, 91%);
  --primary: hsl(207, 90%, 54%);
  --primary-foreground: hsl(210, 40%, 98%);
  --secondary: hsl(210, 40%, 96%);
  --secondary-foreground: hsl(222, 84%, 5%);
  --accent: hsl(210, 40%, 96%);
  --accent-foreground: hsl(222, 84%, 5%);
  --destructive: hsl(0, 84%, 60%);
  --destructive-foreground: hsl(210, 40%, 98%);
  --ring: hsl(207, 90%, 54%);
  --radius: 0.5rem;
  --chart-1: hsl(12, 76%, 61%);
  --chart-2: hsl(173, 58%, 39%);
  --chart-3: hsl(197, 37%, 24%);
  --chart-4: hsl(43, 74%, 66%);
  --chart-5: hsl(27, 87%, 67%);
}

/* AI-themed animations and effects */
@keyframes float {
  0%, 100% { transform: translate3d(0, 0, 0); }
  50% { transform: translate3d(0, -10px, 0); }
}

@keyframes pulse-glow {
  0%, 100% { 
    box-shadow: 0 0 20px rgba(99, 102, 241, 0.3);
  }
  50% { 
    box-shadow: 0 0 40px rgba(99, 102, 241, 0.6), 0 0 60px rgba(147, 51, 234, 0.4);
  }
}

@keyframes gradient-shift {
  0% { background-position: 0% 50%; }
  50% { background-position: 100% 50%; }
  100% { background-position: 0% 50%; }
}

@keyframes sparkle {
  0%, 100% { 
    opacity: 0; 
    transform: scale3d(0, 0, 1);
  }
  50% { 
    opacity: 1; 
    transform: scale3d(1, 1, 1);
  }
}

@keyframes star-shine {
  0%, 100% { 
    opacity: 1; 
    transform: scale(1);
  }
  50% { 
    opacity: 0.7; 
    transform: scale(1.1);
  }
}

@keyframes star-fade {
  0%, 100% { 
    opacity: 1;
  }
  50% { 
    opacity: 0.5;
  }
}

@keyframes border-move {
  0% {
    offset-distance: 0%;
  }
  100% {
    offset-distance: 100%;
  }
}

.ai-float {
  animation: float 6s ease-in-out infinite;
  will-change: transform;
  transform: translate3d(0, 0, 0); /* Force hardware acceleration */
}

.ai-glow {
  animation: pulse-glow 3s ease-in-out infinite;
  will-change: box-shadow;
}

.ai-gradient {
  background: linear-gradient(-45deg, #667eea, #764ba2, #f093fb, #f5576c);
  background-size: 400% 400%;
  animation: gradient-shift 15s ease infinite;
  will-change: background-position;
}

.ai-sparkle {
  position: relative;
}

.ai-sparkle::before {
  content: '✨';
  position: absolute;
  top: -10px;
  right: -10px;
  animation: sparkle 2s ease-in-out infinite;
  will-change: opacity, transform;
  transform: translate3d(0, 0, 0); /* Force hardware acceleration */
}

/* Border glow animation */
.border-glow {
  position: relative;
  overflow: hidden;
}

.border-glow::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 4px;
  height: 4px;
  background: radial-gradient(circle, #10b981, #34d399, transparent);
  border-radius: 50%;
  animation: border-move 3s linear infinite;
  offset-path: rect(0% auto 100% auto);
  z-index: 1;
  opacity: 0.8;
}

/* Rotating border animation for buttons */
@keyframes rotate-border {
  100% {
    transform: rotate(360deg);
  }
}

.btn-animated-border {
  position: relative;
  overflow: hidden;
  border: 2px solid transparent;
  background: #fff;
}

.btn-animated-border::before {
  content: '';
  position: absolute;
  top: -50%;
  left: -50%;
  width: 200%;
  height: 200%;
  background-image: conic-gradient(
    rgb(16, 185, 129) 20deg, 
    rgb(52, 211, 153) 40deg,
    transparent 120deg
  );
  border-radius: inherit;
  animation: rotate-border 3s linear infinite;
  z-index: -1;
}

.btn-animated-border::after {
  content: '';
  position: absolute;
  inset: 2px;
  background: inherit;
  border-radius: inherit;
  z-index: -1;
}

/* Enhanced card shadows */
.ai-card {
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  box-shadow: 
    0 8px 32px rgba(0, 0, 0, 0.1),
    0 2px 16px rgba(0, 0, 0, 0.05);
  transform: translate3d(0, 0, 0); /* Force hardware acceleration */
  backface-visibility: hidden; /* Prevent flicker */
}

/* Modern card styling with pronounced depth */
.card-enhanced {
  background: linear-gradient(135deg, #ffffff 0%, #fefefe 100%);
  border: 1px solid rgba(0, 0, 0, 0.08);
  box-shadow: 
    0 4px 16px rgba(0, 0, 0, 0.08),
    0 2px 8px rgba(0, 0, 0, 0.06),
    0 1px 3px rgba(0, 0, 0, 0.04);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  transform: translateY(0);
}

.card-enhanced:hover {
  box-shadow: 
    0 12px 32px rgba(0, 0, 0, 0.12),
    0 6px 16px rgba(0, 0, 0, 0.08),
    0 2px 8px rgba(0, 0, 0, 0.06);
  transform: translateY(-2px);
  border-color: rgba(0, 0, 0, 0.12);
}

/* Stats cards with elevated appearance */
.stats-card {
  background: linear-gradient(135deg, #ffffff 0%, #fafbff 100%);
  border: 1px solid rgba(99, 102, 241, 0.12);
  box-shadow: 
    0 6px 20px rgba(99, 102, 241, 0.08),
    0 3px 12px rgba(0, 0, 0, 0.06),
    0 1px 4px rgba(0, 0, 0, 0.04);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  transform: translateY(0);
}

.stats-card:hover {
  box-shadow: 
    0 12px 32px rgba(99, 102, 241, 0.12),
    0 6px 20px rgba(99, 102, 241, 0.08),
    0 3px 12px rgba(0, 0, 0, 0.06);
  transform: translateY(-1px);
  border-color: rgba(99, 102, 241, 0.16);
}

/* Project cards with strong elevation */
.project-card {
  background: linear-gradient(135deg, #ffffff 0%, #fefefe 100%);
  border: 1px solid rgba(0, 0, 0, 0.08);
  box-shadow: 
    0 8px 24px rgba(0, 0, 0, 0.08),
    0 4px 12px rgba(0, 0, 0, 0.06),
    0 2px 6px rgba(0, 0, 0, 0.04);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  transform: translateY(0);
}

.project-card:hover {
  box-shadow: 
    0 16px 40px rgba(0, 0, 0, 0.12),
    0 8px 24px rgba(0, 0, 0, 0.08),
    0 4px 12px rgba(0, 0, 0, 0.06);
  transform: translateY(-3px);
  border-color: rgba(0, 0, 0, 0.12);
}

/* Enhanced card appearance with subtle texture */
.card-texture {
  position: relative;
}

.card-texture::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: 
    radial-gradient(circle at 20% 20%, rgba(255, 255, 255, 0.8) 0%, transparent 50%),
    radial-gradient(circle at 80% 80%, rgba(255, 255, 255, 0.6) 0%, transparent 50%);
  border-radius: inherit;
  pointer-events: none;
  opacity: 0.6;
}

/* Card border glow effect */
.card-glow {
  position: relative;
  border-radius: 0.5rem;
}

.card-glow::after {
  content: '';
  position: absolute;
  top: -1px;
  left: -1px;
  right: -1px;
  bottom: -1px;
  background: linear-gradient(45deg, rgba(99, 102, 241, 0.1), rgba(139, 92, 246, 0.1), rgba(59, 130, 246, 0.1));
  border-radius: inherit;
  z-index: -1;
  opacity: 0;
  transition: opacity 0.3s ease;
}

.stats-card.card-glow::after {
  opacity: 0.5;
}

.project-card.card-glow::after {
  opacity: 0.3;
}

/* Enhanced depth with inset highlight */
.card-inset {
  position: relative;
}

.card-inset::before {
  content: '';
  position: absolute;
  top: 1px;
  left: 1px;
  right: 1px;
  height: 1px;
  background: linear-gradient(90deg, 
    rgba(255, 255, 255, 0.8) 0%, 
    rgba(255, 255, 255, 0.4) 50%, 
    rgba(255, 255, 255, 0.8) 100%
  );
  border-radius: 0.5rem 0.5rem 0 0;
  pointer-events: none;
}

/* Paper-like card effect */
.card-paper {
  background: 
    linear-gradient(135deg, #ffffff 0%, #fefefe 100%),
    radial-gradient(circle at 30% 30%, rgba(0, 0, 0, 0.02) 0%, transparent 50%);
  backdrop-filter: blur(0.5px);
}

/* Floating card effect */
.card-floating {
  position: relative;
  z-index: 1;
}

.card-floating::after {
  content: '';
  position: absolute;
  top: 100%;
  left: 10%;
  right: 10%;
  height: 10px;
  background: radial-gradient(ellipse, rgba(0, 0, 0, 0.1) 0%, transparent 70%);
  filter: blur(4px);
  z-index: -1;
  transition: all 0.3s ease;
}

.card-floating:hover::after {
  height: 15px;
  left: 5%;
  right: 5%;
  filter: blur(6px);
}

/* Card content with better spacing */
.card-content-enhanced {
  background: rgba(255, 255, 255, 0.8);
  backdrop-filter: blur(8px);
}

/* Subtle shine effect for premium feel */
.card-shine {
  position: relative;
  overflow: hidden;
}

.card-shine::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(
    90deg,
    transparent,
    rgba(255, 255, 255, 0.2),
    transparent
  );
  transition: left 0.6s ease;
}

.card-shine:hover::before {
  left: 100%;
}

/* Enhanced card content organization */
.card-header-enhanced {
  padding: 1.25rem 1.5rem 0.75rem 1.5rem;
  border-bottom: 1px solid rgba(0, 0, 0, 0.04);
  background: linear-gradient(180deg, rgba(255, 255, 255, 0.8) 0%, rgba(255, 255, 255, 0.4) 100%);
}

.card-content-organized {
  padding: 1rem 1.5rem 1.5rem 1.5rem;
}

/* Stats card improvements */
.stats-card-content {
  padding: 1.25rem 1.5rem;
}

.stats-card-content .stats-label {
  font-size: 0.75rem;
  font-weight: 500;
  color: rgb(75, 85, 99);
  text-transform: uppercase;
  letter-spacing: 0.025em;
  margin-bottom: 0.25rem;
}

.stats-card-content .stats-value {
  font-size: 1.875rem;
  font-weight: 700;
  color: rgb(17, 24, 39);
  line-height: 1.2;
}

.stats-card-content .stats-icon {
  opacity: 0.8;
  transition: all 0.3s ease;
}

.stats-card:hover .stats-icon {
  opacity: 1;
  transform: scale(1.05);
}

/* Project card improvements */
.project-card-header {
  padding: 1.25rem 1.5rem 0.5rem 1.5rem;
  background: linear-gradient(180deg, rgba(255, 255, 255, 0.9) 0%, rgba(255, 255, 255, 0.5) 100%);
}

.project-card-title {
  font-size: 1.125rem;
  font-weight: 600;
  color: rgb(17, 24, 39);
  line-height: 1.4;
  margin-bottom: 0.75rem;
}

.project-card-description {
  font-size: 0.875rem;
  color: rgb(75, 85, 99);
  line-height: 1.5;
  margin-top: 0.5rem;
}

.project-card-content {
  padding: 0.75rem 1.5rem 1.5rem 1.5rem;
}

.project-meta-section {
  padding: 0.75rem 0;
  border-top: 1px solid rgba(0, 0, 0, 0.06);
  margin-top: 0.75rem;
}

.project-stats-grid {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0.75rem 0;
  border-top: 1px solid rgba(0, 0, 0, 0.04);
  margin-top: 0.75rem;
}

.project-stat-item {
  display: flex;
  align-items: center;
  gap: 0.25rem;
  color: rgb(75, 85, 99);
  font-size: 0.75rem;
  font-weight: 500;
}

.project-stat-item .stat-number {
  color: rgb(17, 24, 39);
  font-weight: 600;
}

.project-actions {
  margin-top: 1rem;
  padding-top: 0.75rem;
  border-top: 1px solid rgba(0, 0, 0, 0.04);
}

/* Enhanced button styling */
.btn-primary-enhanced {
  background: linear-gradient(135deg, #4f46e5 0%, #7c3aed 100%);
  border: none;
  color: white;
  font-weight: 500;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  box-shadow: 0 2px 8px rgba(79, 70, 229, 0.3);
}

.btn-primary-enhanced:hover {
  background: linear-gradient(135deg, #4338ca 0%, #6d28d9 100%);
  box-shadow: 0 4px 16px rgba(79, 70, 229, 0.4);
  transform: translateY(-1px);
}

.btn-outline-enhanced {
  border: 1px solid rgba(0, 0, 0, 0.12);
  color: rgb(75, 85, 99);
  font-weight: 500;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  background: rgba(255, 255, 255, 0.8);
}

.btn-outline-enhanced:hover {
  border-color: rgba(79, 70, 229, 0.3);
  color: rgb(79, 70, 229);
  background: rgba(79, 70, 229, 0.04);
  transform: translateY(-1px);
}

/* Status badge enhancements */
.status-badge-enhanced {
  font-size: 0.6875rem;
  font-weight: 600;
  padding: 0.25rem 0.75rem;
  border-radius: 9999px;
  text-transform: uppercase;
  letter-spacing: 0.025em;
  border: 1px solid;
}

.status-active {
  background: rgba(34, 197, 94, 0.1);
  color: rgb(21, 128, 61);
  border-color: rgba(34, 197, 94, 0.2);
}

.status-inactive {
  background: rgba(156, 163, 175, 0.1);
  color: rgb(75, 85, 99);
  border-color: rgba(156, 163, 175, 0.2);
}

/* Timeline and meta information */
.project-timeline {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  color: rgb(107, 114, 128);
  font-size: 0.8125rem;
  margin-bottom: 0.75rem;
}

.project-timeline-icon {
  opacity: 0.7;
  flex-shrink: 0;
}

/* Ready status indicator */
.ready-indicator {
  display: inline-flex;
  align-items: center;
  gap: 0.25rem;
  padding: 0.125rem 0.5rem;
  background: rgba(251, 146, 60, 0.1);
  color: rgb(194, 65, 12);
  border-radius: 0.375rem;
  font-size: 0.6875rem;
  font-weight: 600;
}

/* Responsive improvements */
@media (max-width: 640px) {
  .stats-card-content {
    padding: 1rem;
  }
  
  .stats-card-content .stats-value {
    font-size: 1.5rem;
  }
  
  .project-card-header,
  .project-card-content {
    padding-left: 1rem;
    padding-right: 1rem;
  }
  
  .project-card-title {
    font-size: 1rem;
  }
}

/* Smooth transitions for interactive elements */
button, a, input, textarea, select {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

/* Specific transitions for UI components */
.transition-transform {
  transition: transform 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.transition-colors {
  transition: color 0.3s cubic-bezier(0.4, 0, 0.2, 1), background-color 0.3s cubic-bezier(0.4, 0, 0.2, 1), border-color 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.transition-opacity {
  transition: opacity 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

/* Custom scrollbar */
::-webkit-scrollbar {
  width: 6px;
}

::-webkit-scrollbar-track {
  background: #f1f5f9;
}

::-webkit-scrollbar-thumb {
  background: linear-gradient(to bottom, #6366f1, #8b5cf6);
  border-radius: 3px;
}

::-webkit-scrollbar-thumb:hover {
  background: linear-gradient(to bottom, #4f46e5, #7c3aed);
}

.dark {
  --background: hsl(222, 84%, 5%);
  --foreground: hsl(210, 40%, 98%);
  --muted: hsl(217, 32%, 17%);
  --muted-foreground: hsl(215, 20%, 65%);
  --popover: hsl(222, 84%, 5%);
  --popover-foreground: hsl(210, 40%, 98%);
  --card: hsl(222, 84%, 5%);
  --card-foreground: hsl(210, 40%, 98%);
  --border: hsl(217, 32%, 17%);
  --input: hsl(217, 32%, 17%);
  --primary: hsl(207, 90%, 54%);
  --primary-foreground: hsl(210, 40%, 98%);
  --secondary: hsl(217, 32%, 17%);
  --secondary-foreground: hsl(210, 40%, 98%);
  --accent: hsl(217, 32%, 17%);
  --accent-foreground: hsl(210, 40%, 98%);
  --destructive: hsl(0, 63%, 31%);
  --destructive-foreground: hsl(210, 40%, 98%);
  --ring: hsl(207, 90%, 54%);
  --chart-1: hsl(220, 70%, 50%);
  --chart-2: hsl(160, 60%, 45%);
  --chart-3: hsl(30, 80%, 55%);
  --chart-4: hsl(280, 65%, 60%);
  --chart-5: hsl(340, 75%, 55%);
}

@layer base {
  * {
    @apply border-border;
  }

  body {
    background-color: var(--background);
    color: var(--foreground);
    font-family: 'Inter', system-ui, sans-serif;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
    text-rendering: optimizeLegibility;
    font-feature-settings: "kern" 1;
    font-kerning: normal;
    /* transform: translate3d(0, 0, 0); */
  }

  h1, h2, h3, h4, h5, h6 {
    font-family: 'Inter', system-ui, sans-serif;
  }
}

@layer utilities {
  .prose {
    color: var(--foreground);
  }
  
  .prose p {
    margin-bottom: 1rem;
    line-height: 1.6;
  }
  
  .prose ol {
    margin: 1rem 0;
    padding-left: 2rem;
  }
  
  .prose ol li {
    margin-bottom: 0.5rem;
  }
  
  .prose strong {
    font-weight: 600;
    color: var(--foreground);
  }

  /* Mobile-specific improvements */
  @media (max-width: 640px) {
    .prose {
      font-size: 0.875rem;
      line-height: 1.5;
    }
    
    .prose p {
      margin-bottom: 0.75rem;
    }
    
    .prose ol {
      margin: 0.75rem 0;
      padding-left: 1.5rem;
    }
    
    .prose h1, .prose h2, .prose h3, .prose h4 {
      line-height: 1.3;
    }
  }

  /* Line clamping utility */
  .line-clamp-2 {
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
  }

  /* Star animations */
  .star-shine {
    animation: star-shine 2s ease-in-out infinite;
    will-change: opacity, transform;
  }

  .star-container:hover .star-shine {
    animation: star-fade 1.5s ease-in-out infinite;
  }

  /* Touch-friendly interactive elements */
  @media (max-width: 1024px) {
    button, [role="button"], input, textarea, select {
      min-height: 44px;
    }
    
    /* Ensure tap targets are large enough */
    .btn-small {
      min-height: 40px;
      padding: 0.5rem 1rem;
    }
  }

  /* Improved focus states for mobile */
  @media (max-width: 640px) {
    button:focus, input:focus, textarea:focus, select:focus {
      outline: 2px solid var(--primary);
      outline-offset: 2px;
    }
  }
}

body[data-scroll-locked] {
  position: static !important;
  overflow: visible !important;
}
