
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>eader, DialogTitle } from "@/components/ui/dialog";
import { But<PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { 
  Crown, 
  Star, 
  Zap, 
  MessageSquare, 
  FileText, 
  FolderOpen,
  Check,
  Sparkles,
  TrendingUp,
  Shield
} from "lucide-react";

interface SubscriptionModalProps {
  isOpen: boolean;
  onClose: () => void;
  triggerReason?: 'CHAT_LIMIT_REACHED' | 'PROJECT_LIMIT_REACHED' | 'UPLOAD_LIMIT_REACHED' | 'INSUFFICIENT_CREDITS';
  creditsRemaining?: number;
}

export function SubscriptionModal({ 
  isOpen, 
  onClose, 
  triggerReason = 'INSUFFICIENT_CREDITS',
  creditsRemaining = 0 
}: SubscriptionModalProps) {

  const getModalContent = () => {
    switch (triggerReason) {
      case 'CHAT_LIMIT_REACHED':
        return {
          title: "🚀 Unlock Unlimited AI Chat",
          subtitle: "You've reached your free chat limit",
          description: "Contact us to continue your conversation with unlimited AI-powered insights!",
          urgency: "Get personalized Pro access to continue your research"
        };
      case 'PROJECT_LIMIT_REACHED':
        return {
          title: "📁 Create Unlimited Projects", 
          subtitle: "You've reached your credit limit for projects",
          description: "Contact us to organize unlimited documents across multiple projects!",
          urgency: "Scale your document management with custom Pro access"
        };
      case 'UPLOAD_LIMIT_REACHED':
        return {
          title: "📄 Upload Unlimited Documents",
          subtitle: "You've reached your credit limit for uploads", 
          description: "Contact us to analyze unlimited documents with AI-powered insights!",
          urgency: "Process all your documents with personalized Pro setup"
        };
      default:
        return {
          title: "⚡ Supercharge Your Workflow",
          subtitle: `${creditsRemaining} credits remaining`,
          description: "Contact us for unlimited access to all features with Pro!",
          urgency: "Get custom pricing and avoid running out of credits"
        };
    }
  };

  const content = getModalContent();

  const handleContactUs = () => {
    // Do nothing - button disabled for now
  };

  const proFeatures = [
    { icon: MessageSquare, text: "Unlimited AI Chat Messages", highlight: triggerReason === 'CHAT_LIMIT_REACHED' },
    { icon: FolderOpen, text: "Unlimited Projects", highlight: triggerReason === 'PROJECT_LIMIT_REACHED' },
    { icon: FileText, text: "Unlimited Document Uploads", highlight: triggerReason === 'UPLOAD_LIMIT_REACHED' },
    { icon: Zap, text: "Priority Processing Speed", highlight: false },
    { icon: Shield, text: "Advanced Security Features", highlight: false },
    { icon: TrendingUp, text: "Usage Analytics Dashboard", highlight: false },
  ];

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-[95vw] sm:max-w-lg md:max-w-xl lg:max-w-2xl max-h-[90vh] overflow-y-auto">
        <DialogHeader className="text-center space-y-3 sm:space-y-4">
          <div className="mx-auto w-12 h-12 sm:w-16 sm:h-16 bg-gradient-to-br from-purple-500 to-pink-500 rounded-2xl flex items-center justify-center">
            <Crown className="w-6 h-6 sm:w-8 sm:h-8 text-white" />
          </div>
          <div>
            <DialogTitle className="text-lg sm:text-xl md:text-2xl font-bold bg-gradient-to-r from-purple-600 to-pink-600 bg-clip-text text-transparent leading-tight">
              {content.title}
            </DialogTitle>
            <p className="text-sm sm:text-base text-gray-600 mt-2 px-2">{content.subtitle}</p>
          </div>
        </DialogHeader>

        <div className="space-y-4 sm:space-y-6">
          {/* Urgency Message */}
          <div className="bg-gradient-to-r from-purple-50 to-pink-50 border border-purple-200 rounded-lg p-3 sm:p-4">
            <div className="flex items-start space-x-2 sm:items-center">
              <Sparkles className="w-4 h-4 sm:w-5 sm:h-5 text-purple-600 flex-shrink-0 mt-0.5 sm:mt-0" />
              <span className="font-medium text-purple-900 text-sm sm:text-base leading-tight">{content.urgency}</span>
            </div>
            <p className="text-purple-700 mt-2 text-sm sm:text-base leading-relaxed">{content.description}</p>
          </div>

          {/* Contact Card */}
          <Card className="border-2 border-purple-200 bg-gradient-to-br from-white to-purple-50">
            <CardHeader className="text-center pb-3 sm:pb-4">
              <div className="flex flex-col sm:flex-row items-center justify-center space-y-2 sm:space-y-0 sm:space-x-2">
                <div className="flex items-center space-x-2">
                  <Crown className="w-5 h-5 sm:w-6 sm:h-6 text-purple-600" />
                  <CardTitle className="text-lg sm:text-xl font-bold text-purple-900">Pro Plan</CardTitle>
                </div>
                <Badge className="bg-purple-600 text-white text-xs sm:text-sm">Custom Pricing</Badge>
              </div>
              <div className="flex flex-col items-center space-y-2 mt-3">
                <span className="text-xl sm:text-2xl font-bold text-purple-900">Contact for Pricing</span>
                <p className="text-xs sm:text-sm text-purple-600 text-center px-2">Flexible plans • Personal onboarding • Priority support</p>
              </div>
            </CardHeader>
            <CardContent className="pt-0">
              <div className="grid grid-cols-1 sm:grid-cols-2 gap-2 sm:gap-3">
                {proFeatures.map((feature, index) => (
                  <div 
                    key={index} 
                    className={`flex items-center space-x-2 sm:space-x-3 p-2 sm:p-3 rounded-lg transition-all ${
                      feature.highlight 
                        ? 'bg-purple-100 border border-purple-300' 
                        : 'hover:bg-purple-50'
                    }`}
                  >
                    <div className={`w-6 h-6 sm:w-8 sm:h-8 rounded-lg flex items-center justify-center flex-shrink-0 ${
                      feature.highlight 
                        ? 'bg-purple-600 text-white' 
                        : 'bg-purple-100 text-purple-600'
                    }`}>
                      <feature.icon className="w-3 h-3 sm:w-4 sm:h-4" />
                    </div>
                    <span className={`text-xs sm:text-sm font-medium leading-tight ${
                      feature.highlight ? 'text-purple-900' : 'text-gray-700'
                    }`}>
                      {feature.text}
                    </span>
                    {feature.highlight && (
                      <Star className="w-3 h-3 sm:w-4 sm:h-4 text-purple-600 ml-auto flex-shrink-0" />
                    )}
                  </div>
                ))}
              </div>

              {/* CTA Buttons */}
              <div className="flex flex-col space-y-2 sm:space-y-3 mt-4 sm:mt-6">
                <Button 
                  onClick={handleContactUs}
                  className="w-full bg-gradient-to-r from-purple-600 to-pink-600 hover:from-purple-700 hover:to-pink-700 text-white font-semibold py-2.5 sm:py-3 text-sm sm:text-lg"
                >
                  <Crown className="w-4 h-4 sm:w-5 sm:h-5 mr-1.5 sm:mr-2" />
                  <span className="truncate">Contact Us for Pro Access</span>
                </Button>
                
                <Button 
                  variant="ghost" 
                  onClick={onClose}
                  className="w-full text-gray-600 hover:text-gray-700 py-2 sm:py-2.5 text-sm sm:text-base"
                >
                  Maybe Later
                </Button>
              </div>

              {/* Contact Information */}
              <div className="mt-3 sm:mt-4 p-3 bg-purple-50 rounded-lg border border-purple-200">
                <p className="text-xs sm:text-sm text-purple-700 text-center mb-2 font-medium px-2">
                  Get in touch for personalized pricing and setup
                </p>
                <div className="flex flex-col sm:flex-row items-center justify-center space-y-1 sm:space-y-0 sm:space-x-3 text-xs text-purple-600">
                  <div className="flex items-center space-x-1">
                    <Shield className="w-3 h-3 flex-shrink-0" />
                    <span>Custom Plans</span>
                  </div>
                  <div className="flex items-center space-x-1">
                    <Check className="w-3 h-3 flex-shrink-0" />
                    <span>Personal Support</span>
                  </div>
                  <div className="flex items-center space-x-1">
                    <Star className="w-3 h-3 flex-shrink-0" />
                    <span>Fast Setup</span>
                  </div>
                </div>
                <p className="text-xs text-purple-600 text-center mt-2 break-all">
                  📧 <EMAIL>
                </p>
              </div>
            </CardContent>
          </Card>
        </div>
      </DialogContent>
    </Dialog>
  );
} 