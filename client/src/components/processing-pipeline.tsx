import { Card, CardContent, <PERSON>Header, CardTitle } from "@/components/ui/card";
import { cn } from "@/lib/utils";

interface ProcessingPipelineProps {
  currentStep: 'upload' | 'parsing' | 'indexing' | 'ready' | 'error';
}

export function ProcessingPipeline({ currentStep }: ProcessingPipelineProps) {
  const steps = [
    { id: 'upload', label: 'Upload', sublabel: 'PDF/Text', step: 1 },
    { id: 'parsing', label: 'Parse', sublabel: 'Parse', step: 2 },
    { id: 'indexing', label: 'Index', sublabel: 'Vector DB', step: 3 },
    { id: 'ready', label: 'Query', sublabel: 'Chat Ready!', step: 4 },
  ];

  const getStepStatus = (stepId: string) => {
    const stepIndex = steps.findIndex(s => s.id === stepId);
    const currentIndex = steps.findIndex(s => s.id === currentStep);
    
    if (currentStep === 'error') {
      return stepIndex === 0 ? 'completed' : 'inactive';
    }
    
    if (stepIndex < currentIndex) return 'completed';
    if (stepIndex === currentIndex) return 'active';
    return 'inactive';
  };

  return (
    <Card className="border-0 shadow-2xl bg-gradient-to-br from-white to-slate-50 mb-8 overflow-hidden">
      <CardHeader className="bg-gradient-to-r from-slate-800 to-slate-900 text-white p-6">
        <CardTitle className="text-xl font-bold flex items-center">
          <div className="w-8 h-8 bg-white/20 rounded-lg flex items-center justify-center mr-3">
            <svg className="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
              <path d="M3 4a1 1 0 011-1h12a1 1 0 011 1v2a1 1 0 01-1 1H4a1 1 0 01-1-1V4zM3 10a1 1 0 011-1h6a1 1 0 011 1v6a1 1 0 01-1 1H4a1 1 0 01-1-1v-6zM14 9a1 1 0 00-1 1v6a1 1 0 001 1h2a1 1 0 001-1v-6a1 1 0 00-1-1h-2z"/>
            </svg>
          </div>
          AI Processing Pipeline
        </CardTitle>
        <p className="text-slate-300">Watch your document transform through our AI workflow</p>
      </CardHeader>
      <CardContent className="p-8">
        <div className="flex items-center justify-between">
          {steps.map((step, index) => {
            const status = getStepStatus(step.id);
            
            return (
              <div key={step.id} className="flex items-center">
                <div className="flex flex-col items-center space-y-2">
                  <div
                    className={cn(
                      "w-10 h-10 rounded-full flex items-center justify-center text-white font-medium transition-colors",
                      {
                        "bg-blue-600": status === 'completed' && step.id !== 'ready',
                        "bg-green-600": status === 'completed' && step.id === 'ready',
                        "bg-blue-600 animate-pulse": status === 'active' && step.id !== 'ready',
                        "bg-green-600 animate-pulse": status === 'active' && step.id === 'ready',
                        "bg-slate-200 text-slate-500": status === 'inactive',
                      }
                    )}
                  >
                    {status === 'active' && currentStep === 'parsing' ? (
                      <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin" />
                    ) : status === 'active' && currentStep === 'indexing' ? (
                      <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin" />
                    ) : status === 'completed' && step.id === 'ready' ? (
                      <svg className="w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
                        <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                      </svg>
                    ) : (
                      step.step
                    )}
                  </div>
                  <span
                    className={cn("text-sm font-medium", {
                      "text-slate-700": status === 'completed' || status === 'active',
                      "text-slate-500": status === 'inactive',
                    })}
                  >
                    {step.label}
                  </span>
                  <span
                    className={cn("text-xs", {
                      "text-slate-600": status === 'completed' || status === 'active',
                      "text-slate-400": status === 'inactive',
                    })}
                  >
                    {step.sublabel}
                  </span>
                </div>
                {index < steps.length - 1 && (
                  <div className="flex-1 h-px bg-slate-200 mx-4" />
                )}
              </div>
            );
          })}
        </div>
      </CardContent>
    </Card>
  );
}
