import React, { createContext, useContext, useEffect, useState, useCallback } from 'react'
import { User } from '@supabase/supabase-js'
import { supabase } from '@/lib/supabase'
import { setGlobalErrorHandler } from '@/lib/queryClient'

interface AuthContextType {
  user: User | null
  loading: boolean
  signIn: (email: string, password: string) => Promise<void>
  signUp: (email: string, password: string) => Promise<void>
  signInWithGoogle: () => Promise<any>
  signOut: () => Promise<void>
  handleAuthError: (error: any) => boolean
}

const AuthContext = createContext<AuthContextType | undefined>(undefined)

export function useAuth() {
  const context = useContext(AuthContext)
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider')
  }
  return context
}

interface AuthProviderProps {
  children: React.ReactNode
}

export function AuthProvider({ children }: AuthProviderProps) {
  const [user, setUser] = useState<User | null>(null)
  const [loading, setLoading] = useState(true)

  const performSilentLogout = useCallback(async () => {
    try {
      await supabase.auth.signOut({ scope: 'local' });
      setUser(null);
      localStorage.clear();
      sessionStorage.clear();
    } catch (logoutError) {
      console.warn('Silent logout error:', logoutError);
      setUser(null);
      localStorage.clear();
      sessionStorage.clear();
    }
  }, []);

  const handleAuthError = useCallback((error: any): boolean => {
    try {
      // Check if it's an authentication-related error
      const authErrors = [
        'Invalid or expired token',
        'Authentication failed', 
        'No authorization token provided',
        'fetch failed',
        'UND_ERR_CONNECT_TIMEOUT',
        'ConnectTimeoutError'
      ];
      
      const errorMessage = error?.message || error?.toString() || '';
      const isAuthError = authErrors.some(authError => 
        errorMessage.includes(authError) || 
        (error?.cause && error.cause.code === 'UND_ERR_CONNECT_TIMEOUT')
      );
      
      if (isAuthError || error?.status === 401 || error?.status === 403) {
        console.warn('Authentication error detected, logging out silently:', errorMessage);
        // Silent logout without showing error messages
        performSilentLogout();
        return true; // Indicates error was handled
      }
      
      return false; // Not an auth error, let caller handle it
    } catch (handlerError) {
      console.warn('Error in handleAuthError:', handlerError);
      return false;
    }
  }, [performSilentLogout]);

  useEffect(() => {
    // Set up global error handler for React Query
    setGlobalErrorHandler(handleAuthError);
    
    // Get initial session with error handling
    const initializeAuth = async () => {
      try {
        const { data: { session }, error } = await supabase.auth.getSession();
        
        if (error) {
          handleAuthError(error);
          return;
        }
        
        setUser(session?.user ?? null);
      } catch (error) {
        if (!handleAuthError(error)) {
          console.error('Auth initialization error:', error);
        }
      } finally {
        setLoading(false);
      }
    };

    initializeAuth();

    // Listen for auth changes with error handling
    const {
      data: { subscription },
    } = supabase.auth.onAuthStateChange((event, session) => {
      try {
        setUser(session?.user ?? null);
        setLoading(false);
        
        // Handle OAuth callback success
        if (event === 'SIGNED_IN' && session?.user) {
          // Close any open auth modals by dispatching a custom event
          window.dispatchEvent(new CustomEvent('auth-success'));
        }
        
        // Handle sign out event
        if (event === 'SIGNED_OUT') {
          setUser(null);
        }
      } catch (error) {
        handleAuthError(error);
      }
    });

    return () => {
      subscription.unsubscribe();
    }
  }, [handleAuthError]);

  const signIn = useCallback(async (email: string, password: string) => {
    if (!email?.trim() || !password?.trim()) {
      throw new Error('Email and password are required');
    }
    
    try {
      const { error } = await supabase.auth.signInWithPassword({
        email: email.trim(),
        password,
      });
      if (error) throw error;
    } catch (error) {
      if (!handleAuthError(error)) {
        throw error;
      }
    }
  }, [handleAuthError]);

  const signUp = useCallback(async (email: string, password: string) => {
    if (!email?.trim() || !password?.trim()) {
      throw new Error('Email and password are required');
    }
    
    try {
      const { error } = await supabase.auth.signUp({
        email: email.trim(),
        password,
      });
      if (error) throw error;
    } catch (error) {
      if (!handleAuthError(error)) {
        throw error;
      }
    }
  }, [handleAuthError]);

  const signInWithGoogle = useCallback(async () => {
    try {
      const { error } = await supabase.auth.signInWithOAuth({
        provider: 'google',
        options: {
          redirectTo: `${window.location.origin}/dashboard`,
        },
      });
      if (error) throw error;
    } catch (error) {
      if (!handleAuthError(error)) {
        throw error;
      }
    }
  }, [handleAuthError]);

  const signOut = useCallback(async () => {
    try {
      const { error } = await supabase.auth.signOut();
      if (error) throw error;
    } catch (error) {
      if (!handleAuthError(error)) {
        throw error;
      }
    }
  }, [handleAuthError]);

  const value = {
    user,
    loading,
    signIn,
    signUp,
    signInWithGoogle,
    signOut,
    handleAuthError,
  }

  return (
    <AuthContext.Provider value={value}>
      {children}
    </AuthContext.Provider>
  )
}