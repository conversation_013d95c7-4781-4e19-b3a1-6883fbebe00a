import { useState, useRef } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Upload, File, Trash2 } from "lucide-react";
import { useMutation, useQueryClient } from "@tanstack/react-query";
import { apiRequest } from "@/lib/queryClient";
import { useToast } from "@/hooks/use-toast";
import { formatFileSize, getFileIcon, getFileIconColor } from "@/lib/utils";
import type { Document } from "@shared/schema";

interface FileUploadProps {
  onUploadSuccess: (document: Document) => void;
  projectId?: number; // Optional for backwards compatibility
}

export function FileUpload({ onUploadSuccess, projectId }: FileUploadProps) {
  const [selectedFile, setSelectedFile] = useState<File | null>(null);
  const [dragActive, setDragActive] = useState(false);
  const fileInputRef = useRef<HTMLInputElement>(null);
  const { toast } = useToast();
  const queryClient = useQueryClient();

  const uploadMutation = useMutation({
    mutationFn: async (file: File) => {
      const formData = new FormData();
      formData.append('file', file);
      if (projectId) {
        formData.append('projectId', projectId.toString());
      }
      
      const response = await apiRequest('POST', '/api/documents/upload', formData);
      return response.json();
    },
    onSuccess: (document: Document) => {
      toast({
        title: "Upload successful",
        description: `${document.filename} has been uploaded and is being processed.`,
      });
      setSelectedFile(null);
      onUploadSuccess(document);
      
      // Invalidate relevant queries
      queryClient.invalidateQueries({ queryKey: ['/api/documents'] });
      queryClient.invalidateQueries({ queryKey: ["/api/user/profile"] });
      queryClient.invalidateQueries({ queryKey: ["/api/user/limits"] });
      if (projectId) {
        queryClient.invalidateQueries({ queryKey: [`/api/projects/${projectId}/documents`] });
        queryClient.invalidateQueries({ queryKey: [`/api/projects/${projectId}`] });
        queryClient.invalidateQueries({ queryKey: ['/api/dashboard/stats'] });
      }
    },
    onError: (error: Error) => {
      toast({
        title: "Upload failed",
        description: error.message,
        variant: "destructive",
      });
    },
  });

  const handleDrag = (e: React.DragEvent) => {
    e.preventDefault();
    e.stopPropagation();
    if (e.type === "dragenter" || e.type === "dragover") {
      setDragActive(true);
    } else if (e.type === "dragleave") {
      setDragActive(false);
    }
  };

  const handleDrop = (e: React.DragEvent) => {
    e.preventDefault();
    e.stopPropagation();
    setDragActive(false);

    if (e.dataTransfer.files && e.dataTransfer.files[0]) {
      handleFileSelect(e.dataTransfer.files[0]);
    }
  };

  const handleFileSelect = (file: File) => {
    // Document-only file types (no images, videos, GIFs, or compressed files)
    const allowedTypes = [
      // PDF
      'application/pdf',
      // Microsoft Office
      'application/msword',
      'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
      'application/vnd.ms-powerpoint',
      'application/vnd.openxmlformats-officedocument.presentationml.presentation',
      'application/vnd.ms-excel',
      'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
      // Text files
      'text/plain',
      'text/csv',
      'text/html',
      'text/markdown',
      'application/rtf',
      // Other document formats
      'application/xml',
      'text/xml'
    ];
    
    const fileExtension = file.name.toLowerCase().split('.').pop();
    const allowedExtensions = [
      'pdf', 'doc', 'docx', 'ppt', 'pptx', 'xls', 'xlsx',
      'txt', 'csv', 'html', 'htm', 'md', 'rtf',
      'xml'
    ];
    
    if (!allowedTypes.includes(file.type) && !allowedExtensions.includes(fileExtension || '')) {
      toast({
        title: "Unsupported file type",
        description: "Please upload documents only: PDF, Word, PowerPoint, Excel, or text files. Images, videos, GIFs, and compressed files (.zip, .rar, .epub, etc.) are not allowed.",
        variant: "destructive",
      });
      return;
    }

    // Validate file size (10MB limit)
    if (file.size > 10 * 1024 * 1024) {
      toast({
        title: "File too large",
        description: "File size must be less than 10MB.",
        variant: "destructive",
      });
      return;
    }

    setSelectedFile(file);
  };

  const handleFileInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (e.target.files && e.target.files[0]) {
      handleFileSelect(e.target.files[0]);
    }
  };

  const handleUpload = () => {
    if (selectedFile) {
      uploadMutation.mutate(selectedFile);
    }
  };

  const handleRemoveFile = () => {
    setSelectedFile(null);
    if (fileInputRef.current) {
      fileInputRef.current.value = '';
    }
  };

  return (
    <Card className="border shadow-sm bg-gradient-to-br from-white to-indigo-50/30 overflow-hidden">
      <CardHeader className="bg-gradient-to-r from-indigo-500 to-purple-600 text-white p-4">
        <CardTitle className="text-lg font-semibold flex items-center">
          <div className="w-6 h-6 bg-white/20 rounded-lg flex items-center justify-center mr-2">
            <Upload className="w-3 h-3" />
          </div>
          Upload Document
        </CardTitle>
        <p className="text-indigo-100 text-sm">
          AI-powered document analysis
        </p>
        <div className="flex flex-wrap gap-1 mt-2">
          {['PDF', 'Word', 'Excel', 'PowerPoint', 'Text', 'XML'].map((format) => (
            <span key={format} className="px-2 py-0.5 bg-white/20 rounded-full text-xs font-medium">
              {format}
            </span>
          ))}
        </div>
      </CardHeader>
      <CardContent className="p-4">
        {!selectedFile ? (
          <div
            className={`border-2 border-dashed rounded-xl p-6 text-center transition-all duration-300 cursor-pointer group ${
              dragActive
                ? "border-indigo-500 bg-indigo-50 scale-[1.02]"
                : "border-indigo-200 hover:border-indigo-400 hover:bg-indigo-50/50"
            }`}
            onDragEnter={handleDrag}
            onDragLeave={handleDrag}
            onDragOver={handleDrag}
            onDrop={handleDrop}
            onClick={() => fileInputRef.current?.click()}
          >
            <div className="mx-auto w-12 h-12 bg-gradient-to-br from-indigo-500 to-purple-600 rounded-2xl flex items-center justify-center mb-3 group-hover:scale-110 transition-transform duration-300">
              <Upload className="w-6 h-6 text-white" />
            </div>
            <h3 className="text-lg font-semibold text-slate-800 mb-2">Drop your document here</h3>
            <p className="text-slate-600 mb-3 text-sm">or click to browse your files</p>
            <Button className="bg-gradient-to-r from-indigo-600 to-purple-600 hover:from-indigo-700 hover:to-purple-700 text-white text-sm px-4 py-2 rounded-lg shadow-md hover:shadow-lg transition-all duration-300">
              <File className="w-4 h-4 mr-2" />
              Choose Document
            </Button>
            <p className="text-xs text-slate-500 mt-2">Maximum file size: 10MB</p>
            <input
              ref={fileInputRef}
              type="file"
              className="hidden"
              accept=".pdf,.doc,.docx,.ppt,.pptx,.xls,.xlsx,.txt,.csv,.html,.htm,.md,.rtf,.xml"
              onChange={handleFileInputChange}
            />
          </div>
        ) : (
          <div className="space-y-3">
            <div className="bg-slate-50 rounded-lg p-3 flex items-center justify-between">
              <div className="flex items-center space-x-3">
                <div className={`w-8 h-8 bg-red-100 rounded-lg flex items-center justify-center`}>
                  <i className={`${getFileIcon(selectedFile.type)} ${getFileIconColor(selectedFile.type)} text-sm`} />
                </div>
                <div>
                  <p className="font-medium text-slate-800 text-sm">{selectedFile.name}</p>
                  <p className="text-xs text-slate-500">{formatFileSize(selectedFile.size)}</p>
                </div>
              </div>
              <Button
                variant="ghost"
                size="sm"
                onClick={handleRemoveFile}
                className="text-slate-400 hover:text-red-500 h-8 w-8 p-0"
              >
                <Trash2 className="w-3 h-3" />
              </Button>
            </div>
            <Button
              onClick={handleUpload}
              disabled={uploadMutation.isPending}
              className="w-full bg-blue-600 hover:bg-blue-700 text-white text-sm py-2"
            >
              {uploadMutation.isPending ? (
                <>
                  <div className="w-3 h-3 border-2 border-white border-t-transparent rounded-full animate-spin mr-2" />
                  Uploading...
                </>
              ) : (
                <>
                  <Upload className="w-3 h-3 mr-2" />
                  Upload Document
                </>
              )}
            </Button>
          </div>
        )}
      </CardContent>
    </Card>
  );
}
