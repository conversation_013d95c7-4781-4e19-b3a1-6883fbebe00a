import { useState, useEffect } from 'react';

export function DebugInfo() {
  const [debugInfo, setDebugInfo] = useState<any>({});

  useEffect(() => {
    try {
      setDebugInfo({
        supabaseUrl: import.meta.env.VITE_SUPABASE_URL || 'NOT SET',
        supabaseAnonKey: import.meta.env.VITE_SUPABASE_ANON_KEY ? 'SET' : 'NOT SET',
        nodeEnv: import.meta.env.NODE_ENV || 'undefined',
        reactVersion: '18.3.1',
        userAgent: navigator.userAgent.split(' ')[0],
        timestamp: new Date().toISOString(),
        hasDocument: typeof document !== 'undefined',
        hasWindow: typeof window !== 'undefined',
      });
    } catch (error) {
      console.error('Debug info error:', error);
      setDebugInfo({ error: String(error) });
    }
  }, []);

  if (import.meta.env.NODE_ENV !== 'development') {
    return null;
  }

  return (
    <div className="fixed bottom-4 right-4 bg-black text-white text-xs p-3 rounded shadow-lg max-w-sm opacity-75 z-50">
      <div className="font-bold mb-2">Debug Info</div>
      {debugInfo.error ? (
        <div className="text-red-300">Error: {debugInfo.error}</div>
      ) : (
        <>
          <div>Supabase URL: {debugInfo.supabaseUrl}</div>
          <div>Supabase Key: {debugInfo.supabaseAnonKey}</div>
          <div>Node Env: {debugInfo.nodeEnv}</div>
          <div>React: {debugInfo.reactVersion}</div>
          <div>Browser: {debugInfo.userAgent}</div>
          <div>Document: {debugInfo.hasDocument ? 'Yes' : 'No'}</div>
          <div>Window: {debugInfo.hasWindow ? 'Yes' : 'No'}</div>
          <div>Loaded: {debugInfo.timestamp}</div>
        </>
      )}
    </div>
  );
} 