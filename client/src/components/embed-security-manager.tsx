import { useState, useEffect } from 'react';
import { Plus, Trash2, Shield, AlertTriangle, Copy, CheckCircle, Globe, Lock } from 'lucide-react';
import { Tooltip, TooltipContent, TooltipTrigger } from '@/components/ui/tooltip';
import { useToast } from '@/hooks/use-toast';
import { generateEmbedUrl, generateEmbedCode, generateApiUrl, generateApiSample, generateCurlSample } from '@/lib/config';
import { supabase } from '@/lib/supabase';

interface EmbedDomain {
  id: number;
  userId: string;
  projectId: number | null;
  domain: string;
  isActive: boolean;
  createdAt: string;
}

interface EmbedSecurityManagerProps {
  projectId?: number;
  documentId?: number;
}

export function EmbedSecurityManager({ projectId, documentId }: EmbedSecurityManagerProps) {
  const [domains, setDomains] = useState<EmbedDomain[]>([]);
  const [newDomain, setNewDomain] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const [isAddingDomain, setIsAddingDomain] = useState(false);
  const [embedToken, setEmbedToken] = useState<string>('');
  const [showAdvanced, setShowAdvanced] = useState(false);
  const { toast } = useToast();

  useEffect(() => {
    fetchDomains();
  }, [projectId]);

  const fetchDomains = async () => {
    try {
      const { data: { session } } = await supabase.auth.getSession();
      if (!session?.access_token) {
        console.error('No auth session available');
        return;
      }

      const url = projectId 
        ? `/api/embed/domains?projectId=${projectId}`
        : '/api/embed/domains';
      
      const response = await fetch(url, {
        headers: {
          'Authorization': `Bearer ${session.access_token}`
        }
      });
      
      if (response.ok) {
        const data = await response.json();
        setDomains(data);
      } else {
        console.error('Failed to fetch domains:', response.status, response.statusText);
      }
    } catch (error) {
      console.error('Failed to fetch domains:', error);
      toast({
        title: "Error",
        description: "Failed to load embed domains",
        variant: "destructive",
      });
    }
  };

  const addDomain = async () => {
    if (!newDomain.trim()) {
      toast({
        title: "Invalid Domain",
        description: "Please enter a valid domain name",
        variant: "destructive",
      });
      return;
    }

    // Basic domain validation - allow "*" for global whitelist
    const trimmedDomain = newDomain.trim();
    const domainRegex = /^([a-zA-Z0-9-]+\.)+[a-zA-Z]{2,}$/;
    if (trimmedDomain !== "*" && !domainRegex.test(trimmedDomain)) {
      toast({
        title: "Invalid Domain Format",
        description: "Please enter a valid domain (e.g., example.com) or * for all domains",
        variant: "destructive",
      });
      return;
    }

    setIsAddingDomain(true);
    try {
      const { data: { session } } = await supabase.auth.getSession();
      if (!session?.access_token) {
        throw new Error('Not authenticated');
      }

      const response = await fetch('/api/embed/domains', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${session.access_token}`
        },
        body: JSON.stringify({ 
          domain: newDomain.trim(),
          projectId: projectId 
        })
      });

      if (response.ok) {
        const newEmbedDomain = await response.json();
        setDomains([newEmbedDomain, ...domains]);
        setNewDomain('');
        toast({
          title: "Domain Added",
          description: `${newDomain} has been whitelisted for this project's embeds`,
        });
      } else {
        throw new Error('Failed to add domain');
      }
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to add domain. Please try again.",
        variant: "destructive",
      });
    } finally {
      setIsAddingDomain(false);
    }
  };

  const removeDomain = async (domainId: number, domain: string) => {
    try {
      const { data: { session } } = await supabase.auth.getSession();
      if (!session?.access_token) {
        throw new Error('Not authenticated');
      }

      const response = await fetch(`/api/embed/domains/${domainId}`, {
        method: 'DELETE',
        headers: {
          'Authorization': `Bearer ${session.access_token}`
        }
      });

      if (response.ok) {
        setDomains(domains.filter(d => d.id !== domainId));
        toast({
          title: "Domain Removed", 
          description: `${domain} has been removed from this project's whitelist`,
        });
      } else {
        throw new Error('Failed to remove domain');
      }
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to remove domain. Please try again.",
        variant: "destructive",
      });
    }
  };

  const generateEmbedToken = async () => {
    setIsLoading(true);
    try {
      const { data: { session } } = await supabase.auth.getSession();
      if (!session?.access_token) {
        throw new Error('Not authenticated');
      }

      const response = await fetch('/api/embed/token', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${session.access_token}`
        },
        body: JSON.stringify({ projectId, documentId })
      });

      if (response.ok) {
        const data = await response.json();
        setEmbedToken(data.token);
        toast({
          title: "Token Generated",
          description: "Secure embed token created successfully",
        });
      } else {
        throw new Error('Failed to generate token');
      }
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to generate embed token",
        variant: "destructive",
      });
    } finally {
      setIsLoading(false);
    }
  };

  const copyEmbedCode = (useToken: boolean = false) => {
    const iframeCode = generateEmbedCode({
      projectId: projectId || undefined,
      documentId: documentId || undefined,
      token: (useToken && embedToken) ? embedToken : undefined
    });

    navigator.clipboard.writeText(iframeCode);
    toast({
      title: "Embed Code Copied",
      description: useToken ? "Secure embed code copied to clipboard" : "Basic embed code copied to clipboard",
    });
  };

  const copyEmbedUrl = (useToken: boolean = false) => {
    const embedUrl = generateEmbedUrl({
      projectId: projectId || undefined,
      documentId: documentId || undefined,
      token: (useToken && embedToken) ? embedToken : undefined
    });

    // Open URL in new tab
    window.open(embedUrl, '_blank');

    navigator.clipboard.writeText(embedUrl);
    toast({
      title: "URL opened and copied!",
      description: useToken ? "Secure embed URL opened in new tab and copied to clipboard" : "Basic embed URL opened in new tab and copied to clipboard",
    });
  };

  const copyApiUrl = () => {
    const apiUrl = generateApiUrl();
    navigator.clipboard.writeText(apiUrl);
    toast({
      title: "API URL Copied",
      description: "Chat completion API endpoint copied to clipboard",
    });
  };

  const copyApiSample = (useToken: boolean = false) => {
    const apiSample = generateApiSample({
      projectId: projectId || undefined,
      documentId: documentId || undefined,
      token: (useToken && embedToken) ? embedToken : undefined
    });
    navigator.clipboard.writeText(apiSample);
    toast({
      title: "API Code Copied",
      description: useToken ? "Secure API integration code copied to clipboard" : "Basic API integration code copied to clipboard",
    });
  };

  const copyCurlSample = (useToken: boolean = false) => {
    const curlSample = generateCurlSample({
      projectId: projectId || undefined,
      documentId: documentId || undefined,
      token: (useToken && embedToken) ? embedToken : undefined
    });
    navigator.clipboard.writeText(curlSample);
    toast({
      title: "cURL Command Copied",
      description: useToken ? "Secure cURL request copied to clipboard" : "Basic cURL request copied to clipboard",
    });
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center space-x-3">
        <Shield className="w-6 h-6 text-blue-600" />
        <div>
          <h3 className="text-lg font-semibold">Embed Security</h3>
          <p className="text-sm text-gray-600">Control where this project's content can be embedded</p>
        </div>
      </div>

      {/* Security Warning */}
      <div className="bg-amber-50 border border-amber-200 rounded-lg p-4 flex items-start space-x-3">
        <AlertTriangle className="w-5 h-5 text-amber-600 mt-0.5" />
        <div className="text-sm">
          <p className="font-medium text-amber-800">Security Notice</p>
          <p className="text-amber-700 mt-1">
            Without domain restrictions, anyone can copy your embed code and use it on their website. 
            Add trusted domains below to prevent unauthorized usage of this project's content.
          </p>
        </div>
      </div>

      {/* Domain Management */}
      <div className="border rounded-lg p-4 space-y-4">
        <div className="flex items-center justify-between">
          <h4 className="font-medium flex items-center space-x-2">
            <Globe className="w-4 h-4" />
            <span>Whitelisted Domains</span>
          </h4>
          <span className="text-sm text-gray-500">{domains.length} domains</span>
        </div>

        {/* Add Domain */}
        <div className="flex space-x-2">
          <input
            type="text"
            value={newDomain}
            onChange={(e) => setNewDomain(e.target.value)}
            placeholder="example.com or * for all domains"
            className="flex-1 px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
            onKeyPress={(e) => e.key === 'Enter' && addDomain()}
          />
          <button
            onClick={addDomain}
            disabled={isAddingDomain}
            className="bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700 disabled:opacity-50 flex items-center space-x-1"
          >
            <Plus className="w-4 h-4" />
            <span>{isAddingDomain ? 'Adding...' : 'Add'}</span>
          </button>
        </div>

        {/* Domain List */}
        <div className="space-y-2">
          {domains.length === 0 ? (
            <div className="text-center py-8 text-gray-500">
              <Globe className="w-8 h-8 mx-auto mb-2 opacity-50" />
              <p>No domains whitelisted</p>
              <p className="text-sm">Add domains to restrict embed usage</p>
            </div>
          ) : (
            domains.map((domain) => (
              <div key={domain.id} className="flex items-center justify-between bg-gray-50 p-3 rounded-md">
                <div className="flex items-center space-x-3">
                  <CheckCircle className="w-4 h-4 text-green-600" />
                  <span className="font-medium">{domain.domain}</span>
                  <span className="text-xs text-gray-500">
                    Added {new Date(domain.createdAt).toLocaleDateString()}
                  </span>
                </div>
                <button
                  onClick={() => removeDomain(domain.id, domain.domain)}
                  className="text-red-600 hover:text-red-800 p-1"
                  title="Remove domain"
                >
                  <Trash2 className="w-4 h-4" />
                </button>
              </div>
            ))
          )}
        </div>
      </div>

      {/* Advanced Security */}
      <div className="border rounded-lg p-4 space-y-4">
        <button
          onClick={() => setShowAdvanced(!showAdvanced)}
          className="flex items-center justify-between w-full text-left"
        >
          <h4 className="font-medium flex items-center space-x-2">
            <Lock className="w-4 h-4" />
            <span>Advanced Security (Token-based)</span>
          </h4>
          <span className="text-sm text-gray-500">
            {showAdvanced ? 'Hide' : 'Show'}
          </span>
        </button>

        {showAdvanced && (
          <div className="space-y-4 pt-2 border-t">
            <p className="text-sm text-gray-600">
              Generate secure tokens for enhanced protection. Tokens expire after 24 hours and are tied to specific content.
            </p>

            <div className="flex space-x-2">
              <button
                onClick={generateEmbedToken}
                disabled={isLoading}
                className="bg-green-600 text-white px-4 py-2 rounded-md hover:bg-green-700 disabled:opacity-50"
              >
                {isLoading ? 'Generating...' : 'Generate Secure Token'}
              </button>

              {embedToken && (
                <>
                  <Tooltip>
                    <TooltipTrigger asChild>
                      <button
                        onClick={() => copyEmbedCode(true)}
                        className="bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700 flex items-center space-x-2 font-medium transition-all duration-200 shadow-md hover:shadow-lg transform hover:scale-105"
                      >
                        <Copy className="w-4 h-4" />
                        <span>Copy Secure Embed</span>
                      </button>
                    </TooltipTrigger>
                    <TooltipContent side="bottom" className="max-w-md p-3">
                      <div className="space-y-2">
                        <p className="text-sm font-medium">Secure Embed Code:</p>
                        <pre className="text-xs bg-slate-100 p-2 rounded overflow-x-auto whitespace-pre-wrap">
                          {generateEmbedCode({
                            projectId: projectId || undefined,
                            documentId: documentId || undefined,
                            token: embedToken
                          })}
                        </pre>
                      </div>
                    </TooltipContent>
                  </Tooltip>
                  
                  <button
                    onClick={() => copyEmbedUrl(true)}
                    className="bg-purple-600 text-white px-4 py-2 rounded-md hover:bg-purple-700 flex items-center space-x-2 font-medium transition-all duration-200 shadow-md hover:shadow-lg transform hover:scale-105"
                  >
                    <Copy className="w-4 h-4" />
                    <span>Copy Secure URL</span>
                  </button>

                  <Tooltip>
                    <TooltipTrigger asChild>
                      <button
                        onClick={() => copyApiSample(true)}
                        className="bg-green-600 text-white px-4 py-2 rounded-md hover:bg-green-700 flex items-center space-x-2 font-medium transition-all duration-200 shadow-md hover:shadow-lg transform hover:scale-105"
                      >
                        <Copy className="w-4 h-4" />
                        <span>Copy Secure API</span>
                      </button>
                    </TooltipTrigger>
                    <TooltipContent side="bottom" className="max-w-md p-3">
                      <div className="space-y-2">
                        <p className="text-sm font-medium">Secure API Integration:</p>
                        <pre className="text-xs bg-slate-100 p-2 rounded overflow-x-auto whitespace-pre-wrap max-h-32">
                          {generateApiSample({
                            projectId: projectId || undefined,
                            documentId: documentId || undefined,
                            token: embedToken
                          })}
                        </pre>
                      </div>
                    </TooltipContent>
                  </Tooltip>

                  <Tooltip>
                    <TooltipTrigger asChild>
                      <button
                        onClick={() => copyCurlSample(true)}
                        className="bg-orange-600 text-white px-4 py-2 rounded-md hover:bg-orange-700 flex items-center space-x-2 font-medium transition-all duration-200 shadow-md hover:shadow-lg transform hover:scale-105"
                      >
                        <Copy className="w-4 h-4" />
                        <span>Copy Secure cURL</span>
                      </button>
                    </TooltipTrigger>
                    <TooltipContent side="bottom" className="max-w-md p-3">
                      <div className="space-y-2">
                        <p className="text-sm font-medium">Secure cURL Request:</p>
                        <pre className="text-xs bg-slate-100 p-2 rounded overflow-x-auto whitespace-pre-wrap max-h-32">
                          {generateCurlSample({
                            projectId: projectId || undefined,
                            documentId: documentId || undefined,
                            token: embedToken
                          })}
                        </pre>
                      </div>
                    </TooltipContent>
                  </Tooltip>
                </>
              )}
            </div>

            {embedToken && (
              <div className="bg-gray-100 p-3 rounded-md">
                <p className="text-xs text-gray-600 mb-2">Generated Token (expires in 24h):</p>
                <div className="font-mono text-xs bg-white p-2 rounded border break-all">
                  {embedToken}
                </div>
              </div>
            )}
          </div>
        )}
      </div>

      {/* Embed Codes */}
      <div className="border rounded-lg p-4 space-y-4">
        <h4 className="font-medium">Integration Options</h4>
        
        <div className="space-y-4">
          {/* Chat Widget Embed */}
          <div>
            <div className="flex items-center justify-between mb-2">
              <label className="text-sm font-medium text-gray-700">💬 Chat Widget Embed</label>
              <div className="flex space-x-2">
                <Tooltip>
                  <TooltipTrigger asChild>
                    <button
                      onClick={() => copyEmbedCode(false)}
                      className="bg-blue-500 hover:bg-blue-600 text-white px-3 py-1.5 rounded-md text-sm flex items-center space-x-1 font-medium transition-all duration-200 shadow-md hover:shadow-lg transform hover:scale-105"
                    >
                      <Copy className="w-3 h-3" />
                      <span>Copy Embed</span>
                    </button>
                  </TooltipTrigger>
                  <TooltipContent side="bottom" className="max-w-md p-3">
                    <div className="space-y-2">
                      <p className="text-sm font-medium">Basic Embed Code:</p>
                      <pre className="text-xs bg-slate-100 p-2 rounded overflow-x-auto whitespace-pre-wrap">
                        {generateEmbedCode({
                          projectId: projectId || undefined,
                          documentId: documentId || undefined
                        })}
                      </pre>
                    </div>
                  </TooltipContent>
                </Tooltip>
                <button
                  onClick={() => copyEmbedUrl(false)}
                  className="bg-purple-500 hover:bg-purple-600 text-white px-3 py-1.5 rounded-md text-sm flex items-center space-x-1 font-medium transition-all duration-200 shadow-md hover:shadow-lg transform hover:scale-105"
                >
                  <Copy className="w-3 h-3" />
                  <span>Copy URL</span>
                </button>
              </div>
            </div>
            <div className="bg-gray-100 p-3 rounded-md font-mono text-xs overflow-x-auto">
              {generateEmbedCode({
                projectId: projectId || undefined,
                documentId: documentId || undefined
              })}
            </div>
            <p className="text-xs text-gray-500 mt-1">
              ✅ Full UI included &nbsp;&nbsp; ❌ Limited customization
            </p>
          </div>

          {/* API Integration */}
          <div className="border-t pt-4" data-api-integration>
            <div className="flex items-center justify-between mb-2">
              <label className="text-sm font-medium text-gray-700">🔌 API Integration (Custom UI)</label>
              <div className="flex space-x-2">
                <button
                  onClick={copyApiUrl}
                  className="bg-green-500 hover:bg-green-600 text-white px-3 py-1.5 rounded-md text-sm flex items-center space-x-1 font-medium transition-all duration-200 shadow-md hover:shadow-lg transform hover:scale-105"
                >
                  <Copy className="w-3 h-3" />
                  <span>Copy API URL</span>
                </button>
                <Tooltip>
                  <TooltipTrigger asChild>
                    <button
                      onClick={() => copyApiSample(false)}
                      className="bg-indigo-500 hover:bg-indigo-600 text-white px-3 py-1.5 rounded-md text-sm flex items-center space-x-1 font-medium transition-all duration-200 shadow-md hover:shadow-lg transform hover:scale-105"
                    >
                      <Copy className="w-3 h-3" />
                      <span>Copy Code</span>
                    </button>
                  </TooltipTrigger>
                  <TooltipContent side="bottom" className="max-w-md p-3">
                    <div className="space-y-2">
                      <p className="text-sm font-medium">Sample Integration Code:</p>
                      <pre className="text-xs bg-slate-100 p-2 rounded overflow-x-auto whitespace-pre-wrap max-h-40">
                        {generateApiSample({
                          projectId: projectId || undefined,
                          documentId: documentId || undefined
                        })}
                      </pre>
                    </div>
                  </TooltipContent>
                </Tooltip>
                <Tooltip>
                  <TooltipTrigger asChild>
                    <button
                      onClick={() => copyCurlSample(false)}
                      className="bg-orange-500 hover:bg-orange-600 text-white px-3 py-1.5 rounded-md text-sm flex items-center space-x-1 font-medium transition-all duration-200 shadow-md hover:shadow-lg transform hover:scale-105"
                    >
                      <Copy className="w-3 h-3" />
                      <span>Copy cURL</span>
                    </button>
                  </TooltipTrigger>
                  <TooltipContent side="bottom" className="max-w-md p-3">
                    <div className="space-y-2">
                      <p className="text-sm font-medium">cURL Request Example:</p>
                      <pre className="text-xs bg-slate-100 p-2 rounded overflow-x-auto whitespace-pre-wrap max-h-40">
                        {generateCurlSample({
                          projectId: projectId || undefined,
                          documentId: documentId || undefined
                        })}
                      </pre>
                    </div>
                  </TooltipContent>
                </Tooltip>
              </div>
            </div>
            
            {/* API URL */}
            <div className="bg-gray-100 p-3 rounded-md font-mono text-xs overflow-x-auto mb-3">
              {generateApiUrl()}
            </div>
            
            {/* cURL Example */}
            <div className="mt-3">
              <div className="flex items-center justify-between mb-2">
                <label className="text-xs font-medium text-gray-600">cURL Request Example:</label>
              </div>
              <div className="bg-gray-900 p-3 rounded-md font-mono text-xs overflow-x-auto text-green-400">
                {generateCurlSample({
                  projectId: projectId || undefined,
                  documentId: documentId || undefined
                })}
              </div>
            </div>
            
            <p className="text-xs text-gray-500 mt-2">
              ✅ Full customization &nbsp;&nbsp; ✅ Build your own UI &nbsp;&nbsp; ✅ Complete control
            </p>
          </div>
          
          <div className="border-t pt-4">
            <div className="flex items-center justify-between mb-2">
              <label className="text-sm font-medium text-gray-700">🌐 Direct URL (for reference)</label>
              <div className="text-xs text-gray-500">Use this URL directly in browser</div>
            </div>
            <div className="bg-gray-100 p-3 rounded-md font-mono text-xs overflow-x-auto">
              {generateEmbedUrl({
                projectId: projectId || undefined,
                documentId: documentId || undefined
              })}
            </div>
          </div>
        </div>
      </div>

      {/* Usage Tips */}
      <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
        <h4 className="font-medium text-blue-800 mb-2">🛡️ Security Best Practices & Integration Guide</h4>
        <div className="grid md:grid-cols-2 gap-4">
          <div>
            <h5 className="font-medium text-blue-800 mb-1">Security:</h5>
            <ul className="text-sm text-blue-700 space-y-1">
              <li>• Add only trusted domains to your whitelist</li>
              <li>• Use secure tokens for sensitive content</li>
              <li>• Regularly review and clean up old domains</li>
              <li>• Monitor embed usage in your dashboard</li>
            </ul>
          </div>
          <div>
            <h5 className="font-medium text-blue-800 mb-1">Choose Integration:</h5>
            <ul className="text-sm text-blue-700 space-y-1">
              <li>• <strong>Widget Embed:</strong> Quick setup, limited styling</li>
              <li>• <strong>API Integration:</strong> Full control, custom UI</li>
              <li>• <strong>Direct URL:</strong> Standalone chat page</li>
              <li>• API supports streaming responses (SSE)</li>
            </ul>
          </div>
        </div>
      </div>
    </div>
  );
} 