import { useState, useRef, useEffect, forwardRef } from "react";
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Textarea } from "@/components/ui/textarea";
import { Tooltip, TooltipContent, TooltipTrigger } from "@/components/ui/tooltip";
import { useQuery, useQueryClient } from "@tanstack/react-query";
import { useToast } from "@/hooks/use-toast";
import { Send, ThumbsUp, Copy, Bot, ExternalLink, Code, MessageCircle, ChevronRight } from "lucide-react";
import { formatTimestamp } from "@/lib/utils";
import { generateEmbedUrl, generateEmbedCode, generateApiUrl, generateApiSample } from "@/lib/config";
import ReactMarkdown from "react-markdown";
import { supabase } from "@/lib/supabase";
import { SubscriptionModal } from "@/components/subscription-modal";
import type { ChatMessage, Document, Project } from "@shared/schema";

interface ChatInterfaceProps {
  project: Project;
  document?: Document; // Optional for backwards compatibility
  hasDocuments?: boolean; // Whether the project has any documents
  questionToSend?: string; // Question to send automatically
  onQuestionSent?: () => void; // Callback when question is sent
  shouldFocus?: boolean; // Whether to focus the input
  onFocused?: () => void; // Callback when focus is complete
  documents?: Document[]; // All project documents for AI suggested questions
  onNavigateToApiIntegration?: () => void; // Callback to navigate to API integration section
}

interface StreamingMessage {
  id: string;
  question: string;
  response: string;
  isStreaming: boolean;
  timestamp: Date;
  sourceReferences?: any[];
}

export const ChatInterface = forwardRef<HTMLDivElement, ChatInterfaceProps>(
  ({ project, document, hasDocuments = true, questionToSend, onQuestionSent, shouldFocus, onFocused, documents = [], onNavigateToApiIntegration }, ref) => {
  const [question, setQuestion] = useState("");
  const [streamingMessages, setStreamingMessages] = useState<StreamingMessage[]>([]);
  const [isAsking, setIsAsking] = useState(false);
  const [showSubscriptionModal, setShowSubscriptionModal] = useState(false);
  const { toast } = useToast();
  const queryClient = useQueryClient();
  const abortControllerRef = useRef<AbortController | null>(null);
  const textareaRef = useRef<HTMLTextAreaElement>(null);

  // Use project ID for fetching messages
  const chatId = project?.id || document?.id;
  const queryKey = project ? `/api/projects/${project.id}/chat` : `/api/chat/${document?.id}`;

  const { data: messages = [], isLoading } = useQuery<ChatMessage[]>({
    queryKey: [queryKey],
    enabled: !!chatId,
  });

  // Combine regular messages with streaming messages (newest first)
  const allMessages = [
    ...streamingMessages,
    ...messages.map(msg => ({
      id: msg.id.toString(),
      question: msg.question,
      response: msg.response,
      isStreaming: false,
      timestamp: new Date(msg.timestamp),
      sourceReferences: msg.sourceReferences,
    }))
  ];

  const handleStreamingResponse = async (questionText: string) => {
    if (abortControllerRef.current) {
      abortControllerRef.current.abort();
    }

    abortControllerRef.current = new AbortController();
    setIsAsking(true);

    // Create a temporary streaming message
    const tempId = `temp-${Date.now()}`;
    const streamingMessage: StreamingMessage = {
      id: tempId,
      question: questionText,
      response: "",
      isStreaming: true,
      timestamp: new Date(),
    };

    setStreamingMessages(prev => [...prev, streamingMessage]);

    try {
      // Get authorization header
      const { data: { session } } = await supabase.auth.getSession();
      const headers: Record<string, string> = {
        'Content-Type': 'application/json',
      };
      
      if (session?.access_token) {
        headers["Authorization"] = `Bearer ${session.access_token}`;
      }

      const response = await fetch('/api/chat/ask', {
        method: 'POST',
        headers,
        body: JSON.stringify({
          projectId: project?.id,
          documentId: document?.id,
          question: questionText,
        }),
        credentials: "include",
        signal: abortControllerRef.current.signal,
      });

      if (!response.ok) {
        // Handle rate limiting errors
        if (response.status === 403) {
          const errorData = await response.json().catch(() => ({}));
          if (errorData.code === 'CHAT_LIMIT_REACHED') {
            setShowSubscriptionModal(true);
            setStreamingMessages(prev => prev.filter(msg => msg.id !== tempId));
            return;
          }
        }
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const reader = response.body?.getReader();
      const decoder = new TextDecoder();

      if (!reader) {
        throw new Error('No reader available');
      }

      let buffer = '';
      let lastDataReceived = Date.now();
      const streamTimeout = 90000; // 90 second timeout for streaming
      
      while (true) {
        // Check for timeout
        if (Date.now() - lastDataReceived > streamTimeout) {
          console.warn('Stream timeout - no data received for 90 seconds');
          // Mark streaming as complete and show what we have
          setStreamingMessages(prev => 
            prev.map(msg => 
              msg.id === tempId 
                ? { ...msg, isStreaming: false, response: msg.response + '\n\n[Response was interrupted due to timeout]' }
                : msg
            )
          );
          break;
        }
        
        const { done, value } = await reader.read();
        
        if (done) break;
        
        lastDataReceived = Date.now(); // Reset timeout counter
        buffer += decoder.decode(value, { stream: true });
        const lines = buffer.split('\n');
        
        // Keep the last potentially incomplete line in the buffer
        buffer = lines.pop() || '';
        
        for (const line of lines) {
          if (line.startsWith('data: ')) {
            const data = line.slice(6);
            
            if (data === '[DONE]') {
              // Streaming complete
              setStreamingMessages(prev => 
                prev.map(msg => 
                  msg.id === tempId 
                    ? { ...msg, isStreaming: false }
                    : msg
                )
              );
              
              // Refresh the main messages list to show the saved message
              // Also invalidate user profile and limits to update credits count
              setTimeout(() => {
                queryClient.invalidateQueries({ queryKey: [queryKey] });
                queryClient.invalidateQueries({ queryKey: ["/api/user/profile"] });
                queryClient.invalidateQueries({ queryKey: ["/api/user/limits"] });
                queryClient.invalidateQueries({ queryKey: ["/api/dashboard/stats"] });
                setStreamingMessages(prev => prev.filter(msg => msg.id !== tempId));
              }, 1000);
              
              return;
            }
            
            try {
              const parsed = JSON.parse(data);
              if (parsed.content) {
                // Update the streaming message with new content
                setStreamingMessages(prev => 
                  prev.map(msg => 
                    msg.id === tempId 
                      ? { ...msg, response: msg.response + parsed.content }
                      : msg
                  )
                );
              } else if (parsed.error) {
                // Handle error messages in stream
                setStreamingMessages(prev => 
                  prev.map(msg => 
                    msg.id === tempId 
                      ? { ...msg, isStreaming: false, response: msg.response + '\n\n[Error: ' + parsed.error + ']' }
                      : msg
                  )
                );
              }
            } catch (e) {
              // Ignore JSON parse errors for malformed chunks
            }
          }
        }
      }
      
      // If we exit the loop without receiving [DONE], mark as complete anyway
      setStreamingMessages(prev => 
        prev.map(msg => 
          msg.id === tempId && msg.isStreaming
            ? { ...msg, isStreaming: false }
            : msg
        )
      );
      
      // Clean up and refresh after a brief delay
      setTimeout(() => {
        queryClient.invalidateQueries({ queryKey: [queryKey] });
        queryClient.invalidateQueries({ queryKey: ["/api/user/profile"] });
        queryClient.invalidateQueries({ queryKey: ["/api/user/limits"] });
        queryClient.invalidateQueries({ queryKey: ["/api/dashboard/stats"] });
        setStreamingMessages(prev => prev.filter(msg => msg.id !== tempId));
      }, 1000);
      
    } catch (error: any) {
      if (error.name === 'AbortError') {
        return; // Request was aborted
      }
      
      console.error('Streaming error:', error);
      
      // Remove the streaming message and show error
      setStreamingMessages(prev => prev.filter(msg => msg.id !== tempId));
      
      toast({
        title: "Failed to process question",
        description: error.message,
        variant: "destructive",
      });
    } finally {
      setIsAsking(false);
      abortControllerRef.current = null;
    }
  };

  const handleSubmit = () => {
    if (question.trim() && !isAsking) {
      handleStreamingResponse(question.trim());
      setQuestion("");
    }
  };

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      handleSubmit();
    }
  };

  // Clean up on unmount
  useEffect(() => {
    return () => {
      if (abortControllerRef.current) {
        abortControllerRef.current.abort();
      }
    };
  }, []);

  const copyToClipboard = async (text: string) => {
    try {
      await navigator.clipboard.writeText(text);
      toast({
        title: "Copied to clipboard",
        description: "Response has been copied to your clipboard.",
      });
    } catch (error) {
      toast({
        title: "Failed to copy",
        description: "Could not copy to clipboard.",
        variant: "destructive",
      });
    }
  };

  const generateEmbedUrls = () => {
    // Ensure we have either project or document ID
    if (!project?.id && !document?.id) {
      throw new Error('No project or document ID available for embed generation');
    }

    const embedUrl = generateEmbedUrl({
      projectId: project?.id,
      documentId: document?.id
    });
    
    const minimalUrl = generateEmbedUrl({
      projectId: project?.id,
      documentId: document?.id,
      minimal: true
    });
    
    const darkThemeUrl = generateEmbedUrl({
      projectId: project?.id,
      documentId: document?.id,
      theme: 'dark'
    });
    
    const hideHeaderUrl = generateEmbedUrl({
      projectId: project?.id,
      documentId: document?.id,
      hideHeader: true
    });
    
    const iframeCode = generateEmbedCode({
      projectId: project?.id,
      documentId: document?.id
    });
    
    const minimalIframeCode = generateEmbedCode({
      projectId: project?.id,
      documentId: document?.id,
      minimal: true,
      height: '400'
    });
    
    const darkIframeCode = generateEmbedCode({
      projectId: project?.id,
      documentId: document?.id,
      theme: 'dark'
    });
    
    return { 
      embedUrl, 
      iframeCode, 
      minimalUrl, 
      minimalIframeCode, 
      darkThemeUrl, 
      darkIframeCode,
      hideHeaderUrl 
    };
  };

  // Fallback copy method for environments where clipboard API might not work
  const fallbackCopyTextToClipboard = (text: string) => {
    const textArea = window.document.createElement("textarea");
    textArea.value = text;
    
    // Avoid scrolling to bottom
    textArea.style.top = "0";
    textArea.style.left = "0";
    textArea.style.position = "fixed";
    textArea.style.opacity = "0";

    window.document.body.appendChild(textArea);
    textArea.focus();
    textArea.select();

    try {
      const successful = window.document.execCommand('copy');
      if (!successful) {
        throw new Error('Fallback copy failed');
      }
    } catch (err) {
      throw new Error('Fallback copy not supported');
    } finally {
      window.document.body.removeChild(textArea);
    }
  };

  const copyEmbedUrl = async () => {
    try {
      console.log('Copying embed URL - Project:', project?.id, 'Document:', document?.id);
      const { embedUrl } = generateEmbedUrls();
      console.log('Generated embed URL:', embedUrl);
      
      // Open URL in new tab
      window.open(embedUrl, '_blank');
      
      // Try modern clipboard API first
      if (navigator.clipboard && window.isSecureContext) {
        await navigator.clipboard.writeText(embedUrl);
      } else {
        // Fallback for older browsers or non-secure contexts
        fallbackCopyTextToClipboard(embedUrl);
      }
      
      toast({
        title: "URL opened and copied!",
        description: "Embed URL opened in new tab and copied to clipboard.",
      });
    } catch (error) {
      console.error('Error copying embed URL:', error);
      toast({
        title: "Failed to copy URL",
        description: error instanceof Error ? error.message : "Could not copy embed URL to clipboard.",
        variant: "destructive",
      });
    }
  };

  const copyEmbedCode = async () => {
    try {
      console.log('Copying embed code - Project:', project?.id, 'Document:', document?.id);
      const { iframeCode } = generateEmbedUrls();
      console.log('Generated embed code:', iframeCode);
      
      // Try modern clipboard API first
      if (navigator.clipboard && window.isSecureContext) {
        await navigator.clipboard.writeText(iframeCode);
      } else {
        // Fallback for older browsers or non-secure contexts
        fallbackCopyTextToClipboard(iframeCode);
      }
      
      toast({
        title: "Embed code copied!",
        description: "Paste this HTML code into your website.",
      });
    } catch (error) {
      console.error('Error copying embed code:', error);
      toast({
        title: "Failed to copy code",
        description: error instanceof Error ? error.message : "Could not copy embed code to clipboard.",
        variant: "destructive",
      });
    }
  };

  const copyApiSample = async () => {
    try {
      console.log('Copying API sample - Project:', project?.id, 'Document:', document?.id);
      const apiSample = generateApiSample({
        projectId: project?.id,
        documentId: document?.id
      });
      console.log('Generated API sample:', apiSample);
      
      // Try modern clipboard API first
      if (navigator.clipboard && window.isSecureContext) {
        await navigator.clipboard.writeText(apiSample);
      } else {
        // Fallback for older browsers or non-secure contexts
        fallbackCopyTextToClipboard(apiSample);
      }
      
      toast({
        title: "API code copied!",
        description: "Use this code to build your own custom UI.",
      });
    } catch (error) {
      console.error('Error copying API sample:', error);
      toast({
        title: "Failed to copy API code",
        description: error instanceof Error ? error.message : "Could not copy API code to clipboard.",
        variant: "destructive",
      });
    }
  };

  const navigateToApiIntegration = () => {
    if (onNavigateToApiIntegration) {
      onNavigateToApiIntegration();
    } else {
      // Fallback: try to navigate programmatically
      const embedTab = document.querySelector('[data-value="embed"]') as HTMLElement;
      if (embedTab) {
        embedTab.click();
        // Smooth scroll to the API Integration section after a small delay
        setTimeout(() => {
          const apiSection = document.querySelector('[data-api-integration]') as HTMLElement;
          if (apiSection) {
            apiSection.scrollIntoView({ behavior: 'smooth', block: 'start' });
          }
        }, 100);
      }
    }
    
    toast({
      title: "Navigating to API SDK",
      description: "Redirecting to the Embed & Security tab for full API documentation.",
    });
  };

  // Extract suggested questions from AI Document Analysis
  const getAISuggestedQuestions = () => {
    const questionsFromDocuments: Array<{question: string, source: string}> = [];
    
    documents.forEach(doc => {
      const summary = doc.summary as any;
      if (summary?.suggested_questions && Array.isArray(summary.suggested_questions)) {
        summary.suggested_questions.forEach((question: string) => {
          questionsFromDocuments.push({
            question,
            source: doc.filename
          });
        });
      }
    });
    
    // If we have AI-generated questions, return them (limit to 8 for better UI)
    if (questionsFromDocuments.length > 0) {
      return questionsFromDocuments.slice(0, 8);
    }
    
    // Fallback to generic questions if no AI-generated questions available
    return project ? [
      { question: "What are the main themes across all documents in this project?", source: "Default" },
      { question: "Can you provide a comprehensive summary of all the uploaded documents?", source: "Default" },
      { question: "What are the key insights from all the documents combined?", source: "Default" },
      { question: "Are there any contradictions or conflicts between the documents?", source: "Default" },
    ] : [
      { question: "What are the main points discussed in this document?", source: "Default" },
      { question: "Can you summarize the key findings?", source: "Default" },
      { question: "What conclusions are drawn?", source: "Default" },
      { question: "Are there any recommendations mentioned?", source: "Default" },
    ];
  };

  const aiSuggestedQuestions = getAISuggestedQuestions();

  const placeholderText = project 
    ? "Ask me anything about all the documents in this project..."
    : "What would you like to know about this document?";

  const assistantDescription = project
    ? "Ask me anything about your project documents - I'll provide intelligent answers based on all uploaded files"
    : "Ask me anything about your document - I'll provide intelligent, contextual answers";

  // Handle automatic question sending from DocumentSummary
  useEffect(() => {
    if (questionToSend && !isAsking && questionToSend.trim()) {
      setQuestion(questionToSend);
      handleStreamingResponse(questionToSend.trim());
      setQuestion("");
      onQuestionSent?.();
    }
  }, [questionToSend, isAsking]);

  // Handle focus when shouldFocus prop is true
  useEffect(() => {
    if (shouldFocus && textareaRef.current) {
      textareaRef.current.focus();
      onFocused?.();
    }
  }, [shouldFocus, onFocused]);

  return (
    <div ref={ref} className="space-y-4 lg:space-y-6">
      {/* Question Input */}
      <Card className="border-0 shadow-xl bg-gradient-to-br from-emerald-50 to-blue-50 mb-6 lg:mb-8 overflow-hidden">
        <CardHeader className="bg-gradient-to-r from-emerald-600 to-blue-600 text-white p-4 lg:p-6">
          <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between space-y-3 lg:space-y-0">
            <div className="flex-1">
              <CardTitle className="text-lg lg:text-xl font-bold flex items-center">
                <div className="w-8 h-8 lg:w-10 lg:h-10 bg-white/20 rounded-xl flex items-center justify-center mr-3 shrink-0">
                  <Bot className="w-4 h-4 lg:w-5 lg:h-5" />
                </div>
                <span className="truncate">AI Document Assistant</span>
              </CardTitle>
              <p className="text-emerald-100 text-sm lg:text-base mt-1 lg:mt-0">{assistantDescription}</p>
            </div>
            {hasDocuments && (
              <div className="flex flex-col sm:flex-row space-y-2 sm:space-y-0 sm:space-x-2">
                <Button
                  onClick={copyEmbedUrl}
                  variant="secondary"
                  size="sm"
                  className="bg-white text-emerald-700 border-2 border-white/30 hover:bg-emerald-50 hover:border-emerald-200 shadow-lg font-medium w-full sm:w-auto text-xs lg:text-sm transition-all duration-200 transform hover:scale-105"
                >
                  <ExternalLink className="w-3 h-3 lg:w-4 lg:h-4 mr-1" />
                  Copy URL
                </Button>
                <Tooltip>
                  <TooltipTrigger asChild>
                    <Button
                      onClick={copyEmbedCode}
                      variant="secondary" 
                      size="sm"
                      className="bg-teal-500 text-white border-2 border-teal-400 hover:bg-teal-600 hover:border-teal-500 shadow-lg font-medium w-full sm:w-auto text-xs lg:text-sm transition-all duration-200 transform hover:scale-105"
                    >
                      <Code className="w-3 h-3 lg:w-4 lg:h-4 mr-1" />
                      Embed
                    </Button>
                  </TooltipTrigger>
                  <TooltipContent side="bottom" className="max-w-md p-3">
                    <div className="space-y-2">
                      <p className="text-sm font-medium">Embed Code Preview:</p>
                      <pre className="text-xs bg-slate-100 p-2 rounded overflow-x-auto whitespace-pre-wrap">
                        {(() => {
                          try {
                            const { iframeCode } = generateEmbedUrls();
                            return iframeCode;
                          } catch (error) {
                            return 'Error generating embed code';
                          }
                        })()}
                      </pre>
                    </div>
                  </TooltipContent>
                </Tooltip>
                <Tooltip>
                  <TooltipTrigger asChild>
                    <Button
                      onClick={navigateToApiIntegration}
                      variant="secondary" 
                      size="sm"
                      className="bg-indigo-500 text-white border-2 border-indigo-400 hover:bg-indigo-600 hover:border-indigo-500 shadow-lg font-medium w-full sm:w-auto text-xs lg:text-sm transition-all duration-200 transform hover:scale-105"
                    >
                      <Code className="w-3 h-3 lg:w-4 lg:h-4 mr-1" />
                      API SDK
                    </Button>
                  </TooltipTrigger>
                  <TooltipContent side="bottom" className="max-w-md p-3">
                    <div className="space-y-2">
                      <p className="text-sm font-medium">API SDK Integration:</p>
                      <p className="text-xs text-slate-600">Navigate to full API documentation and code samples</p>
                    </div>
                  </TooltipContent>
                </Tooltip>
              </div>
            )}
          </div>
        </CardHeader>
        <CardContent className="p-4 lg:p-6">
          <div className="relative">
            <Textarea
              ref={textareaRef}
              value={question}
              onChange={(e) => setQuestion(e.target.value)}
              onKeyPress={handleKeyPress}
              className="min-h-[80px] lg:min-h-[100px] pr-12 lg:pr-20 resize-none focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent text-sm lg:text-base"
              placeholder={placeholderText}
              disabled={isAsking}
            />
            <Button
              onClick={handleSubmit}
              disabled={!question.trim() || isAsking}
              className="absolute bottom-2 lg:bottom-4 right-2 lg:right-4 bg-blue-600 hover:bg-blue-700 text-white p-2 lg:p-3"
              size="sm"
            >
              {isAsking ? (
                <div className="w-3 h-3 lg:w-4 lg:h-4 border-2 border-white border-t-transparent rounded-full animate-spin" />
              ) : (
                <Send className="w-3 h-3 lg:w-4 lg:h-4" />
              )}
            </Button>
          </div>
        </CardContent>
      </Card>

      {/* Chat History */}
      {isLoading ? (
        <div className="flex justify-center py-6 lg:py-8">
          <div className="w-6 h-6 lg:w-8 lg:h-8 border-4 border-blue-600 border-t-transparent rounded-full animate-spin" />
        </div>
      ) : allMessages.length === 0 ? (
        <Card className="border-slate-200">
          <CardContent className="p-4 sm:p-6 lg:p-8">
            <div className="mb-6">
              <div className="flex items-center mb-2">
                <div className="w-5 h-5 bg-orange-500 rounded-full flex items-center justify-center mr-3">
                  <MessageCircle className="w-3 h-3 text-white" />
                </div>
                <h3 className="text-base lg:text-lg font-semibold text-slate-800">Suggested Questions</h3>
              </div>
              <p className="text-sm text-slate-500 ml-8">Try asking these</p>
            </div>
            
            <div className="space-y-1">
              {aiSuggestedQuestions.map((item, index) => (
                <button
                  key={index}
                  className="w-full text-left p-3 rounded-lg hover:bg-slate-50 flex items-center justify-between group transition-colors"
                  onClick={() => setQuestion(item.question)}
                >
                  <span className="text-slate-700 text-sm lg:text-base flex-1 pr-3">
                    {item.question}
                  </span>
                  <ChevronRight className="w-4 h-4 text-slate-400 group-hover:text-slate-600 flex-shrink-0" />
                </button>
              ))}
            </div>
          </CardContent>
        </Card>
      ) : (
        allMessages.map((message) => (
          <div key={message.id} className="space-y-3 lg:space-y-4">
            {/* User Question */}
            <div className="flex justify-end">
              <div className="max-w-full sm:max-w-2xl lg:max-w-3xl bg-blue-600 text-white rounded-xl p-3 lg:p-4">
                <p className="text-sm lg:text-base">{message.question}</p>
                <span className="text-xs text-blue-100 mt-2 block">
                  {formatTimestamp(new Date(message.timestamp))}
                </span>
              </div>
            </div>

            {/* AI Response */}
            <div className="flex justify-start">
              <Card className="max-w-full sm:max-w-2xl lg:max-w-3xl border-slate-200 w-full">
                <CardContent className="p-3 sm:p-4 lg:p-6">
                  <div className="flex items-start space-x-2 lg:space-x-3 mb-3 lg:mb-4">
                    <div className="w-6 h-6 lg:w-8 lg:h-8 bg-gradient-to-br from-purple-500 to-blue-600 rounded-full flex items-center justify-center shrink-0">
                      <Bot className="w-3 h-3 lg:w-4 lg:h-4 text-white" />
                    </div>
                    <div className="flex-1 min-w-0">
                      <h4 className="font-medium text-slate-800 mb-2 flex items-center text-sm lg:text-base">
                        AI Assistant
                        {message.isStreaming && (
                          <div className="ml-2 flex items-center text-blue-600">
                            <div className="w-1.5 h-1.5 lg:w-2 lg:h-2 bg-blue-600 rounded-full animate-pulse mr-1"></div>
                            <span className="text-xs">Typing...</span>
                          </div>
                        )}
                      </h4>
                      <div className="prose prose-slate prose-sm lg:prose-base max-w-none text-slate-700 text-sm lg:text-base">
                        <ReactMarkdown children={String(message.response)} />
                        {message.isStreaming && (
                          <span className="inline-block w-1.5 h-3 lg:w-2 lg:h-4 bg-blue-600 animate-pulse ml-1"></span>
                        )}
                      </div>
                    </div>
                  </div>

                  {/* Source References */}
                  {message.sourceReferences && Array.isArray(message.sourceReferences) && message.sourceReferences.length > 0 && (
                    <div className="border-t border-slate-100 pt-4">
                      <h5 className="text-sm font-medium text-slate-700 mb-2">Sources Referenced:</h5>
                      <div className="flex flex-wrap gap-2">
                        {(message.sourceReferences as any[]).map((source: any, index: number) => (
                          <span
                            key={index}
                            className="inline-flex items-center px-2 py-1 bg-slate-100 text-xs font-medium text-slate-600 rounded"
                          >
                            <i className="fas fa-file-text mr-1" />
                            Page {source.page}
                          </span>
                        ))}
                      </div>
                    </div>
                  )}

                  {/* Response Actions */}
                  <div className="flex items-center justify-between mt-4 pt-4 border-t border-slate-100">
                    <div className="flex items-center space-x-4">
                      <Button
                        variant="ghost"
                        size="sm"
                        className="text-slate-500 hover:text-slate-700"
                      >
                        <ThumbsUp className="w-4 h-4 mr-1" />
                        Helpful
                      </Button>
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => copyToClipboard(message.response)}
                        className="text-slate-500 hover:text-slate-700"
                      >
                        <Copy className="w-4 h-4 mr-1" />
                        Copy
                      </Button>
                    </div>
                    <span className="text-xs text-slate-400">
                      {formatTimestamp(new Date(message.timestamp))}
                    </span>
                  </div>
                </CardContent>
              </Card>
            </div>
          </div>
        ))
      )}

      {/* Subscription Modal */}
      <SubscriptionModal
        isOpen={showSubscriptionModal}
        onClose={() => setShowSubscriptionModal(false)}
        triggerReason="CHAT_LIMIT_REACHED"
      />
    </div>
  );
});

ChatInterface.displayName = 'ChatInterface';
