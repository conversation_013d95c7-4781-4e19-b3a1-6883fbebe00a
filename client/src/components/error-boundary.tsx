import { Component, ReactNode, ErrorInfo } from 'react';

interface Props {
  children: ReactNode;
  fallback?: ReactNode;
}

interface State {
  hasError: boolean;
  error?: Error;
}

export class ErrorBoundary extends Component<Props, State> {
  constructor(props: Props) {
    super(props);
    this.state = { hasError: false };
  }

  static getDerivedStateFromError(error: Error): State {
    return { hasError: true, error };
  }

  componentDidCatch(error: Error, errorInfo: ErrorInfo) {
    console.error('ErrorBoundary caught an error:', error, errorInfo);
    
    // Check if it's an authentication error and handle silently
    const authErrorMessages = [
      'Invalid or expired token',
      'Authentication failed',
      'No authorization token provided',
      'fetch failed',
      'UND_ERR_CONNECT_TIMEOUT',
      'ConnectTimeoutError'
    ];
    
    const errorMessage = error?.message || error?.toString() || '';
    const isAuthError = authErrorMessages.some(authError => errorMessage.includes(authError));
    
    if (isAuthError) {
      try {
        localStorage.clear();
        sessionStorage.clear();
        setTimeout(() => {
          window.location.href = '/';
        }, 1000);
      } catch (e) {
        window.location.href = '/';
      }
    }
  }

  render() {
    if (this.state.hasError) {
      // Use custom fallback if provided
      if (this.props.fallback) {
        return this.props.fallback;
      }

      // Default minimal error UI
      return (
        <div style={{
          minHeight: '100vh',
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center',
          background: 'linear-gradient(to bottom right, #f1f5f9, #e2e8f0)',
          fontFamily: 'system-ui, -apple-system, sans-serif'
        }}>
          <div style={{
            background: 'white',
            padding: '2rem',
            borderRadius: '8px',
            boxShadow: '0 4px 6px -1px rgba(0, 0, 0, 0.1)',
            textAlign: 'center',
            maxWidth: '400px',
            margin: '1rem'
          }}>
            <h2 style={{ 
              fontSize: '2.5rem', 
              fontWeight: 'bold', 
              marginBottom: '1rem',
              color: '#dc3545'
            }}>
              404
            </h2>
            <p style={{ 
              color: '#6b7280', 
              marginBottom: '1.5rem',
              fontSize: '1.1rem'
            }}>
              Page not found
            </p>
            <button
              onClick={() => window.location.href = '/'}
              style={{
                background: '#3b82f6',
                color: 'white',
                padding: '0.75rem 1.5rem',
                border: 'none',
                borderRadius: '6px',
                cursor: 'pointer',
                fontSize: '1rem',
                fontWeight: '500'
              }}
            >
              Home
            </button>
          </div>
        </div>
      );
    }

    return this.props.children;
  }
} 