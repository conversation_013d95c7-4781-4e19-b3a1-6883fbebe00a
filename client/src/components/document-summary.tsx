import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, <PERSON>Title } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { Separator } from "@/components/ui/separator";
import { 
  CheckCircle, 
  FileText, 
  Clock, 
  BookOpen, 
  TrendingUp, 
  Users, 
  Calendar, 
  Hash, 
  MessageCircle,
  Sparkles,
  Brain,
  Target,
  Eye,
  ChevronRight
} from "lucide-react";
import type { Document } from "@shared/schema";

interface DocumentSummaryProps {
  document: Document;
  onQuestionClick?: (question: string) => void;
}

interface DocumentSummary {
  main_summary: string;
  quick_summary?: string;
  key_points: string[];
  topics: string[];
  entities: string[];
  suggested_questions: string[];
  document_type: string;
  reading_time_minutes: number;
  complexity_level: 'beginner' | 'intermediate' | 'advanced';
}

export function DocumentSummary({ document, onQuestionClick }: DocumentSummaryProps) {
  const summary = document.summary as DocumentSummary | null;

  const getComplexityColor = (level: string) => {
    switch (level) {
      case 'beginner': return 'bg-green-100 text-green-800 border-green-200';
      case 'intermediate': return 'bg-yellow-100 text-yellow-800 border-yellow-200';
      case 'advanced': return 'bg-red-100 text-red-800 border-red-200';
      default: return 'bg-gray-100 text-gray-800 border-gray-200';
    }
  };

  const getComplexityIcon = (level: string) => {
    switch (level) {
      case 'beginner': return <Eye className="w-3 h-3" />;
      case 'intermediate': return <Target className="w-3 h-3" />;
      case 'advanced': return <Brain className="w-3 h-3" />;
      default: return <BookOpen className="w-3 h-3" />;
    }
  };

  if (!summary) {
    // Fallback UI for documents without AI summary
    return (
      <Card className="border-slate-200 mb-6">
        <CardHeader className="pb-4">
          <div className="flex items-center space-x-3">
            <div className="w-10 h-10 bg-green-600 rounded-lg flex items-center justify-center">
              <CheckCircle className="w-6 h-6 text-white" />
            </div>
            <div>
              <CardTitle className="text-lg font-semibold text-slate-800">Document Ready</CardTitle>
              <p className="text-sm text-slate-600">Successfully processed and indexed</p>
            </div>
          </div>
        </CardHeader>
        <CardContent className="pt-0">
          <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4 text-sm">
            <div className="bg-slate-50 rounded-lg p-3">
              <span className="text-slate-500">Pages:</span>
              <span className="font-medium text-slate-800 ml-2">
                {document.pageCount || 'N/A'}
              </span>
            </div>
            <div className="bg-slate-50 rounded-lg p-3">
              <span className="text-slate-500">Word Count:</span>
              <span className="font-medium text-slate-800 ml-2">
                {document.wordCount?.toLocaleString() || 'N/A'}
              </span>
            </div>
            <div className="bg-slate-50 rounded-lg p-3">
              <span className="text-slate-500">Status:</span>
              <span className="font-medium text-green-800 ml-2">Ready</span>
            </div>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <div className="space-y-4 sm:space-y-6">
      {/* Header Card */}
      <Card className="border-indigo-200 bg-gradient-to-r from-indigo-50 to-blue-50">
        <CardHeader className="pb-4">
          <div className="flex flex-col space-y-4 sm:flex-row sm:items-start sm:justify-between sm:space-y-0">
            <div className="flex items-center space-x-3">
              <div className="w-10 h-10 sm:w-12 sm:h-12 bg-gradient-to-br from-indigo-600 to-blue-600 rounded-xl flex items-center justify-center animate-sparkle-pulse">
                <Sparkles className="w-5 h-5 sm:w-7 sm:h-7 text-white animate-sparkle" />
              </div>
              <div className="flex-1 min-w-0">
                <CardTitle className="text-lg sm:text-xl font-bold text-slate-800 flex flex-col sm:flex-row sm:items-center">
                  <span className="truncate">AI Document Analysis</span>
                  <Badge variant="secondary" className="mt-1 sm:mt-0 sm:ml-2 text-xs self-start sm:self-auto">
                    {summary.document_type}
                  </Badge>
                </CardTitle>
                <p className="text-sm sm:text-base text-slate-600">Intelligent insights powered by AI</p>
              </div>
            </div>
            <Badge 
              variant="outline" 
              className={`${getComplexityColor(summary.complexity_level)} border self-start sm:self-auto flex-shrink-0`}
            >
              {getComplexityIcon(summary.complexity_level)}
              <span className="ml-1 capitalize">{summary.complexity_level}</span>
            </Badge>
          </div>
        </CardHeader>
      </Card>

      {/* Quick Stats */}
      <Card>
        <CardContent className="p-4 sm:p-6">
          <div className="grid grid-cols-2 sm:grid-cols-4 gap-3 sm:gap-4">
            <div className="text-center">
              <div className="w-10 h-10 sm:w-12 sm:h-12 bg-blue-100 rounded-lg flex items-center justify-center mx-auto mb-2">
                <FileText className="w-5 h-5 sm:w-6 sm:h-6 text-blue-600" />
              </div>
              <div className="text-xl sm:text-2xl font-bold text-slate-800">{document.pageCount || 0}</div>
              <div className="text-xs sm:text-sm text-slate-600">Pages</div>
            </div>
            <div className="text-center">
              <div className="w-10 h-10 sm:w-12 sm:h-12 bg-green-100 rounded-lg flex items-center justify-center mx-auto mb-2">
                <Hash className="w-5 h-5 sm:w-6 sm:h-6 text-green-600" />
              </div>
              <div className="text-xl sm:text-2xl font-bold text-slate-800">{document.wordCount?.toLocaleString() || 0}</div>
              <div className="text-xs sm:text-sm text-slate-600">Words</div>
            </div>
            <div className="text-center">
              <div className="w-10 h-10 sm:w-12 sm:h-12 bg-purple-100 rounded-lg flex items-center justify-center mx-auto mb-2">
                <Clock className="w-5 h-5 sm:w-6 sm:h-6 text-purple-600" />
              </div>
              <div className="text-xl sm:text-2xl font-bold text-slate-800">{summary.reading_time_minutes}</div>
              <div className="text-xs sm:text-sm text-slate-600">Min Read</div>
            </div>
            <div className="text-center">
              <div className="w-10 h-10 sm:w-12 sm:h-12 bg-orange-100 rounded-lg flex items-center justify-center mx-auto mb-2">
                <TrendingUp className="w-5 h-5 sm:w-6 sm:h-6 text-orange-600" />
              </div>
              <div className="text-xl sm:text-2xl font-bold text-slate-800">{summary.key_points.length}</div>
              <div className="text-xs sm:text-sm text-slate-600">Key Points</div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Main Summary */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center text-base sm:text-lg">
            <BookOpen className="w-4 h-4 sm:w-5 sm:h-5 mr-2 text-indigo-600" />
            Executive Summary
          </CardTitle>
        </CardHeader>
        <CardContent>
          <p className="text-slate-700 leading-relaxed text-sm sm:text-base">
            {summary.main_summary}
          </p>
        </CardContent>
      </Card>

      {/* Key Points */}
      {summary.key_points.length > 0 && (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center text-base sm:text-lg">
              <Target className="w-4 h-4 sm:w-5 sm:h-5 mr-2 text-green-600" />
              Key Points
            </CardTitle>
          </CardHeader>
          <CardContent>
            <ul className="space-y-3">
              {summary.key_points.map((point, index) => (
                <li key={index} className="flex items-start space-x-3">
                  <div className="w-6 h-6 bg-green-100 rounded-full flex items-center justify-center mt-0.5 flex-shrink-0">
                    <span className="text-green-600 text-sm font-medium">{index + 1}</span>
                  </div>
                  <span className="text-slate-700 leading-relaxed text-sm sm:text-base">{point}</span>
                </li>
              ))}
            </ul>
          </CardContent>
        </Card>
      )}

      {/* Topics and Entities Grid */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-4 sm:gap-6">
        {/* Topics */}
        {summary.topics.length > 0 && (
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center text-base sm:text-lg">
                <Brain className="w-4 h-4 sm:w-5 sm:h-5 mr-2 text-purple-600" />
                Main Topics
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="flex flex-wrap gap-1.5 sm:gap-2">
                {summary.topics.map((topic, index) => (
                  <Badge key={index} variant="secondary" className="bg-purple-100 text-purple-800 border-purple-200 text-xs sm:text-sm">
                    {topic}
                  </Badge>
                ))}
              </div>
            </CardContent>
          </Card>
        )}

        {/* Entities */}
        {summary.entities.length > 0 && (
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center text-base sm:text-lg">
                <Users className="w-4 h-4 sm:w-5 sm:h-5 mr-2 text-blue-600" />
                Key Entities
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="flex flex-wrap gap-1.5 sm:gap-2">
                {summary.entities.map((entity, index) => (
                  <Badge key={index} variant="outline" className="bg-blue-50 text-blue-800 border-blue-200 text-xs sm:text-sm">
                    {entity}
                  </Badge>
                ))}
              </div>
            </CardContent>
          </Card>
        )}
      </div>

      {/* Suggested Questions */}
      {summary.suggested_questions.length > 0 && (
        <Card>
          <CardHeader>
            <CardTitle className="flex flex-col sm:flex-row sm:items-center text-base sm:text-lg">
              <div className="flex items-center">
                <MessageCircle className="w-4 h-4 sm:w-5 sm:h-5 mr-2 text-orange-600" />
                Suggested Questions
              </div>
              <Badge variant="secondary" className="mt-1 sm:mt-0 sm:ml-2 text-xs self-start sm:self-auto">
                Try asking these
              </Badge>
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 gap-2 sm:gap-3">
              {summary.suggested_questions.map((question, index) => (
                <Button
                  key={index}
                  variant="ghost"
                  className="h-auto p-3 sm:p-4 text-left justify-between hover:bg-orange-50 hover:border-orange-200 border border-transparent rounded-lg group"
                  onClick={() => onQuestionClick?.(question)}
                >
                  <span className="text-slate-700 group-hover:text-orange-700 leading-relaxed text-sm sm:text-base pr-2">
                    {question}
                  </span>
                  <ChevronRight className="w-4 h-4 text-slate-400 group-hover:text-orange-600 flex-shrink-0" />
                </Button>
              ))}
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  );
}
