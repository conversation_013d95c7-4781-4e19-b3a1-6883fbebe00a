<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Embed Code Generator</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            margin: 0;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 30px;
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
        }
        .header {
            text-align: center;
            margin-bottom: 30px;
        }
        .header h1 {
            color: #2d3748;
            font-size: 2rem;
            margin-bottom: 10px;
        }
        .form-group {
            margin-bottom: 20px;
        }
        .form-group label {
            display: block;
            font-weight: 600;
            color: #2d3748;
            margin-bottom: 8px;
        }
        .form-group input, .form-group select {
            width: 100%;
            padding: 12px;
            border: 2px solid #e2e8f0;
            border-radius: 8px;
            font-size: 16px;
            box-sizing: border-box;
        }
        .form-group input:focus, .form-group select:focus {
            outline: none;
            border-color: #4299e1;
        }
        .checkbox-group {
            display: flex;
            align-items: center;
            margin: 10px 0;
        }
        .checkbox-group input[type="checkbox"] {
            width: auto;
            margin-right: 10px;
        }
        .form-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
        }
        .preview-section {
            background: #f8fafc;
            border: 2px solid #e2e8f0;
            border-radius: 12px;
            padding: 20px;
            margin: 20px 0;
        }
        .preview-section h3 {
            margin-top: 0;
            color: #2d3748;
        }
        .code-output {
            background: #1a202c;
            color: #e2e8f0;
            padding: 15px;
            border-radius: 8px;
            font-family: 'Monaco', 'Consolas', monospace;
            font-size: 14px;
            overflow-x: auto;
            white-space: pre-wrap;
            word-break: break-all;
        }
        .btn {
            background: linear-gradient(135deg, #4299e1 0%, #3182ce 100%);
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 8px;
            cursor: pointer;
            font-weight: 600;
            font-size: 16px;
            transition: all 0.2s;
            width: 100%;
            margin: 10px 0;
        }
        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(66, 153, 225, 0.4);
        }
        .preview-iframe {
            width: 100%;
            border: 1px solid #e2e8f0;
            border-radius: 8px;
            margin-top: 15px;
        }
        .url-display {
            background: #f7fafc;
            border: 1px solid #e2e8f0;
            padding: 12px;
            border-radius: 8px;
            font-family: monospace;
            word-break: break-all;
            margin: 10px 0;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🔧 Embed Code Generator</h1>
            <p>Create custom embed codes for your documents and projects</p>
        </div>

        <form id="embedForm">
            <div class="form-group">
                <label for="contentType">Content Type</label>
                <select id="contentType" onchange="updateForm()">
                    <option value="document">Single Document</option>
                    <option value="project">Project (Multiple Documents)</option>
                </select>
            </div>

            <div class="form-group">
                <label for="contentId" id="contentIdLabel">Document ID</label>
                <input type="number" id="contentId" value="1" onchange="generateEmbed()" min="1">
            </div>

            <div class="form-grid">
                <div class="form-group">
                    <label for="theme">Theme</label>
                    <select id="theme" onchange="generateEmbed()">
                        <option value="default">Default</option>
                        <option value="dark">Dark</option>
                        <option value="minimal">Minimal</option>
                    </select>
                </div>

                <div class="form-group">
                    <label for="height">Height (pixels)</label>
                    <input type="number" id="height" value="600" onchange="generateEmbed()" min="300" max="1000">
                </div>
            </div>

            <div class="form-group">
                <label>Display Options</label>
                <div class="checkbox-group">
                    <input type="checkbox" id="minimal" onchange="generateEmbed()">
                    <label for="minimal">Minimal mode (compact layout)</label>
                </div>
                <div class="checkbox-group">
                    <input type="checkbox" id="hideHeader" onchange="generateEmbed()">
                    <label for="hideHeader">Hide header</label>
                </div>
            </div>
        </form>

        <div class="preview-section">
            <h3>📋 Generated Embed Code</h3>
            <div class="code-output" id="embedCode"></div>
            <button class="btn" onclick="copyEmbedCode()">Copy Embed Code</button>
        </div>

        <div class="preview-section">
            <h3>🔗 Direct URL</h3>
            <div class="url-display" id="directUrl"></div>
            <button class="btn" onclick="copyDirectUrl()">Copy Direct URL</button>
        </div>

        <div class="preview-section">
            <h3>👀 Live Preview</h3>
            <p>This is how your embed will look:</p>
            <iframe id="previewIframe" class="preview-iframe"></iframe>
        </div>
    </div>

    <script>
        // Helper function to get the base URL dynamically
        function getBaseUrl() {
            // Try to get from meta tag (can be set by server)
            const metaBaseUrl = document.querySelector('meta[name="app-url"]');
            if (metaBaseUrl) {
                return metaBaseUrl.getAttribute('content').replace(/\/$/, '');
            }
            
            // Fallback to current origin
            return window.location.origin;
        }

        function updateForm() {
            const contentType = document.getElementById('contentType').value;
            const label = document.getElementById('contentIdLabel');
            
            if (contentType === 'project') {
                label.textContent = 'Project ID';
            } else {
                label.textContent = 'Document ID';
            }
            
            generateEmbed();
        }

        function generateEmbed() {
            // Get base URL from environment or fallback to current origin
            const baseUrl = getBaseUrl();
            const contentType = document.getElementById('contentType').value;
            const contentId = document.getElementById('contentId').value;
            const theme = document.getElementById('theme').value;
            const height = document.getElementById('height').value;
            const minimal = document.getElementById('minimal').checked;
            const hideHeader = document.getElementById('hideHeader').checked;

            if (!contentId) return;

            // Build URL parameters
            let url = `${baseUrl}/embed-chat?${contentType}Id=${contentId}`;
            
            if (theme !== 'default') {
                url += `&theme=${theme}`;
            }
            
            if (minimal) {
                url += `&minimal=true`;
            }
            
            if (hideHeader) {
                url += `&hideHeader=true`;
            }

            // Generate iframe code
            const borderColor = theme === 'dark' ? '#374151' : '#e2e8f0';
            const iframeCode = `<iframe src="${url}" width="100%" height="${height}" style="border: 1px solid ${borderColor}; border-radius: 8px;" title="${contentType === 'project' ? 'Project' : 'Document'} Chat Widget"></iframe>`;

            // Update displays
            document.getElementById('embedCode').textContent = iframeCode;
            document.getElementById('directUrl').textContent = url;
            
            // Update preview
            const previewIframe = document.getElementById('previewIframe');
            previewIframe.src = url;
            previewIframe.style.height = Math.min(parseInt(height), 500) + 'px';
        }

        function copyEmbedCode() {
            const embedCode = document.getElementById('embedCode').textContent;
            navigator.clipboard.writeText(embedCode).then(() => {
                showNotification('Embed code copied to clipboard!');
            });
        }

        function copyDirectUrl() {
            const directUrl = document.getElementById('directUrl').textContent;
            navigator.clipboard.writeText(directUrl).then(() => {
                showNotification('Direct URL copied to clipboard!');
            });
        }

        function showNotification(message) {
            const notification = document.createElement('div');
            notification.style.cssText = `
                position: fixed;
                top: 20px;
                right: 20px;
                background: linear-gradient(135deg, #48bb78 0%, #38a169 100%);
                color: white;
                padding: 15px 20px;
                border-radius: 10px;
                box-shadow: 0 10px 25px rgba(0,0,0,0.2);
                z-index: 1000;
                font-weight: 500;
                animation: slideIn 0.3s ease-out;
            `;
            notification.textContent = message;
            
            document.body.appendChild(notification);
            
            setTimeout(() => {
                notification.remove();
            }, 3000);
        }

        // Initialize
        generateEmbed();
    </script>

    <style>
        @keyframes slideIn {
            from { transform: translateX(100%); opacity: 0; }
            to { transform: translateX(0); opacity: 1; }
        }
    </style>
</body>
</html> 