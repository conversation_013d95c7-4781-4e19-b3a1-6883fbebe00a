# DocChat AI

An intelligent document Q&A application powered by LlamaIndex and OpenRouter.

## Features

- Upload and parse various document formats (PDF, Word, PowerPoint, Excel, text files, images)
- Intelligent document Q&A using advanced AI
- Project-based document organization
- Real-time chat interface with streaming responses
- Google OAuth and email/password authentication via Supabase

## Prerequisites

- Node.js 18+ 
- PostgreSQL database (Supabase recommended)
- Google Cloud Console account (for OAuth)
- LlamaCloud API key
- OpenRouter API key

## Local Development Setup

### 1. Environment Configuration

Create a `.env` file in the root directory with the following variables:

```env
# Database
DATABASE_URL=postgresql://postgres:[password]@host:port/database
SUPABASE_URL=https://your-project.supabase.co
SUPABASE_SERVICE_ROLE_KEY=your-service-role-key

# Frontend (for client-side Supabase)
VITE_SUPABASE_URL=https://your-project.supabase.co
VITE_SUPABASE_ANON_KEY=your-anon-key

# AI Services
LLAMA_CLOUD_API_KEY=your-llama-cloud-key
OPENROUTER_API_KEY=your-openrouter-key

# Optional
APP_URL=http://localhost:5000
```

### 2. Install Dependencies

```bash
npm install
```

### 3. Database Setup

```bash
# Create database tables
npm run db:push
```

### 4. Development

```bash
# Start development server (serves both API and frontend on port 5000)
npm run dev
```

The application will be available at `http://localhost:5000`

### 5. Production Build

```bash
# Build for production
npm run build

# Start production server
npm start
```

## Project Structure

- `/client` - React frontend application
- `/server` - Express.js backend API
- `/shared` - Shared types and schemas
- `/dist` - Production build output

## API Endpoints

- `POST /api/documents/upload` - Upload and process documents
- `GET /api/documents` - Get user documents
- `POST /api/chat/ask` - Ask questions about documents
- `GET /api/projects` - Get user projects
- `POST /api/projects` - Create new project

All API endpoints require authentication via Bearer token.

## Authentication

The application uses Supabase for authentication with support for:
- Google OAuth
- Email/password signup/signin

See `GOOGLE_OAUTH_SETUP.md` for detailed OAuth configuration instructions. 