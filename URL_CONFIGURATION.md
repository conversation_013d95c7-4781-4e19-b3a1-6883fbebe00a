# 🌐 URL Configuration Guide

This guide explains how to properly configure URLs for local development and production deployment (Railway, Vercel, etc.).

## 🚨 **Problem Solved**

**Before**: Hardcoded `localhost:8080` URLs throughout the application
**After**: Dynamic URL configuration using environment variables

## 📋 **Environment Variables**

### **Required Environment Variables**

```env
# Backend URL (for server-side services)
APP_URL=http://localhost:8080

# Frontend URL (for client-side code)
VITE_APP_URL=http://localhost:8080
```

### **For Local Development** (.env)
```env
APP_URL=http://localhost:8080
VITE_APP_URL=http://localhost:8080
```

### **For Production Deployment** (Railway, Vercel, etc.)
```env
APP_URL=https://your-app-name.railway.app
VITE_APP_URL=https://your-app-name.railway.app
```

## 🔧 **What Was Fixed**

### **1. Frontend Components**
- ✅ `chat-interface.tsx` - Embed URL generation
- ✅ `embed-security-manager.tsx` - Embed code generation  
- ✅ `auth-provider.tsx` - OAuth redirects
- ✅ `embed-chat.tsx` - Already using relative URLs (no changes needed)

### **2. Configuration Utilities**
- ✅ Created `client/src/lib/config.ts` with:
  - `getBaseUrl()` - Dynamic URL resolution
  - `generateEmbedUrl()` - Embed URL generation
  - `generateEmbedCode()` - Iframe code generation

## 🚀 **Deployment Instructions**

### **Railway Deployment**
1. Set environment variables in Railway dashboard:
   ```
   APP_URL=https://your-app-name.railway.app
   VITE_APP_URL=https://your-app-name.railway.app
   ```

2. The app will automatically use these URLs for:
   - Embed code generation
   - OAuth redirects
   - API references
   - Cross-origin requests

## 🎯 **How It Works**

### **Frontend URL Resolution Priority:**
1. **Environment Variable**: `import.meta.env.VITE_APP_URL`
2. **Fallback**: `window.location.origin`
3. **Server Fallback**: `http://localhost:8080`

### **Backend URL Resolution:**
- Uses `process.env.APP_URL` with fallback to `http://localhost:8080`

## 🎉 **Benefits**

- ✅ **No hardcoded URLs** anywhere in the codebase
- ✅ **Easy deployment switching** - just change environment variables
- ✅ **Embed codes work correctly** in production
- ✅ **OAuth redirects properly** configured
- ✅ **Cross-origin requests** handled correctly
- ✅ **Future-proof** for any deployment platform

