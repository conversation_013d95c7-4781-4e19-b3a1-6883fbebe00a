import { documents, chatMessages, projects, userProfiles, creditUsage, type Document, type InsertDocument, type ChatMessage, type InsertChatMessage, type Project, type InsertProject, type UserProfile, type InsertUserProfile, type CreditUsage, type InsertCreditUsage, type UserProfileWithUsage } from "@shared/schema";

export interface IStorage {
  // User profile operations
  createUserProfile(profile: InsertUserProfile): Promise<UserProfile>;
  getUserProfile(userId: string): Promise<UserProfile | undefined>;
  updateUserProfile(userId: string, updates: Partial<UserProfile>): Promise<UserProfile>;
  getUserProfileWithUsage(userId: string): Promise<UserProfileWithUsage>;
  
  // Credit operations
  deductCredits(userId: string, actionType: string, actionId?: number, creditsUsed?: number): Promise<boolean>;
  getCreditUsage(userId: string): Promise<CreditUsage[]>;
  checkUserLimits(userId: string): Promise<{
    canCreateProject: boolean;
    canUploadDocument: boolean;
    canUseChat: boolean;
    creditsRemaining: number;
    subscriptionStatus: string;
  }>;
  
  // Embed domain operations
  getEmbedDomains(userId: string, projectId?: number): Promise<Array<{id: number, userId: string, projectId: number | null, domain: string, isActive: boolean, createdAt: string}>>;
  addEmbedDomain(userId: string, domain: string, projectId?: number): Promise<{id: number, userId: string, projectId: number | null, domain: string, isActive: boolean, createdAt: string}>;
  removeEmbedDomain(domainId: number, userId: string): Promise<void>;
  trackEmbedUsage(userId: string, documentId?: number, projectId?: number, domain?: string, ipAddress?: string, userAgent?: string): Promise<void>;
  
  // Project operations
  createProject(project: InsertProject): Promise<Project>;
  getProject(id: number): Promise<Project | undefined>;
  getUserProjects(userId: string): Promise<Project[]>;
  updateProjectIndex(id: number, indexId: string): Promise<Project>;
  deleteProject(id: number, userId: string): Promise<void>;
  
  // Document operations
  createDocument(document: InsertDocument): Promise<Document>;
  getDocument(id: number): Promise<Document | undefined>;
  updateDocumentStatus(id: number, status: string, data?: any): Promise<Document>;
  updateDocumentError(id: number, errorMessage: string): Promise<Document>;
  getUserDocuments(userId: string): Promise<Document[]>;
  getProjectDocuments(projectId: number): Promise<Document[]>;
  deleteDocument(id: number): Promise<boolean>;
  
  // Chat message operations
  createChatMessage(message: InsertChatMessage): Promise<ChatMessage>;
  getChatMessages(documentId: number): Promise<ChatMessage[]>;
  getProjectChatMessages(projectId: number): Promise<ChatMessage[]>;
  
  // Dashboard stats
  getDashboardStats(userId: string): Promise<{
    totalProjects: number;
    totalDocuments: number;
    totalMessages: number;
    activeProjects: number;
  }>;
}

export class MemStorage implements IStorage {
  private projects: Map<number, Project>;
  private documents: Map<number, Document>;
  private chatMessages: Map<number, ChatMessage>;
  private userProfiles: Map<string, UserProfile>;
  private creditUsageRecords: Map<number, CreditUsage>;
  private embedDomains: Map<number, {id: number, userId: string, projectId: number | null, domain: string, isActive: boolean, createdAt: string}>;
  private embedUsage: Map<number, any>;
  private currentProjectId: number;
  private currentDocumentId: number;
  private currentMessageId: number;
  private currentCreditUsageId: number;
  private currentEmbedDomainId: number;
  private currentEmbedUsageId: number;

  constructor() {
    this.projects = new Map();
    this.documents = new Map();
    this.chatMessages = new Map();
    this.userProfiles = new Map();
    this.creditUsageRecords = new Map();
    this.embedDomains = new Map();
    this.embedUsage = new Map();
    this.currentProjectId = 1;
    this.currentDocumentId = 1;
    this.currentMessageId = 1;
    this.currentCreditUsageId = 1;
    this.currentEmbedDomainId = 1;
    this.currentEmbedUsageId = 1;
  }

  // User profile operations
  async createUserProfile(insertProfile: InsertUserProfile): Promise<UserProfile> {
    const profile: UserProfile = {
      ...insertProfile,
      createdAt: new Date(),
      updatedAt: new Date(),
    };
    this.userProfiles.set(profile.id, profile);
    return profile;
  }

  async getUserProfile(userId: string): Promise<UserProfile | undefined> {
    return this.userProfiles.get(userId);
  }

  async updateUserProfile(userId: string, updates: Partial<UserProfile>): Promise<UserProfile> {
    const profile = this.userProfiles.get(userId);
    if (!profile) {
      throw new Error("User profile not found");
    }

    const updatedProfile: UserProfile = {
      ...profile,
      ...updates,
      updatedAt: new Date(),
    };

    this.userProfiles.set(userId, updatedProfile);
    return updatedProfile;
  }

  async getUserProfileWithUsage(userId: string): Promise<UserProfileWithUsage> {
    let profile = this.userProfiles.get(userId);
    
    // Create profile if it doesn't exist
    if (!profile) {
      profile = await this.createUserProfile({
        id: userId,
        email: `user${userId}@example.com`, // In real implementation, get from auth
        subscriptionStatus: "free",
        subscriptionStartDate: null,
        subscriptionEndDate: null,
        credits: 30,
        totalCreditsUsed: 0,
      });
    }

    const creditRecords = Array.from(this.creditUsageRecords.values())
      .filter(record => record.userId === userId);

    const projectsCreated = creditRecords.filter(r => r.actionType === 'project_create').length;
    const documentsUploaded = creditRecords.filter(r => r.actionType === 'document_upload').length;

    // Get current active projects to count only chat messages for existing projects
    const currentProjects = await this.getUserProjects(userId);
    const activeProjectIds = currentProjects.map(p => p.id);
    
    // Count chat messages only for current active projects
    const chatMessagesUsed = Array.from(this.chatMessages.values())
      .filter(msg => activeProjectIds.includes(msg.projectId)).length;

    return {
      ...profile,
      creditsRemaining: Math.max(0, profile.credits - profile.totalCreditsUsed),
      projectsCreated,
      documentsUploaded,
      chatMessagesUsed,
    };
  }

  // Credit operations
  async deductCredits(userId: string, actionType: string, actionId?: number, creditsUsed: number = 1): Promise<boolean> {
    const profile = await this.getUserProfile(userId);
    if (!profile) {
      throw new Error("User profile not found");
    }

    if (profile.subscriptionStatus === "pro") {
      // Pro users have unlimited credits
      return true;
    }

    const creditsRemaining = profile.credits - profile.totalCreditsUsed;
    if (creditsRemaining < creditsUsed) {
      return false; // Not enough credits
    }

    // Deduct credits
    await this.updateUserProfile(userId, {
      totalCreditsUsed: profile.totalCreditsUsed + creditsUsed,
    });

    // Record credit usage
    const creditUsageRecord: CreditUsage = {
      id: this.currentCreditUsageId++,
      userId,
      actionType,
      actionId: actionId || null,
      creditsUsed,
      timestamp: new Date(),
    };
    this.creditUsageRecords.set(creditUsageRecord.id, creditUsageRecord);

    return true;
  }

  async getCreditUsage(userId: string): Promise<CreditUsage[]> {
    return Array.from(this.creditUsageRecords.values())
      .filter(record => record.userId === userId)
      .sort((a, b) => b.timestamp.getTime() - a.timestamp.getTime());
  }

  async checkUserLimits(userId: string): Promise<{
    canCreateProject: boolean;
    canUploadDocument: boolean;
    canUseChat: boolean;
    creditsRemaining: number;
    subscriptionStatus: string;
  }> {
    const profileWithUsage = await this.getUserProfileWithUsage(userId);
    
    if (profileWithUsage.subscriptionStatus === "pro") {
      return {
        canCreateProject: true,
        canUploadDocument: true,
        canUseChat: true,
        creditsRemaining: -1, // Unlimited
        subscriptionStatus: profileWithUsage.subscriptionStatus,
      };
    }

    // Get current number of active projects (not deleted ones)
    const currentProjects = await this.getUserProjects(userId);
    const currentProjectCount = currentProjects.length;

    // Free user limitations
    return {
      canCreateProject: profileWithUsage.creditsRemaining >= 1, // Removed project creation limit for free users
      canUploadDocument: profileWithUsage.creditsRemaining >= 1, // Removed document upload limit for free users
      canUseChat: profileWithUsage.creditsRemaining >= 1,
      creditsRemaining: profileWithUsage.creditsRemaining,
      subscriptionStatus: profileWithUsage.subscriptionStatus,
    };
  }

  async createProject(insertProject: InsertProject): Promise<Project> {
    const id = this.currentProjectId++;
    const project: Project = {
      ...insertProject,
      id,
      indexId: null,
      status: "active",
      createdAt: new Date(),
      updatedAt: new Date(),
    };
    this.projects.set(id, project);
    return project;
  }

  async getProject(id: number): Promise<Project | undefined> {
    return this.projects.get(id);
  }

  async getUserProjects(userId: string): Promise<Project[]> {
    return Array.from(this.projects.values())
      .filter(project => project.userId === userId)
      .sort((a, b) => b.createdAt.getTime() - a.createdAt.getTime());
  }

  async updateProjectIndex(id: number, indexId: string): Promise<Project> {
    const project = this.projects.get(id);
    if (!project) {
      throw new Error("Project not found");
    }

    const updatedProject: Project = {
      ...project,
      indexId,
      updatedAt: new Date(),
    };

    this.projects.set(id, updatedProject);
    return updatedProject;
  }

  async deleteProject(id: number, userId: string): Promise<void> {
    const project = this.projects.get(id);
    if (!project || project.userId !== userId) {
      throw new Error("Project not found or unauthorized");
    }

    // Delete associated documents
    const documentsToDelete = Array.from(this.documents.entries())
      .filter(([_, doc]) => doc.projectId === id);
    for (const [docId, _] of documentsToDelete) {
      this.documents.delete(docId);
    }

    // Delete associated chat messages
    const messagesToDelete = Array.from(this.chatMessages.entries())
      .filter(([_, msg]) => msg.projectId === id);
    for (const [msgId, _] of messagesToDelete) {
      this.chatMessages.delete(msgId);
    }

    // Delete the project
    this.projects.delete(id);
  }

  async createDocument(insertDocument: InsertDocument): Promise<Document> {
    const id = this.currentDocumentId++;
    const document: Document = {
      ...insertDocument,
      id,
      status: "uploading",
      parsedData: null,
      summary: null,
      pageCount: null,
      wordCount: null,
      errorMessage: null,
      createdAt: new Date(),
    };
    this.documents.set(id, document);
    return document;
  }

  async getDocument(id: number): Promise<Document | undefined> {
    return this.documents.get(id);
  }

  async updateDocumentStatus(id: number, status: string, data?: any): Promise<Document> {
    const document = this.documents.get(id);
    if (!document) {
      throw new Error("Document not found");
    }

    const updatedDocument: Document = {
      ...document,
      status,
      ...(data && {
        parsedData: data.parsedData || document.parsedData,
        summary: data.summary || document.summary,
        pageCount: data.pageCount || document.pageCount,
        wordCount: data.wordCount || document.wordCount,
      }),
    };

    this.documents.set(id, updatedDocument);
    return updatedDocument;
  }

  async updateDocumentError(id: number, errorMessage: string): Promise<Document> {
    const document = this.documents.get(id);
    if (!document) {
      throw new Error("Document not found");
    }

    const updatedDocument: Document = {
      ...document,
      status: "error",
      errorMessage,
    };

    this.documents.set(id, updatedDocument);
    return updatedDocument;
  }

  async getUserDocuments(userId: string): Promise<Document[]> {
    return Array.from(this.documents.values())
      .filter(doc => doc.userId === userId)
      .sort((a, b) => b.createdAt.getTime() - a.createdAt.getTime());
  }

  async getProjectDocuments(projectId: number): Promise<Document[]> {
    return Array.from(this.documents.values())
      .filter(doc => doc.projectId === projectId)
      .sort((a, b) => b.createdAt.getTime() - a.createdAt.getTime());
  }

  async deleteDocument(id: number): Promise<boolean> {
    const exists = this.documents.has(id);
    if (exists) {
      this.documents.delete(id);
      
      // Also delete associated chat messages
      const messagesToDelete = Array.from(this.chatMessages.values())
        .filter(msg => msg.documentId === id);
      
      messagesToDelete.forEach(msg => {
        this.chatMessages.delete(msg.id);
      });
    }
    return exists;
  }

  async createChatMessage(insertMessage: InsertChatMessage): Promise<ChatMessage> {
    const id = this.currentMessageId++;
    const message: ChatMessage = {
      ...insertMessage,
      id,
      sourceReferences: insertMessage.sourceReferences || null,
      timestamp: new Date(),
    };
    this.chatMessages.set(id, message);
    return message;
  }

  async getChatMessages(documentId: number): Promise<ChatMessage[]> {
    return Array.from(this.chatMessages.values())
      .filter(message => message.documentId === documentId)
      .sort((a, b) => b.timestamp.getTime() - a.timestamp.getTime());
  }

  async getProjectChatMessages(projectId: number): Promise<ChatMessage[]> {
    return Array.from(this.chatMessages.values())
      .filter(message => message.projectId === projectId)
      .sort((a, b) => b.timestamp.getTime() - a.timestamp.getTime());
  }

  async getDashboardStats(userId: string): Promise<{
    totalProjects: number;
    totalDocuments: number;
    totalMessages: number;
    activeProjects: number;
  }> {
    const userProjects = await this.getUserProjects(userId);
    const userDocuments = await this.getUserDocuments(userId);
    const userMessages = Array.from(this.chatMessages.values()).filter(msg => {
      const project = this.projects.get(msg.projectId);
      return project?.userId === userId;
    });

    return {
      totalProjects: userProjects.length,
      totalDocuments: userDocuments.length,
      totalMessages: userMessages.length,
      activeProjects: userProjects.filter(p => p.status === 'active').length,
    };
  }

  // Embed domain operations
  async getEmbedDomains(userId: string, projectId?: number): Promise<Array<{id: number, userId: string, projectId: number | null, domain: string, isActive: boolean, createdAt: string}>> {
    return Array.from(this.embedDomains.values())
      .filter(domain => {
        if (domain.userId !== userId) return false;
        if (projectId !== undefined) {
          return domain.projectId === projectId;
        }
        return true;
      })
      .sort((a, b) => new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime());
  }

  async addEmbedDomain(userId: string, domain: string, projectId?: number): Promise<{id: number, userId: string, projectId: number | null, domain: string, isActive: boolean, createdAt: string}> {
    const id = this.currentEmbedDomainId++;
    const embedDomain = {
      id,
      userId,
      projectId: projectId || null,
      domain,
      isActive: true,
      createdAt: new Date().toISOString(),
    };
    this.embedDomains.set(id, embedDomain);
    return embedDomain;
  }

  async removeEmbedDomain(domainId: number, userId: string): Promise<void> {
    const domain = this.embedDomains.get(domainId);
    if (!domain || domain.userId !== userId) {
      throw new Error("Domain not found or unauthorized");
    }
    this.embedDomains.delete(domainId);
  }

  async trackEmbedUsage(userId: string, documentId?: number, projectId?: number, domain?: string, ipAddress?: string, userAgent?: string): Promise<void> {
    const id = this.currentEmbedUsageId++;
    const usage = {
      id,
      userId,
      documentId: documentId || null,
      projectId: projectId || null,
      domain: domain || null,
      ipAddress: ipAddress || null,
      userAgent: userAgent || null,
      requestsCount: 1,
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString(),
    };
    this.embedUsage.set(id, usage);
  }
}

// Conditional storage selection based on environment variables
const supabaseUrl = process.env.SUPABASE_URL;
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY;

let storage: IStorage;

if (supabaseUrl && supabaseServiceKey) {
  console.log("Supabase environment variables found, will attempt to use Supabase storage");
  // We'll let supabase-storage.ts handle the actual initialization
  // If it fails, the application will fall back to the error handling we added
  try {
    // Use dynamic import with proper ES module syntax
    const supabaseStorageModule = await import('./supabase-storage.js');
    storage = supabaseStorageModule.supabaseStorage;
    console.log("Supabase storage loaded successfully");
  } catch (error) {
    console.error("Failed to load Supabase storage, using in-memory storage:", error);
    storage = new MemStorage();
  }
} else {
  console.warn("Supabase environment variables not found, using in-memory storage");
  console.warn("For production use, please configure SUPABASE_URL and SUPABASE_SERVICE_ROLE_KEY in Railway");
  storage = new MemStorage();
  console.log("In-memory storage initialized");
}

export { storage };
