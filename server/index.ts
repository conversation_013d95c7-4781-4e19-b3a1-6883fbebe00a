// Import environment variables first, before any other imports
import "./env.ts";

import express, { type Request, Response, NextFunction } from "express";
import { registerRoutes } from "./routes";
import { setupVite, serveStatic, log } from "./vite";

const app = express();
app.use(express.json());
app.use(express.urlencoded({ extended: false }));

// Health check endpoint for Railway

app.get("/health", (req, res) => res.json({ status: "healthy", timestamp: new Date().toISOString() }));

/**
 * Enhanced error sanitization to prevent information disclosure
 */
function sanitizeErrorMessage(error: any): string {
  const isProduction = process.env.NODE_ENV === 'production';
  
  // Secure error logging - different levels for dev vs prod
  if (isProduction) {
    // In production, only log basic error occurrence without details
    console.error(`Application error occurred: ${error instanceof Error ? error.constructor.name : 'Unknown'}`);
  } else {
    // In development, log more details for debugging
    console.error("Application error:", error instanceof Error ? error.message : error);
  }
  
  // In production, return generic messages to prevent information disclosure
  if (isProduction) {
    // Check for safe error patterns that can be shown in production
    if (error?.message) {
      const message = error.message.toLowerCase();
      
      if (message.includes('not found') || 
          message.includes('access denied') ||
          message.includes('unauthorized') ||
          message.includes('validation') ||
          message.includes('invalid') ||
          message.includes('rate limit')) {
        return error.message;
      }
    }
    
    // Default generic message for production
    return "An internal error occurred. Please try again later.";
  }
  
  // Development mode - show more details for debugging
  if (error?.message) {
    const message = error.message;
    
    // Still sanitize sensitive data even in development
    if (message.includes("Bearer") || 
        message.includes("token") || 
        message.includes("key") ||
        message.includes("secret") ||
        message.includes("password")) {
      return "Service error (sensitive data hidden)";
    }
    
    // Check for common error patterns that should be sanitized
    if (message.includes("API error") || 
        message.includes("fetch failed") ||
        message.toLowerCase().includes("llamaparse") ||
        message.toLowerCase().includes("openrouter") ||
        message.toLowerCase().includes("llamaindex")) {
      return "External service temporarily unavailable. Please try again later.";
    }
    
    return message;
  }
  
  return "Internal Server Error";
}

app.use((req, res, next) => {
  const start = Date.now();
  const path = req.path;
  let capturedJsonResponse: Record<string, any> | undefined = undefined;

  const originalResJson = res.json;
  res.json = function (bodyJson, ...args) {
    capturedJsonResponse = bodyJson;
    return originalResJson.apply(res, [bodyJson, ...args]);
  };

  res.on("finish", () => {
    const duration = Date.now() - start;
    if (path.startsWith("/api")) {
      let logLine = `${req.method} ${path} ${res.statusCode} in ${duration}ms`;
      if (capturedJsonResponse) {
        logLine += ` :: ${JSON.stringify(capturedJsonResponse)}`;
      }

      if (logLine.length > 80) {
        logLine = logLine.slice(0, 79) + "…";
      }

      log(logLine);
    }
  });

  next();
});

(async () => {
  const server = await registerRoutes(app);

  app.use((err: any, _req: Request, res: Response, _next: NextFunction) => {
    const status = err.status || err.statusCode || 500;
    const message = sanitizeErrorMessage(err);

    res.status(status).json({ message });
    throw err;
  });

  // importantly only setup vite in development and after
  // setting up all the other routes so the catch-all route
  // doesn't interfere with the other routes
  if (process.env.NODE_ENV === "development") {
    await setupVite(app, server);
  } else {
    serveStatic(app);
  }

  // Railway provides PORT via environment variable
  // Use Railway's PORT or fallback to 8080 for local development
  const port = parseInt(process.env.PORT || "8080");
  server.listen({
    port,
    host: "0.0.0.0",
    reusePort: true,
  }, () => {
    log(`serving on port ${port}`);
  });
})(); 
