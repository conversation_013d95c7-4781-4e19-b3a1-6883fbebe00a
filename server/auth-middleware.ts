import { Request, Response, NextFunction } from 'express'
import { createClient } from '@supabase/supabase-js'

const supabaseUrl = process.env.SUPABASE_URL
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY

// Make Supabase optional for Railway health checks and basic functionality
let supabase: any = null

if (supabaseUrl && supabaseServiceKey) {
  supabase = createClient(supabaseUrl, supabaseServiceKey)
  
  // Secure logging - only log success without exposing config details
  if (process.env.NODE_ENV !== 'production') {
    console.log("Supabase client initialized successfully")
  }
} else {
  // Only warn in development to avoid log noise in production
  if (process.env.NODE_ENV !== 'production') {
    console.warn("WARNING: Supabase configuration missing. Authentication will be disabled.")
    console.warn("For full functionality, please configure Supabase environment variables.")
  }
}

export interface AuthenticatedRequest extends Request {
  user?: {
    id: string
    email: string
  }
}

export async function authenticateUser(req: AuthenticatedRequest, res: Response, next: NextFunction) {
  try {
    // If Supabase is not configured, allow access with a default user for development/demo
    if (!supabase) {
      if (process.env.NODE_ENV !== 'production') {
        console.warn("Supabase not configured - using demo user for request")
      }
      req.user = {
        id: 'demo-user-' + Date.now(),
        email: '<EMAIL>'
      }
      return next()
    }

    const authHeader = req.headers.authorization
    if (!authHeader?.startsWith('Bearer ')) {
      return res.status(401).json({ message: 'No authorization token provided' })
    }

    const token = authHeader.split(' ')[1]
    
    const { data: { user }, error } = await supabase.auth.getUser(token)
    
    if (error || !user) {
      return res.status(401).json({ message: 'Invalid or expired token' })
    }

    req.user = {
      id: user.id,
      email: user.email!
    }
    
    next()
  } catch (error) {
    // Secure error logging - don't expose sensitive auth details
    if (process.env.NODE_ENV !== 'production') {
      console.error('Authentication error:', error instanceof Error ? error.message : 'Unknown error')
    }
    res.status(401).json({ message: 'Authentication failed' })
  }
}