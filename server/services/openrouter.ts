interface OpenRouterResponse {
  choices: Array<{
    message: {
      content: string;
    };
  }>;
  usage?: {
    prompt_tokens: number;
    completion_tokens: number;
    total_tokens: number;
  };
}

interface OpenRouterStreamResponse {
  choices: Array<{
    delta: {
      content?: string;
    };
  }>;
}

export class OpenRouterService {
  private apiKey: string;
  private baseUrl = "https://openrouter.ai/api/v1";
  private isConfigured: boolean;

  constructor() {
    this.apiKey = process.env.OPENROUTER_API_KEY || "";
    this.isConfigured = !!this.apiKey;
    
    if (!this.apiKey) {
      console.warn("WARNING: OPENROUTER_API_KEY not configured. Chat functionality will be disabled.");
      console.warn("For full functionality, please set OPENROUTER_API_KEY in Railway environment variables.");
    } else {
      console.log("OpenRouterService initialized successfully");
    }
  }

  private checkConfiguration(): void {
    if (!this.isConfigured) {
      throw new Error("Chat service is currently unavailable. Please try again later.");
    }
  }

  /**
   * Sanitizes third-party API errors to prevent exposing sensitive information
   */
  private sanitizeError(error: any, context: string): Error {
    // Log the actual error for debugging
    console.error(`OpenRouter ${context} error:`, error);
    
    // Return a generic user-friendly message
    if (error?.message?.includes('timeout') || error?.name === 'AbortError') {
      return new Error("The chat service is taking longer than expected. Please try again.");
    }
    
    if (error?.message?.includes('401') || error?.message?.includes('403')) {
      return new Error("Chat service is temporarily unavailable. Please try again later.");
    }
    
    if (error?.message?.includes('429')) {
      return new Error("Chat service is busy. Please try again in a few moments.");
    }
    
    if (error?.message?.includes('500') || error?.message?.includes('502') || error?.message?.includes('503')) {
      return new Error("Chat service is temporarily unavailable. Please try again later.");
    }
    
    // Generic fallback message
    return new Error("Unable to generate response. Please try again.");
  }

  private buildPayload(prompt: string, context: string) {
    const finalPrompt = `You are a helpful assistant. Based on the following context, answer the user's question.

Context:
${context}

User's Question:
${prompt}
`;

    return {
      model: "deepseek/deepseek-chat-v3-0324:free",
      messages: [
        {
          role: "user",
          content: finalPrompt,
        },
      ],
      temperature: 0.7,
      max_tokens: 1000,
    };
  }

  private getHeaders() {
    return {
      "Authorization": `Bearer ${this.apiKey}`,
      "Content-Type": "application/json",
      "HTTP-Referer": process.env.APP_URL || "http://localhost:5000",
      "X-Title": "DocChat AI",
    };
  }

  async generateResponse(prompt: string, context: string): Promise<string> {
    this.checkConfiguration();
    
    const payload = this.buildPayload(prompt, context);
    const controller = new AbortController();
    const timeoutId = setTimeout(() => controller.abort(), 10000); // 10 second timeout

    try {
      const response = await fetch(`${this.baseUrl}/chat/completions`, {
        method: "POST",
        headers: this.getHeaders(),
        body: JSON.stringify(payload),
        signal: controller.signal,
      });
      
      clearTimeout(timeoutId);

      if (!response.ok) {
        const errorText = await response.text();
        console.error(`OpenRouter API error: ${response.status} - ${errorText}`);
        throw this.sanitizeError({ status: response.status, message: errorText }, "generateResponse");
      }

      const data: OpenRouterResponse = await response.json();
      
      if (!data.choices || data.choices.length === 0) {
        throw new Error("Unable to generate response. Please try again.");
      }

      return data.choices[0].message.content;
    } catch (error) {
      clearTimeout(timeoutId);
      if (error instanceof Error && error.message.includes("Chat service") || error.message.includes("Unable to generate")) {
        // Already sanitized
        throw error;
      }
      throw this.sanitizeError(error, "generateResponse");
    }
  }

  async *generateStreamResponse(prompt: string, context: string): AsyncGenerator<string> {
    this.checkConfiguration();
    
    const payload = {
      ...this.buildPayload(prompt, context),
      stream: true
    };

    const controller = new AbortController();
    const timeoutId = setTimeout(() => controller.abort(), 60000); // Increased to 60 second timeout

    try {
      console.log('OpenRouter streaming request started');
      const response = await fetch(`${this.baseUrl}/chat/completions`, {
        method: "POST",
        headers: this.getHeaders(),
        body: JSON.stringify(payload),
        signal: controller.signal,
      });

      clearTimeout(timeoutId);

      if (!response.ok) {
        const errorText = await response.text();
        console.error(`OpenRouter streaming API error: ${response.status} - ${errorText}`);
        throw this.sanitizeError({ status: response.status, message: errorText }, "generateStreamResponse");
      }

      if (!response.body) {
        throw new Error("Unable to start streaming response. Please try again.");
      }

      const reader = response.body.getReader();
      const decoder = new TextDecoder();
      let buffer = '';
      let totalContent = '';
      let lastChunkTime = Date.now();
      let openRouterDone = false;

      try {
        while (true) {
          const { done, value } = await reader.read();
          if (done) {
            console.log('OpenRouter stream ended by reader.done');
            break;
          }

          lastChunkTime = Date.now();
          buffer += decoder.decode(value, { stream: true });
          
          const lines = buffer.split('\n');
          buffer = lines.pop() || '';

          for (const line of lines) {
            if (line.startsWith('data:')) {
              const dataContent = line.slice(5).trim();
              
              if (dataContent === '[DONE]') {
                console.log('OpenRouter sent [DONE] signal');
                openRouterDone = true;
                break;
              }
              
              try {
                const data: OpenRouterStreamResponse = JSON.parse(dataContent);
                if (data.choices?.[0]?.delta?.content) {
                  const content = data.choices[0].delta.content;
                  totalContent += content;
                  yield content;
                }
              } catch (e) {
                console.error('Error parsing stream data:', line, e);
                // Continue processing other lines instead of failing completely
              }
            }
          }
          
          if (openRouterDone) {
            break;
          }
        }
        
        console.log(`OpenRouter streaming completed. Total content length: ${totalContent.length}, OpenRouter done signal: ${openRouterDone}`);
        
        // Check if the response seems incomplete (ends mid-sentence or word)
        if (totalContent.length > 0 && !openRouterDone) {
          const lastWords = totalContent.trim().split(' ').slice(-3).join(' ');
          console.warn(`OpenRouter stream ended without [DONE] signal. Last content: "${lastWords}"`);
          
          // If it looks like it ended mid-sentence, this might be an incomplete response
          if (totalContent.trim().length > 10 && 
              !totalContent.trim().match(/[.!?]$/) && 
              !totalContent.trim().endsWith('**') &&
              !totalContent.trim().endsWith(':')) {
            console.warn('Response appears to be incomplete - ended mid-sentence');
          }
        }
        
      } finally {
        reader.releaseLock();
      }
    } catch (error) {
      clearTimeout(timeoutId);
      if (error instanceof Error && (error.message.includes("Chat service") || error.message.includes("Unable to"))) {
        // Already sanitized
        throw error;
      }
      throw this.sanitizeError(error, "generateStreamResponse");
    }
  }
}

export const openRouterService = new OpenRouterService();
