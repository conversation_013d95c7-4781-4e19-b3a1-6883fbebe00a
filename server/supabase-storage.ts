import { createClient } from '@supabase/supabase-js'
import { documents, chatMessages, projects, userProfiles, creditUsage, type Document, type InsertDocument, type ChatMessage, type InsertChatMessage, type Project, type InsertProject, type UserProfile, type InsertUserProfile, type CreditUsage, type InsertCreditUsage, type UserProfileWithUsage } from "@shared/schema"
import type { IStorage } from './storage'

const supabaseUrl = process.env.SUPABASE_URL
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY

// Only create supabase client if environment variables are available
let supabase: any = null;

if (supabaseUrl && supabaseServiceKey) {
  supabase = createClient(supabaseUrl, supabaseServiceKey);
  console.log("Supabase client created successfully");
} else {
  console.warn("SUPABASE_URL and SUPABASE_SERVICE_ROLE_KEY not configured");
}

export class SupabaseStorage implements IStorage {
  private checkSupabaseAvailable(): void {
    if (!supabase) {
      throw new Error("Supabase is not configured. Please set SUPABASE_URL and SUPABASE_SERVICE_ROLE_KEY environment variables.");
    }
  }

  // User profile operations
  async createUserProfile(profile: InsertUserProfile): Promise<UserProfile> {
    this.checkSupabaseAvailable();
    
    const { data, error } = await supabase
      .from('user_profiles')
      .insert([{
        id: profile.id,
        email: profile.email,
        subscription_status: profile.subscriptionStatus || 'free',
        subscription_start_date: profile.subscriptionStartDate || null,
        subscription_end_date: profile.subscriptionEndDate || null,
        credits: profile.credits || 7,
        total_credits_used: profile.totalCreditsUsed || 0,
      }])
      .select()
      .single()

    if (error) {
      throw new Error(`Failed to create user profile: ${error.message}`)
    }

    return {
      id: data.id,
      email: data.email,
      subscriptionStatus: data.subscription_status,
      subscriptionStartDate: data.subscription_start_date ? new Date(data.subscription_start_date) : null,
      subscriptionEndDate: data.subscription_end_date ? new Date(data.subscription_end_date) : null,
      credits: data.credits,
      totalCreditsUsed: data.total_credits_used,
      createdAt: new Date(data.created_at),
      updatedAt: new Date(data.updated_at),
    }
  }

  async getUserProfile(userId: string): Promise<UserProfile | undefined> {
    this.checkSupabaseAvailable();
    
    const { data, error } = await supabase
      .from('user_profiles')
      .select('*')
      .eq('id', userId)
      .single()

    if (error) {
      if (error.code === 'PGRST116') { // Record not found
        return undefined
      }
      throw new Error(`Failed to get user profile: ${error.message}`)
    }

    return {
      id: data.id,
      email: data.email,
      subscriptionStatus: data.subscription_status,
      subscriptionStartDate: data.subscription_start_date ? new Date(data.subscription_start_date) : null,
      subscriptionEndDate: data.subscription_end_date ? new Date(data.subscription_end_date) : null,
      credits: data.credits,
      totalCreditsUsed: data.total_credits_used,
      createdAt: new Date(data.created_at),
      updatedAt: new Date(data.updated_at),
    }
  }

  async updateUserProfile(userId: string, updates: Partial<UserProfile>): Promise<UserProfile> {
    const updateData: any = {
      updated_at: new Date().toISOString(),
    }
    
    if (updates.email !== undefined) updateData.email = updates.email
    if (updates.subscriptionStatus !== undefined) updateData.subscription_status = updates.subscriptionStatus
    if (updates.subscriptionStartDate !== undefined) updateData.subscription_start_date = updates.subscriptionStartDate?.toISOString()
    if (updates.subscriptionEndDate !== undefined) updateData.subscription_end_date = updates.subscriptionEndDate?.toISOString()
    if (updates.credits !== undefined) updateData.credits = updates.credits
    if (updates.totalCreditsUsed !== undefined) updateData.total_credits_used = updates.totalCreditsUsed

    const { data, error } = await supabase
      .from('user_profiles')
      .update(updateData)
      .eq('id', userId)
      .select()
      .single()

    if (error) {
      throw new Error(`Failed to update user profile: ${error.message}`)
    }

    return {
      id: data.id,
      email: data.email,
      subscriptionStatus: data.subscription_status,
      subscriptionStartDate: data.subscription_start_date ? new Date(data.subscription_start_date) : null,
      subscriptionEndDate: data.subscription_end_date ? new Date(data.subscription_end_date) : null,
      credits: data.credits,
      totalCreditsUsed: data.total_credits_used,
      createdAt: new Date(data.created_at),
      updatedAt: new Date(data.updated_at),
    }
  }

  async getUserProfileWithUsage(userId: string): Promise<UserProfileWithUsage> {
    let profile = await this.getUserProfile(userId)
    
    // Create profile if it doesn't exist
    if (!profile) {
      // Get user email from Supabase auth
      const { data: { user }, error: userError } = await supabase.auth.admin.getUserById(userId)
      const userEmail = user?.email || `user${userId}@example.com`
      
      profile = await this.createUserProfile({
        id: userId,
        email: userEmail,
        subscriptionStatus: "free",
        subscriptionStartDate: null,
        subscriptionEndDate: null,
        credits: 30,
        totalCreditsUsed: 0,
      })
    }

    // Get credit usage statistics
    const { data: creditData, error: creditError } = await supabase
      .from('credit_usage')
      .select('action_type')
      .eq('user_id', userId)

    if (creditError) {
      throw new Error(`Failed to get credit usage: ${creditError.message}`)
    }

    const projectsCreated = creditData?.filter(r => r.action_type === 'project_create').length || 0
    const documentsUploaded = creditData?.filter(r => r.action_type === 'document_upload').length || 0

    // Get current active projects to count only chat messages for existing projects
    const currentProjects = await this.getUserProjects(userId)
    const activeProjectIds = currentProjects.map(p => p.id)
    
    // Count chat messages only for current active projects
    let chatMessagesUsed = 0
    if (activeProjectIds.length > 0) {
      const { data: chatData, error: chatError } = await supabase
        .from('chat_messages')
        .select('id')
        .in('project_id', activeProjectIds)

      if (chatError) {
        throw new Error(`Failed to get chat messages: ${chatError.message}`)
      }
      chatMessagesUsed = chatData?.length || 0
    }

    return {
      ...profile,
      creditsRemaining: Math.max(0, profile.credits - profile.totalCreditsUsed),
      projectsCreated,
      documentsUploaded,
      chatMessagesUsed,
    }
  }

  // Credit operations
  async deductCredits(userId: string, actionType: string, actionId?: number, creditsUsed: number = 1): Promise<boolean> {
    const profile = await this.getUserProfile(userId)
    if (!profile) {
      throw new Error("User profile not found")
    }

    if (profile.subscriptionStatus === "pro") {
      // Pro users have unlimited credits
      return true
    }

    const creditsRemaining = profile.credits - profile.totalCreditsUsed
    if (creditsRemaining < creditsUsed) {
      return false // Not enough credits
    }

    // Use a transaction to ensure atomicity
    const { error: transactionError } = await supabase.rpc('deduct_user_credits', {
      p_user_id: userId,
      p_action_type: actionType,
      p_action_id: actionId,
      p_credits_used: creditsUsed
    })

    if (transactionError) {
      // If the function doesn't exist, fall back to manual implementation
      console.warn('RPC function not found, using manual implementation')
      
      // Update user profile
      await this.updateUserProfile(userId, {
        totalCreditsUsed: profile.totalCreditsUsed + creditsUsed,
      })

      // Record credit usage
      const { error: insertError } = await supabase
        .from('credit_usage')
        .insert([{
          user_id: userId,
          action_type: actionType,
          action_id: actionId,
          credits_used: creditsUsed,
        }])

      if (insertError) {
        throw new Error(`Failed to record credit usage: ${insertError.message}`)
      }
    }

    return true
  }

  async getCreditUsage(userId: string): Promise<CreditUsage[]> {
    const { data, error } = await supabase
      .from('credit_usage')
      .select('*')
      .eq('user_id', userId)
      .order('timestamp', { ascending: false })

    if (error) {
      throw new Error(`Failed to get credit usage: ${error.message}`)
    }

    return data.map(record => ({
      id: record.id,
      userId: record.user_id,
      actionType: record.action_type,
      actionId: record.action_id,
      creditsUsed: record.credits_used,
      timestamp: new Date(record.timestamp),
    }))
  }

  async checkUserLimits(userId: string): Promise<{
    canCreateProject: boolean;
    canUploadDocument: boolean;
    canUseChat: boolean;
    creditsRemaining: number;
    subscriptionStatus: string;
  }> {
    const profileWithUsage = await this.getUserProfileWithUsage(userId)
    
    if (profileWithUsage.subscriptionStatus === "pro") {
      return {
        canCreateProject: true,
        canUploadDocument: true,
        canUseChat: true,
        creditsRemaining: -1, // Unlimited
        subscriptionStatus: profileWithUsage.subscriptionStatus,
      }
    }

    // Get current number of active projects (not deleted ones)
    const currentProjects = await this.getUserProjects(userId)
    const currentProjectCount = currentProjects.length

    // Free user limitations
    return {
      canCreateProject: profileWithUsage.creditsRemaining >= 1, // Removed project creation limit for free users
      canUploadDocument: profileWithUsage.creditsRemaining >= 1, // Removed document upload limit for free users
      canUseChat: profileWithUsage.creditsRemaining >= 1,
      creditsRemaining: profileWithUsage.creditsRemaining,
      subscriptionStatus: profileWithUsage.subscriptionStatus,
    }
  }

  // Project operations
  async createProject(project: InsertProject): Promise<Project> {
    const { data, error } = await supabase
      .from('projects')
      .insert([{
        name: project.name,
        description: project.description,
        user_id: project.userId,
        index_id: null,
        status: 'active',
      }])
      .select()
      .single()

    if (error) {
      throw new Error(`Failed to create project: ${error.message}`)
    }

    return {
      id: data.id,
      name: data.name,
      description: data.description,
      userId: data.user_id,
      indexId: data.index_id,
      status: data.status,
      createdAt: new Date(data.created_at),
      updatedAt: new Date(data.updated_at),
    }
  }

  async getProject(id: number): Promise<Project | undefined> {
    const { data, error } = await supabase
      .from('projects')
      .select('*')
      .eq('id', id)
      .single()

    if (error) {
      if (error.code === 'PGRST116') return undefined // Not found
      throw new Error(`Failed to get project: ${error.message}`)
    }

    return {
      id: data.id,
      name: data.name,
      description: data.description,
      userId: data.user_id,
      indexId: data.index_id,
      status: data.status,
      createdAt: new Date(data.created_at),
      updatedAt: new Date(data.updated_at),
    }
  }

  async getUserProjects(userId: string): Promise<Project[]> {
    const { data, error } = await supabase
      .from('projects')
      .select('*')
      .eq('user_id', userId)
      .order('created_at', { ascending: false })

    if (error) {
      throw new Error(`Failed to get user projects: ${error.message}`)
    }

    return data.map(project => ({
      id: project.id,
      name: project.name,
      description: project.description,
      userId: project.user_id,
      indexId: project.index_id,
      status: project.status,
      createdAt: new Date(project.created_at),
      updatedAt: new Date(project.updated_at),
    }))
  }

  async updateProjectIndex(id: number, indexId: string): Promise<Project> {
    const { data, error } = await supabase
      .from('projects')
      .update({
        index_id: indexId,
        updated_at: new Date().toISOString(),
      })
      .eq('id', id)
      .select()
      .single()

    if (error) {
      throw new Error(`Failed to update project index: ${error.message}`)
    }

    return {
      id: data.id,
      name: data.name,
      description: data.description,
      userId: data.user_id,
      indexId: data.index_id,
      status: data.status,
      createdAt: new Date(data.created_at),
      updatedAt: new Date(data.updated_at),
    }
  }

  async deleteProject(id: number, userId: string): Promise<void> {
    // First verify the project exists and belongs to the user
    const { data: project, error: projectError } = await supabase
      .from('projects')
      .select('id, user_id')
      .eq('id', id)
      .eq('user_id', userId)
      .single()

    if (projectError) {
      if (projectError.code === 'PGRST116') {
        throw new Error("Project not found or unauthorized")
      }
      throw new Error(`Failed to verify project: ${projectError.message}`)
    }

    // Delete associated chat messages first (due to foreign key constraints)
    const { error: chatError } = await supabase
      .from('chat_messages')
      .delete()
      .eq('project_id', id)

    if (chatError) {
      throw new Error(`Failed to delete chat messages: ${chatError.message}`)
    }

    // Delete associated documents
    const { error: docsError } = await supabase
      .from('documents')
      .delete()
      .eq('project_id', id)

    if (docsError) {
      throw new Error(`Failed to delete documents: ${docsError.message}`)
    }

    // Finally delete the project
    const { error: deleteError } = await supabase
      .from('projects')
      .delete()
      .eq('id', id)
      .eq('user_id', userId)

    if (deleteError) {
      throw new Error(`Failed to delete project: ${deleteError.message}`)
    }
  }

  // Document operations
  async createDocument(document: InsertDocument): Promise<Document> {
    const { data, error } = await supabase
      .from('documents')
      .insert([{
        project_id: document.projectId,
        user_id: document.userId,
        filename: document.filename,
        filesize: document.filesize,
        content_type: document.contentType,
        status: 'uploading',
        parsed_data: null,
        page_count: null,
        word_count: null,
        error_message: null,
      }])
      .select()
      .single()

    if (error) {
      throw new Error(`Failed to create document: ${error.message}`)
    }

    return {
      id: data.id,
      projectId: data.project_id,
      userId: data.user_id,
      filename: data.filename,
      filesize: data.filesize,
      contentType: data.content_type,
      status: data.status,
      parsedData: data.parsed_data,
      summary: data.summary,
      pageCount: data.page_count,
      wordCount: data.word_count,
      errorMessage: data.error_message,
      createdAt: new Date(data.created_at),
    }
  }

  async getDocument(id: number): Promise<Document | undefined> {
    const { data, error } = await supabase
      .from('documents')
      .select('*')
      .eq('id', id)
      .single()

    if (error) {
      if (error.code === 'PGRST116') return undefined // Not found
      throw new Error(`Failed to get document: ${error.message}`)
    }

    return {
      id: data.id,
      projectId: data.project_id,
      userId: data.user_id,
      filename: data.filename,
      filesize: data.filesize,
      contentType: data.content_type,
      status: data.status,
      parsedData: data.parsed_data,
      pageCount: data.page_count,
      wordCount: data.word_count,
      errorMessage: data.error_message,
      createdAt: new Date(data.created_at),
    }
  }

  async updateDocumentStatus(id: number, status: string, data?: any): Promise<Document> {
    const updateData: any = { status }
    
    if (data?.parsedData) updateData.parsed_data = data.parsedData
    if (data?.pageCount) updateData.page_count = data.pageCount
    if (data?.wordCount) updateData.word_count = data.wordCount
    if (data?.summary) updateData.summary = data.summary

    const { data: result, error } = await supabase
      .from('documents')
      .update(updateData)
      .eq('id', id)
      .select()
      .single()

    if (error) {
      throw new Error(`Failed to update document status: ${error.message}`)
    }

    return {
      id: result.id,
      projectId: result.project_id,
      userId: result.user_id,
      filename: result.filename,
      filesize: result.filesize,
      contentType: result.content_type,
      status: result.status,
      parsedData: result.parsed_data,
      summary: result.summary,
      pageCount: result.page_count,
      wordCount: result.word_count,
      errorMessage: result.error_message,
      createdAt: new Date(result.created_at),
    }
  }

  async updateDocumentError(id: number, errorMessage: string): Promise<Document> {
    const { data, error } = await supabase
      .from('documents')
      .update({
        status: 'error',
        error_message: errorMessage,
      })
      .eq('id', id)
      .select()
      .single()

    if (error) {
      throw new Error(`Failed to update document error: ${error.message}`)
    }

    return {
      id: data.id,
      projectId: data.project_id,
      userId: data.user_id,
      filename: data.filename,
      filesize: data.filesize,
      contentType: data.content_type,
      status: data.status,
      parsedData: data.parsed_data,
      summary: data.summary,
      pageCount: data.page_count,
      wordCount: data.word_count,
      errorMessage: data.error_message,
      createdAt: new Date(data.created_at),
    }
  }

  async getUserDocuments(userId: string): Promise<Document[]> {
    const { data, error } = await supabase
      .from('documents')
      .select('*')
      .eq('user_id', userId)
      .order('created_at', { ascending: false })

    if (error) {
      throw new Error(`Failed to get user documents: ${error.message}`)
    }

    return data.map(doc => ({
      id: doc.id,
      projectId: doc.project_id,
      userId: doc.user_id,
      filename: doc.filename,
      filesize: doc.filesize,
      contentType: doc.content_type,
      status: doc.status,
      parsedData: doc.parsed_data,
      summary: doc.summary,
      pageCount: doc.page_count,
      wordCount: doc.word_count,
      errorMessage: doc.error_message,
      createdAt: new Date(doc.created_at),
    }))
  }

  async deleteDocument(id: number): Promise<boolean> {
    // First delete associated chat messages
    const { error: chatError } = await supabase
      .from('chat_messages')
      .delete()
      .eq('document_id', id)

    if (chatError) {
      throw new Error(`Failed to delete associated chat messages: ${chatError.message}`)
    }

    // Then delete the document
    const { error } = await supabase
      .from('documents')
      .delete()
      .eq('id', id)

    if (error) {
      throw new Error(`Failed to delete document: ${error.message}`)
    }

    return true;
  }

  // Chat message operations
  async createChatMessage(message: InsertChatMessage): Promise<ChatMessage> {
    const { data, error } = await supabase
      .from('chat_messages')
      .insert([{
        project_id: message.projectId,
        document_id: message.documentId,
        question: message.question,
        response: message.response,
        source_references: message.sourceReferences,
      }])
      .select()
      .single()

    if (error) {
      throw new Error(`Failed to create chat message: ${error.message}`)
    }

    return {
      id: data.id,
      projectId: data.project_id,
      documentId: data.document_id,
      question: data.question,
      response: data.response,
      sourceReferences: data.source_references,
      timestamp: new Date(data.timestamp),
    }
  }

  async getChatMessages(documentId: number): Promise<ChatMessage[]> {
    const { data, error } = await supabase
      .from('chat_messages')
      .select('*')
      .eq('document_id', documentId)
      .order('timestamp', { ascending: false })

    if (error) {
      throw new Error(`Failed to get chat messages: ${error.message}`)
    }

    return data.map(msg => ({
      id: msg.id,
      projectId: msg.project_id,
      documentId: msg.document_id,
      question: msg.question,
      response: msg.response,
      sourceReferences: msg.source_references,
      timestamp: new Date(msg.timestamp),
    }))
  }

  async getProjectDocuments(projectId: number): Promise<Document[]> {
    const { data, error } = await supabase
      .from('documents')
      .select('*')
      .eq('project_id', projectId)
      .order('created_at', { ascending: false })

    if (error) {
      throw new Error(`Failed to get project documents: ${error.message}`)
    }

    return data.map(doc => ({
      id: doc.id,
      projectId: doc.project_id,
      userId: doc.user_id,
      filename: doc.filename,
      filesize: doc.filesize,
      contentType: doc.content_type,
      status: doc.status,
      parsedData: doc.parsed_data,
      summary: doc.summary,
      pageCount: doc.page_count,
      wordCount: doc.word_count,
      errorMessage: doc.error_message,
      createdAt: new Date(doc.created_at),
    }))
  }

  async getProjectChatMessages(projectId: number): Promise<ChatMessage[]> {
    const { data, error } = await supabase
      .from('chat_messages')
      .select('*')
      .eq('project_id', projectId)
      .order('timestamp', { ascending: false })

    if (error) {
      throw new Error(`Failed to get project chat messages: ${error.message}`)
    }

    return data.map(msg => ({
      id: msg.id,
      projectId: msg.project_id,
      documentId: msg.document_id,
      question: msg.question,
      response: msg.response,
      sourceReferences: msg.source_references,
      timestamp: new Date(msg.timestamp),
    }))
  }

  async getDashboardStats(userId: string): Promise<{
    totalProjects: number;
    totalDocuments: number;
    totalMessages: number;
    activeProjects: number;
  }> {
    // Get user projects count
    const { data: projectsData, error: projectsError } = await supabase
      .from('projects')
      .select('id, status')
      .eq('user_id', userId)

    if (projectsError) {
      throw new Error(`Failed to get projects for stats: ${projectsError.message}`)
    }

    // Get user documents count
    const { data: documentsData, error: documentsError } = await supabase
      .from('documents')
      .select('id')
      .eq('user_id', userId)

    if (documentsError) {
      throw new Error(`Failed to get documents for stats: ${documentsError.message}`)
    }

    // Get user messages count (through projects)
    const projectIds = projectsData.map(p => p.id)
    let messagesData: any[] = []
    
    if (projectIds.length > 0) {
      const { data, error: messagesError } = await supabase
        .from('chat_messages')
        .select('id')
        .in('project_id', projectIds)

      if (messagesError) {
        throw new Error(`Failed to get messages for stats: ${messagesError.message}`)
      }
      messagesData = data || []
    }

    return {
      totalProjects: projectsData.length,
      totalDocuments: documentsData.length,
      totalMessages: messagesData.length,
      activeProjects: projectsData.filter(p => p.status === 'active').length,
    }
  }

  // Embed domain operations
  async getEmbedDomains(userId: string, projectId?: number): Promise<Array<{id: number, userId: string, projectId: number | null, domain: string, isActive: boolean, createdAt: string}>> {
    let query = supabase
      .from('embed_domains')
      .select('*')
      .eq('user_id', userId)

    if (projectId !== undefined) {
      query = query.eq('project_id', projectId)
    }

    const { data, error } = await query.order('created_at', { ascending: false })

    if (error) {
      throw new Error(`Failed to get embed domains: ${error.message}`)
    }

    return data.map(domain => ({
      id: domain.id,
      userId: domain.user_id,
      projectId: domain.project_id,
      domain: domain.domain,
      isActive: domain.is_active,
      createdAt: domain.created_at,
    }))
  }

  async addEmbedDomain(userId: string, domain: string, projectId?: number): Promise<{id: number, userId: string, projectId: number | null, domain: string, isActive: boolean, createdAt: string}> {
    const { data, error } = await supabase
      .from('embed_domains')
      .insert([{
        user_id: userId,
        project_id: projectId || null,
        domain: domain,
        is_active: true,
      }])
      .select()
      .single()

    if (error) {
      throw new Error(`Failed to add embed domain: ${error.message}`)
    }

    return {
      id: data.id,
      userId: data.user_id,
      projectId: data.project_id,
      domain: data.domain,
      isActive: data.is_active,
      createdAt: data.created_at,
    }
  }

  async removeEmbedDomain(domainId: number, userId: string): Promise<void> {
    const { error } = await supabase
      .from('embed_domains')
      .delete()
      .eq('id', domainId)
      .eq('user_id', userId)

    if (error) {
      throw new Error(`Failed to remove embed domain: ${error.message}`)
    }
  }

  async trackEmbedUsage(userId: string, documentId?: number, projectId?: number, domain?: string, ipAddress?: string, userAgent?: string): Promise<void> {
    try {
      await supabase.rpc('track_embed_usage', {
        p_user_id: userId,
        p_document_id: documentId || null,
        p_project_id: projectId || null,
        p_domain: domain || null,
        p_ip_address: ipAddress || null,
        p_user_agent: userAgent || null,
      })
    } catch (error) {
      // Silently fail to avoid breaking embed functionality
      console.error('Failed to track embed usage:', error)
    }
  }
}

export const supabaseStorage = new SupabaseStorage()