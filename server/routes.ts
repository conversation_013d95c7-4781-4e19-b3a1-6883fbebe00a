import type { Express } from "express";
import { createServer, type Server } from "http";
import multer from "multer";
import { storage, uploadFiles, uploadFile } from "./storage";
import { insertDocumentSchema, questionSchema, embedQuestionSchema, insertProjectSchema, createProjectSchema } from "@shared/schema";
import { llamaParseService } from "./services/llamaparse";
import { llamaIndexService } from "./services/llamaindex";
import { openRouterService } from "./services/openrouter";
import { SummarizationService } from "./services/summarization";
import { authenticateUser, type AuthenticatedRequest } from "./auth-middleware";
import rateLimit from 'express-rate-limit';
import jwt from 'jsonwebtoken';
import crypto from 'crypto';
import { createClient } from '@supabase/supabase-js';
import { z } from "zod";

// ============ SECURE LOGGING SYSTEM ============

enum LogLevel {
  ERROR = 'ERROR',
  WARN = 'WARN', 
  INFO = 'INFO',
  DEBUG = 'DEBUG',
  SECURITY = 'SECURITY'
}

interface LogContext {
  userId?: string;
  ip?: string;
  endpoint?: string;
  method?: string;
  userAgent?: string;
}

class SecureLogger {
  private isProduction = process.env.NODE_ENV === 'production';
  private isDevelopment = process.env.NODE_ENV === 'development';

  private sanitizeData(data: any): any {
    if (typeof data === 'string') {
      // Remove potential sensitive data patterns
      return data
        .replace(/password=[\w\-\.@]+/gi, 'password=***')
        .replace(/token=[\w\-\.]+/gi, 'token=***')
        .replace(/key=[\w\-\.]+/gi, 'key=***')
        .replace(/secret=[\w\-\.]+/gi, 'secret=***')
        .replace(/Bearer\s+[\w\-\.]+/gi, 'Bearer ***')
        .replace(/sk-[\w\-\.]+/gi, 'sk-***')
        .replace(/llx-[\w\-\.]+/gi, 'llx-***');
    }
    
    if (typeof data === 'object' && data !== null) {
      const sanitized: any = {};
      for (const [key, value] of Object.entries(data)) {
        // Skip sensitive fields
        if (['password', 'token', 'key', 'secret', 'authorization'].includes(key.toLowerCase())) {
          sanitized[key] = '***';
        } else {
          sanitized[key] = this.sanitizeData(value);
        }
      }
      return sanitized;
    }
    
    return data;
  }

  private formatMessage(level: LogLevel, message: string, context?: LogContext, data?: any): string {
    const timestamp = new Date().toISOString();
    const sanitizedData = data ? this.sanitizeData(data) : '';
    const contextStr = context ? 
      `[${context.method || ''}${context.endpoint || ''}${context.userId ? ` user:${context.userId}` : ''}${context.ip ? ` ip:${context.ip}` : ''}]` : '';
    
    return `${timestamp} [${level}]${contextStr} ${message}${sanitizedData ? ` ${JSON.stringify(sanitizedData)}` : ''}`;
  }

  security(message: string, context?: LogContext, data?: any) {
    console.log(this.formatMessage(LogLevel.SECURITY, message, context, data));
  }

  error(message: string, context?: LogContext, data?: any) {
    // Always log errors, but sanitize sensitive data
    console.error(this.formatMessage(LogLevel.ERROR, message, context, this.isDevelopment ? data : undefined));
  }

  warn(message: string, context?: LogContext, data?: any) {
    console.warn(this.formatMessage(LogLevel.WARN, message, context, this.isDevelopment ? data : undefined));
  }

  info(message: string, context?: LogContext, data?: any) {
    if (!this.isProduction) {
      console.log(this.formatMessage(LogLevel.INFO, message, context, data));
    }
  }

  debug(message: string, context?: LogContext, data?: any) {
    if (this.isDevelopment) {
      console.log(this.formatMessage(LogLevel.DEBUG, message, context, data));
    }
  }

  // Safe error response for clients
  sanitizeErrorForClient(error: any): string {
    if (this.isDevelopment) {
      // In development, show more details for debugging
      return error instanceof Error ? error.message : String(error);
    }

    // In production, return generic messages to prevent information disclosure
    if (error instanceof Error) {
      const message = error.message.toLowerCase();
      
      // Safe error patterns that can be shown to users
      if (message.includes('validation') || 
          message.includes('invalid') || 
          message.includes('not found') ||
          message.includes('access denied') ||
          message.includes('unauthorized') ||
          message.includes('rate limit') ||
          message.includes('file too large') ||
          message.includes('unsupported file')) {
        return error.message;
      }
    }

    // Generic error for everything else
    return 'An internal error occurred. Please try again later.';
  }
}

const logger = new SecureLogger();

// ============ ENHANCED RATE LIMITING FOR FILE UPLOADS ============

// Global upload rate limiting by IP (prevents basic flood attacks)
const uploadRateLimit = rateLimit({
  windowMs: 60 * 60 * 1000, // 1 hour window
  max: 20, // Maximum 20 uploads per hour per IP
  message: { 
    message: "Too many file uploads from this IP. Please try again later.",
    code: "UPLOAD_RATE_LIMIT_EXCEEDED"
  },
  standardHeaders: true,
  legacyHeaders: false,
  // Skip successful requests don't count towards the limit
  skipSuccessfulRequests: false,
  // Custom key generator to track by IP
  keyGenerator: (req) => {
    return req.ip || req.connection.remoteAddress || 'unknown';
  }
});

// Strict upload rate limiting for frequent uploaders
const strictUploadRateLimit = rateLimit({
  windowMs: 15 * 60 * 1000, // 15 minutes window
  max: 3, // Maximum 3 uploads per 15 minutes per IP
  message: { 
    message: "Upload frequency too high. Please wait before uploading again.",
    code: "STRICT_UPLOAD_RATE_LIMIT"
  },
  standardHeaders: true,
  legacyHeaders: false,
});

// Per-user upload tracking (prevents authenticated abuse)
const userUploadTracking = new Map();

function trackUserUpload(userId: string, fileSize: number): boolean {
  const now = Date.now();
  const oneHour = 60 * 60 * 1000;
  
  if (!userUploadTracking.has(userId)) {
    userUploadTracking.set(userId, []);
  }
  
  const userUploads = userUploadTracking.get(userId);
  
  // Clean old entries (older than 1 hour)
  const recentUploads = userUploads.filter((upload: any) => now - upload.timestamp < oneHour);
  
  // Check limits
  const uploadCount = recentUploads.length;
  const totalSize = recentUploads.reduce((sum: number, upload: any) => sum + upload.size, 0);
  
  // Limits per user per hour
  const MAX_UPLOADS_PER_HOUR = 50;
  const MAX_TOTAL_SIZE_PER_HOUR = 100 * 1024 * 1024; // 100MB
  
  if (uploadCount >= MAX_UPLOADS_PER_HOUR) {
    return false; // Too many uploads
  }
  
  if (totalSize + fileSize > MAX_TOTAL_SIZE_PER_HOUR) {
    return false; // Total size would exceed limit
  }
  
  // Track this upload
  recentUploads.push({ timestamp: now, size: fileSize });
  userUploadTracking.set(userId, recentUploads);
  
  return true;
}

// Enhanced file validation with magic number checking
function validateFileContent(buffer: Buffer, mimetype: string, filename: string): boolean {
  const firstBytes = buffer.slice(0, 4);
  
  // Magic number validation for common file types
  const magicNumbers: { [key: string]: number[][] } = {
    'application/pdf': [[0x25, 0x50, 0x44, 0x46]], // %PDF
    'application/msword': [[0xD0, 0xCF, 0x11, 0xE0]], // MS Office
    'application/vnd.openxmlformats-officedocument.wordprocessingml.document': [
      [0x50, 0x4B, 0x03, 0x04], // ZIP-based formats
      [0x50, 0x4B, 0x05, 0x06],
      [0x50, 0x4B, 0x07, 0x08]
    ],
    'text/plain': [], // Text files don't have magic numbers, skip validation
    'image/jpeg': [[0xFF, 0xD8, 0xFF]],
    'image/png': [[0x89, 0x50, 0x4E, 0x47]],
  };
  
  const expectedMagicNumbers = magicNumbers[mimetype];
  
  // If no magic numbers defined for this type, allow it (for text files, etc.)
  if (!expectedMagicNumbers || expectedMagicNumbers.length === 0) {
    return true;
  }
  
  // Check if file starts with any of the expected magic numbers
  return expectedMagicNumbers.some(magic => {
    return magic.every((byte, index) => 
      index < firstBytes.length && firstBytes[index] === byte
    );
  });
}

// Configure multer for file uploads with enhanced security
const upload = multer({
  storage: multer.memoryStorage(),
  limits: {
    fileSize: 5 * 1024 * 1024, // 5MB limit
    files: 1, // Only allow 1 file per request
    fields: 10, // Limit form fields
  },
  fileFilter: (req, file, cb) => {
    const allowedTypes = [
      // PDF
      'application/pdf',
      // Microsoft Office
      'application/msword',
      'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
      'application/vnd.ms-powerpoint',
      'application/vnd.openxmlformats-officedocument.presentationml.presentation',
      'application/vnd.ms-excel',
      'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
      // Text files
      'text/plain',
      'text/csv',
      'text/html',
      'text/markdown',
      'application/rtf',
      // Other document formats
      'application/xml',
      'text/xml'
    ];
    
    const fileExtension = file.originalname.toLowerCase().split('.').pop();
    const allowedExtensions = [
      'pdf', 'doc', 'docx', 'ppt', 'pptx', 'xls', 'xlsx',
      'txt', 'csv', 'html', 'htm', 'md', 'rtf',
      'xml'
    ];
    
    // Check MIME type and file extension
    if (!allowedTypes.includes(file.mimetype) || !allowedExtensions.includes(fileExtension || '')) {
      return cb(new Error('Unsupported file type. Please upload documents only: PDF, Word, PowerPoint, Excel, or text files. Images, videos, GIFs, and compressed files (.zip, .rar, .epub, etc.) are not allowed.'));
    }
    
    // Additional security: Check filename for path traversal attempts
    if (file.originalname.includes('../') || file.originalname.includes('..\\')) {
      return cb(new Error('Invalid filename.'));
    }
    
    cb(null, true);
  },
});

// ============ AI ABUSE TRACKING SYSTEM ============

// Track AI usage to detect potential abuse patterns
const aiUsageTracking = new Map<string, Array<{timestamp: number, cost: number, tokens: number}>>();

function trackAiUsage(userId: string, estimatedCost: number = 0.01, tokens: number = 0): boolean {
  const now = Date.now();
  const oneHour = 60 * 60 * 1000;
  
  if (!aiUsageTracking.has(userId)) {
    aiUsageTracking.set(userId, []);
  }
  
  const userUsage = aiUsageTracking.get(userId)!;
  
  // Clean old entries (older than 1 hour)
  const recentUsage = userUsage.filter(usage => now - usage.timestamp < oneHour);
  
  // Check limits for abuse detection
  const requestCount = recentUsage.length;
  const totalCost = recentUsage.reduce((sum, usage) => sum + usage.cost, 0);
  const totalTokens = recentUsage.reduce((sum, usage) => sum + usage.tokens, 0);
  
  // Abuse thresholds per hour
  const MAX_AI_REQUESTS_PER_HOUR = 100;
  const MAX_ESTIMATED_COST_PER_HOUR = 5.00; // $5 per hour
  const MAX_TOKENS_PER_HOUR = 100000; // 100k tokens per hour
  
  // Log suspicious activity
  if (requestCount > MAX_AI_REQUESTS_PER_HOUR * 0.8) { // 80% threshold warning
    console.warn(`HIGH_AI_USAGE: User ${userId} has made ${requestCount} AI requests in the last hour`);
  }
  
  if (totalCost > MAX_ESTIMATED_COST_PER_HOUR * 0.8) {
    console.warn(`HIGH_AI_COST: User ${userId} estimated cost $${totalCost.toFixed(2)} in the last hour`);
  }
  
  // Block if thresholds exceeded
  if (requestCount >= MAX_AI_REQUESTS_PER_HOUR) {
    console.error(`AI_ABUSE_DETECTED: User ${userId} exceeded request limit (${requestCount})`);
    return false;
  }
  
  if (totalCost >= MAX_ESTIMATED_COST_PER_HOUR) {
    console.error(`AI_COST_ABUSE: User ${userId} exceeded cost limit ($${totalCost.toFixed(2)})`);
    return false;
  }
  
  // Track this usage
  recentUsage.push({ timestamp: now, cost: estimatedCost, tokens });
  aiUsageTracking.set(userId, recentUsage);
  
  return true;
}

// Get AI usage statistics
function getAiUsageStats() {
  const now = Date.now();
  const oneHour = 60 * 60 * 1000;
  
  let totalRequests = 0;
  let totalCost = 0;
  let totalTokens = 0;
  let activeUsers = new Set<string>();
  
  aiUsageTracking.forEach((usage, userId) => {
    const recentUsage = usage.filter(u => now - u.timestamp < oneHour);
    totalRequests += recentUsage.length;
    totalCost += recentUsage.reduce((sum, u) => sum + u.cost, 0);
    totalTokens += recentUsage.reduce((sum, u) => sum + u.tokens, 0);
    if (recentUsage.length > 0) {
      activeUsers.add(userId);
    }
  });
  
  return {
    aiRequestsLastHour: totalRequests,
    estimatedCostLastHour: totalCost,
    totalTokensLastHour: totalTokens,
    activeAiUsers: activeUsers.size,
    averageCostPerRequest: totalRequests > 0 ? totalCost / totalRequests : 0
  };
}

// Upload monitoring and analytics
function getUploadStats() {
  const now = Date.now();
  const oneHour = 60 * 60 * 1000;
  
  let totalUploads = 0;
  let totalSize = 0;
  let activeUsers = new Set<string>();
  
  userUploadTracking.forEach((uploads, userId) => {
    const recentUploads = uploads.filter((upload: any) => now - upload.timestamp < oneHour);
    totalUploads += recentUploads.length;
    totalSize += recentUploads.reduce((sum: number, upload: any) => sum + upload.size, 0);
    if (recentUploads.length > 0) {
      activeUsers.add(userId);
    }
  });
  
  return {
    uploadsLastHour: totalUploads,
    totalSizeLastHour: totalSize,
    activeUsers: activeUsers.size,
    averageFileSize: totalUploads > 0 ? Math.round(totalSize / totalUploads) : 0
  };
}

// Embed security configuration
const EMBED_JWT_SECRET = process.env.EMBED_JWT_SECRET || 'embed_jwt_fallback_secret_key_for_development_only_' + crypto.randomBytes(16).toString('hex');

// Secure secret configuration logging
logger.security('EMBED_JWT_SECRET configuration status', undefined, { 
  configured: !!EMBED_JWT_SECRET,
  environment: process.env.NODE_ENV 
});

// Initialize Supabase client for admin operations
const supabaseUrl = process.env.SUPABASE_URL;
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY;
let supabase: any = null;

if (supabaseUrl && supabaseServiceKey) {
  supabase = createClient(supabaseUrl, supabaseServiceKey, {
    auth: {
      autoRefreshToken: false,
      persistSession: false
    }
  });
  logger.info("Supabase admin client initialized");
} else {
  logger.warn("Supabase configuration missing - admin features disabled");
}

// Domain whitelist for embed security
interface EmbedDomain {
  id: number;
  userId: number;
  domain: string;
  isActive: boolean;
  createdAt: string;
}

// ============ COMPREHENSIVE RATE LIMITING SYSTEM ============

// Critical AI endpoints (most expensive - strict limits)
const aiChatRateLimit = rateLimit({
  windowMs: 15 * 60 * 1000, // 15 minutes
  max: 30, // Maximum 30 AI chat requests per 15 minutes per IP
  message: { 
    message: "Too many AI chat requests. Please wait before asking another question.",
    code: "AI_RATE_LIMIT_EXCEEDED"
  },
  standardHeaders: true,
  legacyHeaders: false,
  // Use user ID as key when available, fallback to IP
  keyGenerator: (req) => {
    const authReq = req as AuthenticatedRequest;
    return authReq.user?.id || req.ip || 'unknown';
  }
});

// Strict AI rate limiting for burst protection  
const strictAiRateLimit = rateLimit({
  windowMs: 60 * 1000, // 1 minute
  max: 7, // Maximum 7 AI requests per minute per user
  message: { 
    message: "AI requests too frequent. Please wait a moment before asking another question.",
    code: "AI_BURST_LIMIT_EXCEEDED"
  },
  standardHeaders: true,
  legacyHeaders: false,
  keyGenerator: (req) => {
    const authReq = req as AuthenticatedRequest;
    return authReq.user?.id || req.ip || 'unknown';
  }
});

// General API endpoints (moderate limits)
const apiRateLimit = rateLimit({
  windowMs: 15 * 60 * 1000, // 15 minutes  
  max: 200, // 200 requests per 15 minutes per IP
  message: { 
    message: "Too many API requests. Please slow down.",
    code: "API_RATE_LIMIT_EXCEEDED"
  },
  standardHeaders: true,
  legacyHeaders: false,
});

// Chat history and document listing (lighter limits)
const readOnlyRateLimit = rateLimit({
  windowMs: 15 * 60 * 1000, // 15 minutes
  max: 500, // 500 read requests per 15 minutes per IP
  message: { 
    message: "Too many requests. Please wait before making more requests.",
    code: "READ_RATE_LIMIT_EXCEEDED"
  },
  standardHeaders: true,
  legacyHeaders: false,
});

// Rate limiting for embed endpoints (existing)
const embedRateLimit = rateLimit({
  windowMs: 15 * 60 * 1000, // 15 minutes
  max: 100, // limit each IP to 100 requests per windowMs
  message: { message: "Too many embed requests, please try again later" },
  standardHeaders: true,
  legacyHeaders: false,
});

// Function to validate domain
function isValidDomain(domain: string): boolean {
  // Allow "*" as a global wildcard
  if (domain === "*") {
    return true;
  }
  
  try {
    new URL(`https://${domain}`);
    return true;
  } catch {
    return false;
  }
}

// Function to check if referrer domain is whitelisted
async function isEmbedAllowed(documentId: number | null, projectId: number | null, referrer: string | undefined): Promise<boolean> {
  console.log(`isEmbedAllowed called with documentId: ${documentId}, projectId: ${projectId}, referrer: ${referrer}`);
  
  // Get the owner of the document/project first
  let ownerId: string | null = null;
  
  if (documentId) {
    const document = await storage.getDocument(documentId);
    ownerId = document?.userId || null;
  } else if (projectId) {
    const project = await storage.getProject(projectId);
    ownerId = project?.userId || null;
  }
  
  if (!ownerId) {
    console.log(`No owner found for documentId: ${documentId}, projectId: ${projectId}`);
    return false;
  }

  // Get the user's whitelisted domains for this specific project
  const whitelistedDomains = await storage.getEmbedDomains(ownerId, projectId || undefined);
  console.log(`Owner: ${ownerId}, projectId: ${projectId}, whitelisted domains:`, whitelistedDomains.map(d => d.domain));
  
  // Check if wildcard "*" is allowed (allows all domains)
  const hasWildcard = whitelistedDomains.some(d => d.isActive && d.domain === "*");
  if (hasWildcard) {
    console.log(`Wildcard domain (*) found - allowing access`);
    return true;
  }

  // If no referrer (direct access or testing), deny unless we have wildcard
  if (!referrer) {
    console.log(`No referrer header found and no wildcard domain - denying access`);
    return false;
  }
  
  try {
    const referrerUrl = new URL(referrer);
    const domain = referrerUrl.hostname;
    
    console.log(`Checking embed authorization for domain: ${domain}, owner: ${ownerId}, whitelisted domains:`, whitelistedDomains.map(d => d.domain));
    
    const isAllowed = whitelistedDomains.some(d => 
      d.isActive && d.domain === domain
    );
    
    console.log(`Embed authorization result: ${isAllowed}`);
    return isAllowed;
  } catch (error) {
    console.error('Error in isEmbedAllowed:', error);
    return false;
  }
}

// Generate embed token
function generateEmbedToken(userId: string, documentId?: number, projectId?: number): string {
  const payload = {
    userId,
    documentId,
    projectId,
    type: 'embed',
    exp: Math.floor(Date.now() / 1000) + (24 * 60 * 60) // 24 hours
  };
  
  return jwt.sign(payload, EMBED_JWT_SECRET);
}

// Verify embed token
function verifyEmbedToken(token: string): any {
  try {
    return jwt.verify(token, EMBED_JWT_SECRET);
  } catch {
    return null;
  }
}

// Initialize summarization service
const summarizationService = new SummarizationService();

export async function registerRoutes(app: Express): Promise<Server> {
  
  // USER PROFILE AND CREDIT API ROUTES
  
  // Get user profile with usage
  app.get("/api/user/profile", authenticateUser, async (req: AuthenticatedRequest, res) => {
    try {
      const profile = await storage.getUserProfileWithUsage(req.user!.id);
      res.json(profile);
    } catch (error) {
      logger.error("Get user profile failed", {
        userId: req.user!.id,
        endpoint: '/api/user/profile'
      });
      res.status(500).json({ message: logger.sanitizeErrorForClient(error) });
    }
  });

  // Check user limits
  app.get("/api/user/limits", authenticateUser, async (req: AuthenticatedRequest, res) => {
    try {
      const limits = await storage.checkUserLimits(req.user!.id);
      res.json(limits);
    } catch (error) {
      console.error("Check user limits error:", error);
      res.status(500).json({ message: "Failed to check user limits" });
    }
  });

  // Get credit usage history
  app.get("/api/user/credits", authenticateUser, async (req: AuthenticatedRequest, res) => {
    try {
      const creditUsage = await storage.getCreditUsage(req.user!.id);
      res.json(creditUsage);
    } catch (error) {
      console.error("Get credit usage error:", error);
      res.status(500).json({ message: "Failed to get credit usage" });
    }
  });

  // ADMIN API ROUTES (for subscription management)
  
  // Admin endpoint to update user subscription by email
  app.post("/api/admin/subscription/update", async (req, res) => {
    try {
      // Simple security: check for admin key in headers
      const adminKey = req.headers['x-admin-key'];
      if (adminKey !== process.env.ADMIN_SECRET_KEY) {
        return res.status(403).json({ message: "Admin access required" });
      }

      const { email, subscriptionStatus, durationMonths = 1 } = req.body;
      
      if (!email || !subscriptionStatus) {
        return res.status(400).json({ message: "Email and subscription status are required" });
      }

      console.log(`Admin updating subscription for ${email} to ${subscriptionStatus}`);

      // Find user by email in Supabase Auth
      const { data: { users }, error: authError } = await supabase.auth.admin.listUsers();
      
      if (authError) {
        return res.status(500).json({ message: `Failed to list users: ${authError.message}` });
      }
      
      const user = users.find(u => u.email === email);
      if (!user) {
        return res.status(404).json({ message: `User with email ${email} not found` });
      }

      // Calculate subscription dates
      const subscriptionStartDate = new Date();
      const subscriptionEndDate = new Date();
      subscriptionEndDate.setMonth(subscriptionEndDate.getMonth() + durationMonths);

      // Update or create user profile
      let updatedProfile;
      const existingProfile = await storage.getUserProfile(user.id);
      
      if (existingProfile) {
        updatedProfile = await storage.updateUserProfile(user.id, {
          subscriptionStatus,
          subscriptionStartDate,
          subscriptionEndDate,
        });
      } else {
        updatedProfile = await storage.createUserProfile({
          id: user.id,
          email: user.email,
                  subscriptionStatus,
        subscriptionStartDate,
        subscriptionEndDate,
        credits: 30,
          totalCreditsUsed: 0,
        });
      }

      res.json({
        success: true,
        message: `Subscription updated successfully for ${email}`,
        profile: updatedProfile
      });

    } catch (error) {
      console.error("Admin subscription update error:", error);
      res.status(500).json({ 
        message: "Failed to update subscription",
        error: error instanceof Error ? error.message : "Unknown error"
      });
    }
  });

  // Admin endpoint to get user info by email
  app.get("/api/admin/user/:email", async (req, res) => {
    try {
      const adminKey = req.headers['x-admin-key'];
      if (adminKey !== process.env.ADMIN_SECRET_KEY) {
        return res.status(403).json({ message: "Admin access required" });
      }

      const { email } = req.params;
      
      // Find user by email in Supabase Auth
      const { data: { users }, error: authError } = await supabase.auth.admin.listUsers();
      
      if (authError) {
        return res.status(500).json({ message: `Failed to list users: ${authError.message}` });
      }
      
      const user = users.find(u => u.email === email);
      if (!user) {
        return res.status(404).json({ message: `User with email ${email} not found` });
      }

      const profile = await storage.getUserProfileWithUsage(user.id);
      
      res.json({
        user: {
          id: user.id,
          email: user.email,
          created_at: user.created_at,
        },
        profile
      });

    } catch (error) {
      console.error("Admin get user error:", error);
      res.status(500).json({ 
        message: "Failed to get user information",
        error: error instanceof Error ? error.message : "Unknown error"
      });
    }
  });

  // Upload document with comprehensive security
  app.post("/api/documents/upload", 
    uploadRateLimit,           // IP-based rate limiting
    strictUploadRateLimit,     // Strict frequency limiting 
    authenticateUser,          // User authentication
    upload.single('file'),     // File upload with validation
    async (req: AuthenticatedRequest, res) => {
    try {
      if (!req.file) {
        return res.status(400).json({ message: "No file uploaded" });
      }

      // Enhanced file content validation
      if (!validateFileContent(req.file.buffer, req.file.mimetype, req.file.originalname)) {
        return res.status(400).json({ 
          message: "File content doesn't match the file type. Possible malicious file detected.",
          code: "INVALID_FILE_CONTENT"
        });
      }

      // Per-user upload tracking (prevents authenticated user abuse)
      if (!trackUserUpload(req.user!.id, req.file.size)) {
        return res.status(429).json({ 
          message: "Upload limit exceeded. Please try again later.",
          code: "USER_UPLOAD_LIMIT_EXCEEDED"
        });
      }

      // Check user limits before upload
      const limits = await storage.checkUserLimits(req.user!.id);
      if (!limits.canUploadDocument) {
        return res.status(403).json({ 
          message: limits.subscriptionStatus === "free" 
            ? "Insufficient credits for document upload. Upgrade to Pro for unlimited uploads!"
            : "Insufficient credits for document upload",
          code: "UPLOAD_LIMIT_REACHED"
        });
      }

      const projectId = req.body.projectId ? parseInt(req.body.projectId) : null;
      
      // Validate projectId
      if (!projectId || isNaN(projectId)) {
        return res.status(400).json({ message: "Valid project ID is required" });
      }

      // Verify the project exists and user owns it
      const project = await storage.getProject(projectId);
      if (!project) {
        return res.status(404).json({ message: "Project not found" });
      }
      if (project.userId !== req.user!.id) {
        return res.status(403).json({ message: "Access denied" });
      }

      // Deduct credits for document upload
      const creditsDeducted = await storage.deductCredits(req.user!.id, 'document_upload');
      if (!creditsDeducted && limits.subscriptionStatus === "free") {
        return res.status(403).json({ 
          message: "Insufficient credits for document upload. Upgrade to Pro for unlimited uploads!",
          code: "INSUFFICIENT_CREDITS"
        });
      }

      const documentData = {
        projectId,
        userId: req.user!.id,
        filename: req.file.originalname,
        filesize: req.file.size,
        contentType: req.file.mimetype,
      };

      const validated = insertDocumentSchema.parse(documentData);
      const document = await storage.createDocument(validated);

      // Update credit usage with document ID
      if (creditsDeducted) {
        await storage.deductCredits(req.user!.id, 'document_upload', document.id, 0); // Update existing record
      }

      // Security logging for monitoring
      logger.security('File upload completed', {
        userId: req.user!.id,
        ip: req.ip,
        endpoint: '/api/documents/upload',
        method: 'POST'
      }, {
        filename: req.file.originalname,
        fileSize: req.file.size,
        contentType: req.file.mimetype
      });

      // Start background processing
      processDocumentAsync(document.id, req.file.buffer, req.file.originalname);

      res.json(document);
    } catch (error) {
      // Enhanced error logging for security monitoring
      logger.error('File upload failed', {
        userId: req.user?.id || 'unknown',
        ip: req.ip,
        endpoint: '/api/documents/upload',
        method: 'POST',
        userAgent: req.get('User-Agent')
      }, {
        filename: req.file?.originalname || 'none',
        fileSize: req.file?.size || 0,
        errorType: error instanceof Error ? error.constructor.name : 'Unknown'
      });
      
      // Check for suspicious patterns
      if (error instanceof Error) {
        if (error.message.includes('File too large') || error.message.includes('LIMIT_FILE_SIZE')) {
          return res.status(413).json({ 
            message: "File too large. Maximum size allowed is 5MB.",
            code: "FILE_TOO_LARGE"
          });
        }
        
        if (error.message.includes('Unsupported file type')) {
          return res.status(400).json({ 
            message: error.message,
            code: "UNSUPPORTED_FILE_TYPE"
          });
        }
        
        if (error.message.includes('Invalid filename')) {
          return res.status(400).json({ 
            message: "Invalid filename detected.",
            code: "INVALID_FILENAME"
          });
        }
      }
      
      res.status(500).json({ message: logger.sanitizeErrorForClient(error) });
    }
  });

  // Get document status
  app.get("/api/documents/:id", authenticateUser, async (req: AuthenticatedRequest, res) => {
    try {
      const id = parseInt(req.params.id);
      const document = await storage.getDocument(id);
      
      if (!document) {
        return res.status(404).json({ message: "Document not found" });
      }

      // Check if user owns this document
      if (document.userId !== req.user!.id) {
        return res.status(403).json({ message: "Access denied" });
      }

      res.json(document);
    } catch (error) {
      console.error("Get document error:", error);
      res.status(500).json({ message: "Failed to get document" });
    }
  });

  // Ask question with streaming response - SECURED WITH RATE LIMITING
  app.post("/api/chat/ask", 
    strictAiRateLimit,      // Burst protection (3 requests/minute)
    aiChatRateLimit,        // General AI protection (30 requests/15min)
    authenticateUser,       // User authentication
    async (req: AuthenticatedRequest, res) => {
    try {
      // AI abuse detection and tracking
      if (!trackAiUsage(req.user!.id, 0.02, 1000)) { // Estimate $0.02 and 1000 tokens per request
        return res.status(429).json({ 
          message: "AI usage limit exceeded. Please wait before making more requests.",
          code: "AI_ABUSE_DETECTED"
        });
      }

      // Check user limits before processing chat
      const limits = await storage.checkUserLimits(req.user!.id);
      if (!limits.canUseChat) {
        return res.status(403).json({ 
          message: "Insufficient credits for chat message",
          code: "CHAT_LIMIT_REACHED"
        });
      }

      const validated = questionSchema.parse(req.body);
      const { projectId, documentId, question } = validated;

      let context = "";
      let targetProject: any = null;
      let targetDocument: any = null;

      if (projectId) {
        // Project-based chat
        const project = await storage.getProject(projectId);
        if (!project) {
          return res.status(404).json({ message: "Project not found" });
        }

        // Check if user owns this project
        if (project.userId !== req.user!.id) {
          return res.status(403).json({ message: "Access denied" });
        }

        targetProject = project;

        // Get all ready documents in the project
        const documents = await storage.getProjectDocuments(projectId);
        const readyDocuments = documents.filter(doc => doc.status === "ready");

        if (readyDocuments.length === 0) {
          return res.status(400).json({ message: "No ready documents in project" });
        }

        // Combine context from all documents
        context = readyDocuments
          .map(doc => {
            const parsedData = doc.parsedData as any;
            return `--- ${doc.filename} ---\n${parsedData?.text || "No content available"}`;
          })
          .join("\n\n");

      } else if (documentId) {
        // Document-based chat (backward compatibility)
        const document = await storage.getDocument(documentId);
        if (!document) {
          return res.status(404).json({ message: "Document not found" });
        }

        // Check if user owns this document
        if (document.userId !== req.user!.id) {
          return res.status(403).json({ message: "Access denied" });
        }

        if (document.status !== "ready") {
          return res.status(400).json({ message: "Document is not ready for querying" });
        }

        targetDocument = document;
        const parsedData = document.parsedData as any;
        context = parsedData?.text || "No content available";
      }

      // Set headers for streaming
      res.setHeader('Content-Type', 'text/event-stream');
      res.setHeader('Cache-Control', 'no-cache');
      res.setHeader('Connection', 'keep-alive');
      res.flushHeaders();

      let fullResponse = "";
      
      // Security logging for AI usage monitoring
      logger.security('AI request initiated', {
        userId: req.user!.id,
        ip: req.ip,
        endpoint: '/api/chat/ask',
        method: 'POST'
      }, {
        questionLength: question.length,
        documentId,
        projectId
      });
      
      try {
        // Use OpenRouter to generate AI response
        const stream = await openRouterService.generateStreamResponse(question, context);
        
        for await (const chunk of stream) {
          fullResponse += chunk;
          res.write(`data: ${JSON.stringify({ content: chunk })}\n\n`);
        }
        
        // Check if OpenRouter response seems incomplete
        if (fullResponse.length > 0) {
          const trimmedResponse = fullResponse.trim();
          const isIncompleteResponse = 
            trimmedResponse.length > 10 && 
            !trimmedResponse.match(/[.!?]$/) && 
            !trimmedResponse.endsWith('**') &&
            !trimmedResponse.endsWith(':') &&
            trimmedResponse.length < 50; // Very short responses are likely incomplete
          
          if (isIncompleteResponse) {
            console.log(`OpenRouter response appears incomplete (${trimmedResponse.length} chars): "${trimmedResponse.slice(-30)}"`);
            console.log('Clearing response to trigger fallback');
            fullResponse = ""; // Clear to trigger fallback
          } else {
            console.log(`OpenRouter response completed successfully (${trimmedResponse.length} chars)`);
          }
        }
      } catch (error) {
        console.log("OpenRouter failed, falling back to content-based response:", error);
        // Don't re-throw here - let the fallback logic handle it
      }
      
      if (!fullResponse) {
        // This runs when OpenRouter fails and fallback response is needed
        console.log(`OpenRouter failed, using enhanced content-based response for: "${question}"`);
        
        // Fallback to sending response in chunks
        const lowerQuestion = question.toLowerCase();
        const contextLines = context.split('\n').filter((line: string) => line.trim().length > 0);
        
        let fallbackResponse = "";
        
        // Add a notice about using fallback response
        const fallbackNotice = "*(Note: Using content-based response due to AI service interruption)*\n\n";
        
        // Enhanced keyword-based response generation
        if (lowerQuestion.includes('summary') || lowerQuestion.includes('summarize')) {
          fallbackResponse = fallbackNotice + `## Document Summary\n\nBased on the document content, here are the key points:\n\n`;
          const importantLines = contextLines
            .filter(line => line.length > 50)
            .slice(0, 5)
            .map((line, i) => `${i + 1}. ${line.trim()}`);
          fallbackResponse += importantLines.join('\n\n');
        } else if (lowerQuestion.includes('main') || lowerQuestion.includes('key')) {
          fallbackResponse = fallbackNotice + `## Key Information\n\nThe main points from the document include:\n\n`;
          const relevantLines = contextLines
            .filter(line => line.toLowerCase().includes('important') || line.toLowerCase().includes('key') || line.toLowerCase().includes('main'))
            .slice(0, 3);
          if (relevantLines.length === 0) {
            fallbackResponse += contextLines.slice(0, 3).map(line => `• ${line.trim()}`).join('\n');
          } else {
            fallbackResponse += relevantLines.map(line => `• ${line.trim()}`).join('\n');
          }
        } else {
          // Generic search through content
          const keywords = question.toLowerCase().split(' ').filter(word => word.length > 3);
          const relevantLines = contextLines.filter(line => 
            keywords.some(keyword => line.toLowerCase().includes(keyword))
          ).slice(0, 3);
          
          if (relevantLines.length > 0) {
            fallbackResponse = fallbackNotice + `Based on your question about "${question}", here's what I found:\n\n`;
            fallbackResponse += relevantLines.map(line => `• ${line.trim()}`).join('\n\n');
          } else {
            fallbackResponse = fallbackNotice + `I found information related to your question in the document:\n\n`;
            fallbackResponse += contextLines.slice(0, 2).map(line => `• ${line.trim()}`).join('\n\n');
            fallbackResponse += `\n\nFor more specific information, please try rephrasing your question or asking about specific topics mentioned in the document.`;
          }
        }
        
        // Split into chunks and stream
        const chunkSize = 50;
        for (let i = 0; i < fallbackResponse.length; i += chunkSize) {
          const chunk = fallbackResponse.slice(i, i + chunkSize);
          fullResponse += chunk;
          res.write(`data: ${JSON.stringify({ content: chunk })}\n\n`);
          await new Promise(resolve => setTimeout(resolve, 50)); // Simulate streaming
        }
      }

      // Save chat message after streaming completes
      const sourceReferences = [{
        page: 1,
        text: context.substring(0, 100) + "...",
        score: 0.85,
      }];

      try {
        // Deduct credits for chat message
        const creditsDeducted = await storage.deductCredits(req.user!.id, 'chat_message');
        if (!creditsDeducted && limits.subscriptionStatus === "free") {
          console.error("Insufficient credits for chat message");
          // Still save the message but log the issue
        }

        const chatMessage = await storage.createChatMessage({
          projectId: projectId as number,
          documentId: documentId || null,
          question,
          response: fullResponse,
          sourceReferences,
        });

        // Update credit usage with message ID
        if (creditsDeducted) {
          await storage.deductCredits(req.user!.id, 'chat_message', chatMessage.id, 0);
        }
      } catch (chatSaveError) {
        console.error("Failed to save chat message:", chatSaveError);
        // Don't break the streaming response if chat saving fails
      }

      res.write(`data: [DONE]\n\n`);
      res.end();
    } catch (error) {
      logger.error('AI chat request failed', {
        userId: req.user!.id,
        ip: req.ip,
        endpoint: '/api/chat/ask',
        method: 'POST'
      }, {
        errorType: error instanceof Error ? error.constructor.name : 'Unknown'
      });
      
      // Check if headers have been sent (streaming started)
      if (res.headersSent) {
        // If streaming has started, send error and proper end signal
        res.write(`data: {"error": "An error occurred"}\n\n`);
        res.write(`data: [DONE]\n\n`);
        res.end();
      } else {
        // If streaming hasn't started, send JSON error
        res.status(500).json({ message: logger.sanitizeErrorForClient(error) });
      }
    }
  });



  // Get chat history - SECURED WITH RATE LIMITING
  app.get("/api/chat/:documentId", 
    readOnlyRateLimit,      // Read-only endpoint protection
    authenticateUser,       // User authentication
    async (req: AuthenticatedRequest, res) => {
    try {
      const documentId = parseInt(req.params.documentId);
      
      const document = await storage.getDocument(documentId);
      if (!document) {
        return res.status(404).json({ message: "Document not found" });
      }

      // Check if user owns this document
      if (document.userId !== req.user!.id) {
        return res.status(403).json({ message: "Access denied" });
      }
      
      const messages = await storage.getChatMessages(documentId);
      res.json(messages);
    } catch (error) {
      console.error("Get chat history error:", error);
      res.status(500).json({ message: "Failed to get chat history" });
    }
  });

  // Get user documents - SECURED WITH RATE LIMITING
  app.get("/api/documents", 
    readOnlyRateLimit,      // Read-only endpoint protection
    authenticateUser,       // User authentication
    async (req: AuthenticatedRequest, res) => {
    try {
      const documents = await storage.getUserDocuments(req.user!.id);
      res.json(documents);
    } catch (error) {
      console.error("Get user documents error:", error);
      res.status(500).json({ message: "Failed to get documents" });
    }
  });

  // PROJECT API ROUTES
  
  // Create new project - SECURED WITH RATE LIMITING
  app.post("/api/projects", 
    apiRateLimit,           // General API protection
    authenticateUser,       // User authentication
    async (req: AuthenticatedRequest, res) => {
    try {
      // Check user limits before creating project
      const limits = await storage.checkUserLimits(req.user!.id);
      if (!limits.canCreateProject) {
        return res.status(403).json({ 
          message: limits.subscriptionStatus === "free" 
            ? "Insufficient credits for project creation. Upgrade to Pro for unlimited projects!"
            : "Insufficient credits for project creation",
          code: "PROJECT_LIMIT_REACHED"
        });
      }

      // Deduct credits for project creation
      const creditsDeducted = await storage.deductCredits(req.user!.id, 'project_create');
      if (!creditsDeducted && limits.subscriptionStatus === "free") {
        return res.status(403).json({ 
          message: "Insufficient credits for project creation. Upgrade to Pro for unlimited projects!",
          code: "INSUFFICIENT_CREDITS"
        });
      }

      const validated = createProjectSchema.parse(req.body);
      const projectData = {
        ...validated,
        userId: req.user!.id,
      };
      
      const project = await storage.createProject(projectData);

      // Update credit usage with project ID
      if (creditsDeducted) {
        await storage.deductCredits(req.user!.id, 'project_create', project.id, 0);
      }

      res.json(project);
    } catch (error) {
      logger.error("Create project failed", {
        userId: req.user!.id,
        endpoint: '/api/projects',
        method: 'POST'
      });
      res.status(500).json({ message: logger.sanitizeErrorForClient(error) });
    }
  });

  // Get user's projects - SECURED WITH RATE LIMITING
  app.get("/api/projects", 
    readOnlyRateLimit,      // Read-only endpoint protection
    authenticateUser,       // User authentication
    async (req: AuthenticatedRequest, res) => {
    try {
      const projects = await storage.getUserProjects(req.user!.id);
      res.json(projects);
    } catch (error) {
      console.error("Get projects error:", error);
      res.status(500).json({ message: "Failed to get projects" });
    }
  });

  // Get user's projects with counts - SECURED WITH RATE LIMITING
  app.get("/api/projects/with-counts", 
    readOnlyRateLimit,      // Read-only endpoint protection
    authenticateUser,       // User authentication
    async (req: AuthenticatedRequest, res) => {
    try {
      logger.debug("Getting projects for user", { userId: req.user!.id });
      const projects = await storage.getUserProjects(req.user!.id);
      logger.debug("Found projects", { userId: req.user!.id }, { count: projects.length });
      
      // Get counts for each project
      const projectsWithCounts = await Promise.all(
        projects.map(async (project) => {
          const documents = await storage.getProjectDocuments(project.id);
          const messages = await storage.getProjectChatMessages(project.id);
          
          return {
            ...project,
            documentCount: documents.length,
            readyDocumentCount: documents.filter(doc => doc.status === 'ready').length,
            chatCount: messages.length,
          };
        })
      );
      
      logger.debug("Returning projects with counts", { userId: req.user!.id }, { count: projectsWithCounts.length });
      res.json(projectsWithCounts);
    } catch (error) {
      console.error("Get projects with counts error:", error);
      res.status(500).json({ message: "Failed to get projects with counts" });
    }
  });

  // Get specific project - SECURED WITH RATE LIMITING  
  app.get("/api/projects/:id", 
    readOnlyRateLimit,      // Read-only endpoint protection
    authenticateUser,       // User authentication
    async (req: AuthenticatedRequest, res) => {
    try {
      const id = parseInt(req.params.id);
      const project = await storage.getProject(id);
      
      if (!project) {
        return res.status(404).json({ message: "Project not found" });
      }

      // Check if user owns this project
      if (project.userId !== req.user!.id) {
        return res.status(403).json({ message: "Access denied" });
      }

      res.json(project);
    } catch (error) {
      console.error("Get project error:", error);
      res.status(500).json({ message: "Failed to get project" });
    }
  });

  // Get project documents - SECURED WITH RATE LIMITING
  app.get("/api/projects/:id/documents", 
    readOnlyRateLimit,      // Read-only endpoint protection
    authenticateUser,       // User authentication
    async (req: AuthenticatedRequest, res) => {
    try {
      const projectId = parseInt(req.params.id);
      const project = await storage.getProject(projectId);
      
      if (!project) {
        return res.status(404).json({ message: "Project not found" });
      }

      // Check if user owns this project
      if (project.userId !== req.user!.id) {
        return res.status(403).json({ message: "Access denied" });
      }

      const documents = await storage.getProjectDocuments(projectId);
      res.json(documents);
    } catch (error) {
      console.error("Get project documents error:", error);
      res.status(500).json({ message: "Failed to get project documents" });
    }
  });

  // Get project chat messages
  app.get("/api/projects/:id/chat", 
    readOnlyRateLimit,      // Read-only endpoint protection
    authenticateUser,       // User authentication
    async (req: AuthenticatedRequest, res) => {
    try {
      const projectId = parseInt(req.params.id);
      const project = await storage.getProject(projectId);
      
      if (!project) {
        return res.status(404).json({ message: "Project not found" });
      }

      // Check if user owns this project
      if (project.userId !== req.user!.id) {
        return res.status(403).json({ message: "Access denied" });
      }

      const messages = await storage.getProjectChatMessages(projectId);
      res.json(messages);
    } catch (error) {
      console.error("Get project chat error:", error);
      res.status(500).json({ message: "Failed to get project chat" });
    }
  });

  // Delete project - SECURED WITH RATE LIMITING
  app.delete("/api/projects/:id", 
    apiRateLimit,           // General API protection
    authenticateUser,       // User authentication
    async (req: AuthenticatedRequest, res) => {
    try {
      const projectId = parseInt(req.params.id);
      
      if (isNaN(projectId)) {
        return res.status(400).json({ message: "Invalid project ID" });
      }

      await storage.deleteProject(projectId, req.user!.id);
      res.json({ message: "Project deleted successfully" });
    } catch (error) {
      console.error("Delete project error:", error);
      if (error instanceof Error && error.message.includes("not found or unauthorized")) {
        return res.status(404).json({ message: "Project not found or access denied" });
      }
      res.status(500).json({ message: logger.sanitizeErrorForClient(error) });
    }
  });

  // Get dashboard stats
  app.get("/api/dashboard/stats", authenticateUser, async (req: AuthenticatedRequest, res) => {
    try {
      logger.debug("Getting dashboard stats", { userId: req.user!.id });
      const stats = await storage.getDashboardStats(req.user!.id);
      logger.debug("Dashboard stats retrieved", { userId: req.user!.id }, { 
        totalProjects: stats.totalProjects,
        totalDocuments: stats.totalDocuments 
      });
      res.json(stats);
    } catch (error) {
      console.error("Get dashboard stats error:", error);
      res.status(500).json({ message: "Failed to get dashboard stats" });
    }
  });

  // Admin endpoint for upload monitoring
  app.get("/api/admin/upload-stats", async (req, res) => {
    try {
      // Simple security: check for admin key in headers
      const adminKey = req.headers['x-admin-key'];
      if (adminKey !== process.env.ADMIN_SECRET_KEY) {
        return res.status(401).json({ message: "Unauthorized" });
      }

      const uploadStats = getUploadStats();
      const aiStats = getAiUsageStats();
      
      res.json({
        timestamp: new Date().toISOString(),
        uploads: uploadStats,
        ai: aiStats,
        rateLimitInfo: {
          uploadRateLimit: "10 requests per 10 minutes per user",
          chatRateLimit: "50 requests per hour per user"
        }
      });

    } catch (error) {
      console.error("Admin upload stats error:", error);
      res.status(500).json({ 
        message: "Failed to get upload stats",
        error: error instanceof Error ? error.message : "Unknown error"
      });
    }
  });

  // NEW ADMIN ENDPOINTS FOR MONITORING & SECURITY

  // Get all users with pagination and filters
  app.get("/api/admin/users", async (req, res) => {
    try {
      const adminKey = req.headers['x-admin-key'];
      if (adminKey !== process.env.ADMIN_SECRET_KEY) {
        return res.status(401).json({ message: "Unauthorized" });
      }

      const page = parseInt(req.query.page as string) || 1;
      const limit = parseInt(req.query.limit as string) || 20;
      const offset = (page - 1) * limit;

      // Get users from Supabase Auth
      const { data: { users }, error: authError } = await supabase.auth.admin.listUsers({
        page,
        perPage: limit
      });

      if (authError) {
        return res.status(500).json({ message: `Failed to list users: ${authError.message}` });
      }

      // Get user profiles and usage data
      const usersWithProfiles = await Promise.all(
        users.map(async (user: any) => {
          try {
            const profile = await storage.getUserProfileWithUsage(user.id);
            return {
              id: user.id,
              email: user.email,
              created_at: user.created_at,
              last_sign_in_at: user.last_sign_in_at,
              profile
            };
          } catch (error) {
            return {
              id: user.id,
              email: user.email,
              created_at: user.created_at,
              last_sign_in_at: user.last_sign_in_at,
              profile: null
            };
          }
        })
      );

      res.json({
        users: usersWithProfiles,
        pagination: {
          page,
          limit,
          total: users.length
        }
      });

    } catch (error) {
      console.error("Admin users list error:", error);
      res.status(500).json({ 
        message: "Failed to get users list",
        error: error instanceof Error ? error.message : "Unknown error"
      });
    }
  });

  // Get recent user activity
  app.get("/api/admin/recent-activity", async (req, res) => {
    try {
      const adminKey = req.headers['x-admin-key'];
      if (adminKey !== process.env.ADMIN_SECRET_KEY) {
        return res.status(401).json({ message: "Unauthorized" });
      }

      const days = parseInt(req.query.days as string) || 7;
      const cutoffDate = new Date();
      cutoffDate.setDate(cutoffDate.getDate() - days);

      // Get recent users
      const { data: { users }, error: authError } = await supabase.auth.admin.listUsers();
      
      if (authError) {
        return res.status(500).json({ message: `Failed to get users: ${authError.message}` });
      }

      const recentUsers = users
        .filter((user: any) => new Date(user.created_at) > cutoffDate)
        .sort((a: any, b: any) => new Date(b.created_at).getTime() - new Date(a.created_at).getTime())
        .slice(0, 50);

      // Get recent projects (if using Supabase storage)
      let recentProjects = [];
      try {
        if (storage.constructor.name === 'SupabaseStorage') {
          const { data: projectsData } = await supabase
            .from('projects')
            .select('id, name, user_id, created_at')
            .gte('created_at', cutoffDate.toISOString())
            .order('created_at', { ascending: false })
            .limit(50);
          
          recentProjects = projectsData || [];
        }
      } catch (error) {
        console.warn("Could not fetch recent projects:", error);
      }

      // Get upload stats
      const uploadStats = getUploadStats();
      const aiStats = getAiUsageStats();

      res.json({
        timeframe: `Last ${days} days`,
        recentUsers: recentUsers.map((user: any) => ({
          id: user.id,
          email: user.email,
          created_at: user.created_at,
          last_sign_in_at: user.last_sign_in_at
        })),
        recentProjects,
        activitySummary: {
          newUsers: recentUsers.length,
          newProjects: recentProjects.length,
          dailyUploads: uploadStats.uploadsLastHour * 24, // Estimate daily from hourly
          dailyAiRequests: aiStats.aiRequestsLastHour * 24 // Estimate daily from hourly
        }
      });

    } catch (error) {
      console.error("Admin recent activity error:", error);
      res.status(500).json({ 
        message: "Failed to get recent activity",
        error: error instanceof Error ? error.message : "Unknown error"
      });
    }
  });

  // Get system health status
  app.get("/api/admin/system-health", async (req, res) => {
    try {
      const adminKey = req.headers['x-admin-key'];
      if (adminKey !== process.env.ADMIN_SECRET_KEY) {
        return res.status(401).json({ message: "Unauthorized" });
      }

      const healthChecks = {
        timestamp: new Date().toISOString(),
        database: "unknown",
        llamaCloud: "unknown",
        openRouter: "unknown",
        supabase: "unknown"
      };

      // Check database connection
      try {
        await storage.getUserProfile("test"); // Simple test query
        healthChecks.database = "healthy";
      } catch (error) {
        healthChecks.database = "error";
      }

      // Check Supabase connection
      try {
        if (supabase) {
          const { error } = await supabase.from('projects').select('count').limit(1);
          healthChecks.supabase = error ? "error" : "healthy";
        } else {
          healthChecks.supabase = "not_configured";
        }
      } catch (error) {
        healthChecks.supabase = "error";
      }

      // Check LlamaCloud (basic config check)
      healthChecks.llamaCloud = process.env.LLAMA_CLOUD_API_KEY ? "configured" : "not_configured";
      
      // Check OpenRouter (basic config check)
      healthChecks.openRouter = process.env.OPENROUTER_API_KEY ? "configured" : "not_configured";

      res.json(healthChecks);

    } catch (error) {
      console.error("Admin system health error:", error);
      res.status(500).json({ 
        message: "Failed to get system health",
        error: error instanceof Error ? error.message : "Unknown error"
      });
    }
  });

  // Security monitoring endpoint
  app.get("/api/admin/security-alerts", async (req, res) => {
    try {
      const adminKey = req.headers['x-admin-key'];
      if (adminKey !== process.env.ADMIN_SECRET_KEY) {
        return res.status(401).json({ message: "Unauthorized" });
      }

      const uploadStats = getUploadStats();
      const aiStats = getAiUsageStats();

      // Basic security metrics
      const alerts = [];
      
      // Check for high activity (using available metrics)
      if (uploadStats.uploadsLastHour > 50) {
        alerts.push({
          type: "high_upload_activity",
          severity: "info",
          message: `High upload activity: ${uploadStats.uploadsLastHour} uploads in last hour`,
          timestamp: new Date().toISOString()
        });
      }

      if (aiStats.aiRequestsLastHour > 100) {
        alerts.push({
          type: "high_ai_activity",
          severity: "info",
          message: `High AI activity: ${aiStats.aiRequestsLastHour} requests in last hour`,
          timestamp: new Date().toISOString()
        });
      }

      // Check for high costs
      if (aiStats.estimatedCostLastHour > 10) {
        alerts.push({
          type: "high_ai_costs",
          severity: "warning",
          message: `High AI costs: $${aiStats.estimatedCostLastHour.toFixed(2)} in last hour`,
          timestamp: new Date().toISOString()
        });
      }

      res.json({
        alerts,
        summary: {
          totalAlerts: alerts.length,
          criticalAlerts: alerts.filter(a => a.severity === "critical").length,
          warningAlerts: alerts.filter(a => a.severity === "warning").length,
          infoAlerts: alerts.filter(a => a.severity === "info").length
        },
        lastChecked: new Date().toISOString()
      });

    } catch (error) {
      console.error("Admin security alerts error:", error);
      res.status(500).json({ 
        message: "Failed to get security alerts",
        error: error instanceof Error ? error.message : "Unknown error"
      });
    }
  });

  // Block/unblock user endpoint
  app.post("/api/admin/user/block", async (req, res) => {
    try {
      const adminKey = req.headers['x-admin-key'];
      if (adminKey !== process.env.ADMIN_SECRET_KEY) {
        return res.status(401).json({ message: "Unauthorized" });
      }

      const { userId, action, reason } = req.body; // action: 'block' or 'unblock'
      
      if (!userId || !action) {
        return res.status(400).json({ message: "User ID and action are required" });
      }

      // In a real implementation, you'd update user status in your database
      // For now, we'll just log the action
      console.log(`Admin action: ${action} user ${userId}. Reason: ${reason || 'Not provided'}`);

      // Note: Supabase doesn't have built-in user blocking, so you'd need to:
      // 1. Add a 'blocked' field to your user profiles table
      // 2. Check this field in your authentication middleware
      // 3. Update the field here

      res.json({
        message: `User ${action} action logged`,
        userId,
        action,
        timestamp: new Date().toISOString()
      });

    } catch (error) {
      console.error("Admin user block error:", error);
      res.status(500).json({ 
        message: "Failed to block/unblock user",
        error: error instanceof Error ? error.message : "Unknown error"
      });
    }
  });

  // ADMIN USER CONTENT ACCESS ENDPOINTS (for support & debugging)

  // Get user's projects (admin access)
  app.get("/api/admin/user/:userId/projects", async (req, res) => {
    try {
      const adminKey = req.headers['x-admin-key'];
      if (adminKey !== process.env.ADMIN_SECRET_KEY) {
        return res.status(401).json({ message: "Unauthorized" });
      }

      const { userId } = req.params;
      
      if (!userId) {
        return res.status(400).json({ message: "User ID is required" });
      }

      // Log admin access for security audit
      logger.security('Admin accessed user projects', {
        userId: 'admin',
        ip: req.ip,
        endpoint: `/api/admin/user/${userId}/projects`,
        method: 'GET'
      }, { targetUserId: userId });

      const projects = await storage.getUserProjects(userId);
      
      // Get counts for each project
      const projectsWithCounts = await Promise.all(
        projects.map(async (project) => {
          const documents = await storage.getProjectDocuments(project.id);
          const messages = await storage.getProjectChatMessages(project.id);
          
          return {
            ...project,
            documentCount: documents.length,
            readyDocumentCount: documents.filter(doc => doc.status === 'ready').length,
            chatCount: messages.length,
          };
        })
      );

      res.json({
        userId,
        projects: projectsWithCounts,
        timestamp: new Date().toISOString()
      });

    } catch (error) {
      console.error("Admin get user projects error:", error);
      res.status(500).json({ 
        message: "Failed to get user projects",
        error: error instanceof Error ? error.message : "Unknown error"
      });
    }
  });

  // Get specific user project details (admin access)
  app.get("/api/admin/user/:userId/projects/:projectId", async (req, res) => {
    try {
      const adminKey = req.headers['x-admin-key'];
      if (adminKey !== process.env.ADMIN_SECRET_KEY) {
        return res.status(401).json({ message: "Unauthorized" });
      }

      const { userId, projectId } = req.params;
      
      if (!userId || !projectId) {
        return res.status(400).json({ message: "User ID and Project ID are required" });
      }

      const projectIdNum = parseInt(projectId);
      if (isNaN(projectIdNum)) {
        return res.status(400).json({ message: "Invalid project ID" });
      }

      // Log admin access for security audit
      logger.security('Admin accessed user project', {
        userId: 'admin',
        ip: req.ip,
        endpoint: `/api/admin/user/${userId}/projects/${projectId}`,
        method: 'GET'
      }, { targetUserId: userId, projectId: projectIdNum });

      const project = await storage.getProject(projectIdNum);
      
      if (!project) {
        return res.status(404).json({ message: "Project not found" });
      }

      // Verify project belongs to the specified user
      if (project.userId !== userId) {
        return res.status(404).json({ message: "Project not found for this user" });
      }

      const documents = await storage.getProjectDocuments(projectIdNum);
      const messages = await storage.getProjectChatMessages(projectIdNum);

      res.json({
        project: {
          ...project,
          documentCount: documents.length,
          readyDocumentCount: documents.filter(doc => doc.status === 'ready').length,
          chatCount: messages.length,
        },
        documents,
        messages,
        timestamp: new Date().toISOString()
      });

    } catch (error) {
      console.error("Admin get user project error:", error);
      res.status(500).json({ 
        message: "Failed to get user project",
        error: error instanceof Error ? error.message : "Unknown error"
      });
    }
  });

  // Get detailed chat history for admin (separate endpoint for better performance)
  app.get("/api/admin/user/:userId/projects/:projectId/chat-history", async (req, res) => {
    try {
      const adminKey = req.headers['x-admin-key'];
      if (adminKey !== process.env.ADMIN_SECRET_KEY) {
        return res.status(401).json({ message: "Unauthorized" });
      }

      const { userId, projectId } = req.params;
      const limit = parseInt(req.query.limit as string) || 50;
      const offset = parseInt(req.query.offset as string) || 0;
      
      if (!userId || !projectId) {
        return res.status(400).json({ message: "User ID and Project ID are required" });
      }

      const projectIdNum = parseInt(projectId);
      if (isNaN(projectIdNum)) {
        return res.status(400).json({ message: "Invalid project ID" });
      }

      // Log admin access for security audit
      logger.security('Admin accessed detailed chat history', {
        userId: 'admin',
        ip: req.ip,
        endpoint: `/api/admin/user/${userId}/projects/${projectId}/chat-history`,
        method: 'GET'
      }, { targetUserId: userId, projectId: projectIdNum });

      const project = await storage.getProject(projectIdNum);
      
      if (!project) {
        return res.status(404).json({ message: "Project not found" });
      }

      // Verify project belongs to the specified user
      if (project.userId !== userId) {
        return res.status(404).json({ message: "Project not found for this user" });
      }

      // Get all chat messages with full content
      const allMessages = await storage.getProjectChatMessages(projectIdNum);
      
      // Sort by timestamp (newest first) and apply pagination
      const sortedMessages = allMessages
        .sort((a, b) => new Date(b.timestamp).getTime() - new Date(a.timestamp).getTime())
        .slice(offset, offset + limit);

      // Get total count for pagination
      const totalMessages = allMessages.length;

      res.json({
        projectId: projectIdNum,
        userId,
        messages: sortedMessages,
        pagination: {
          total: totalMessages,
          limit,
          offset,
          hasMore: offset + limit < totalMessages
        },
        timestamp: new Date().toISOString()
      });

    } catch (error) {
      console.error("Admin get detailed chat history error:", error);
      res.status(500).json({ 
        message: "Failed to get chat history",
        error: error instanceof Error ? error.message : "Unknown error"
      });
    }
  });

  // Admin chat with user's project (for debugging)
  app.post("/api/admin/user/:userId/projects/:projectId/chat", async (req, res) => {
    try {
      const adminKey = req.headers['x-admin-key'];
      if (adminKey !== process.env.ADMIN_SECRET_KEY) {
        return res.status(401).json({ message: "Unauthorized" });
      }

      const { userId, projectId } = req.params;
      const { question } = req.body;
      
      if (!userId || !projectId || !question) {
        return res.status(400).json({ message: "User ID, Project ID, and question are required" });
      }

      const projectIdNum = parseInt(projectId);
      if (isNaN(projectIdNum)) {
        return res.status(400).json({ message: "Invalid project ID" });
      }

      // Log admin access for security audit
      logger.security('Admin chatted with user project', {
        userId: 'admin',
        ip: req.ip,
        endpoint: `/api/admin/user/${userId}/projects/${projectId}/chat`,
        method: 'POST'
      }, { 
        targetUserId: userId, 
        projectId: projectIdNum,
        question: question.substring(0, 100) + (question.length > 100 ? '...' : '')
      });

      const project = await storage.getProject(projectIdNum);
      
      if (!project) {
        return res.status(404).json({ message: "Project not found" });
      }

      // Verify project belongs to the specified user
      if (project.userId !== userId) {
        return res.status(404).json({ message: "Project not found for this user" });
      }

      // Get all ready documents in the project
      const documents = await storage.getProjectDocuments(projectIdNum);
      const readyDocuments = documents.filter(doc => doc.status === "ready");

      if (readyDocuments.length === 0) {
        return res.status(400).json({ message: "No ready documents in project" });
      }

      // Combine context from all documents
      const context = readyDocuments
        .map(doc => {
          const parsedData = doc.parsedData as any;
          return `--- ${doc.filename} ---\n${parsedData?.text || "No content available"}`;
        })
        .join("\n\n");

      // Set headers for streaming
      res.setHeader('Content-Type', 'text/event-stream');
      res.setHeader('Cache-Control', 'no-cache');
      res.setHeader('Connection', 'keep-alive');

      // Generate AI response (using existing chat logic)
      const { generateResponse } = require('./services/openrouter');
      
      try {
        let fullResponse = '';
        
        await generateResponse(question, context, {
          onChunk: (chunk: string) => {
            fullResponse += chunk;
            res.write(`data: ${JSON.stringify({ chunk, type: 'chunk' })}\n\n`);
          },
          onComplete: async () => {
            // Save the admin chat message
            const adminMessage = await storage.createChatMessage({
              projectId: projectIdNum,
              question,
              response: fullResponse,
              isAdminDebug: true, // Mark as admin debug session
            });

            res.write(`data: ${JSON.stringify({ 
              type: 'complete', 
              messageId: adminMessage.id,
              adminDebug: true 
            })}\n\n`);
            res.end();
          },
          onError: (error: Error) => {
            res.write(`data: ${JSON.stringify({ 
              type: 'error', 
              error: error.message 
            })}\n\n`);
            res.end();
          }
        });

      } catch (error) {
        console.error("Admin chat error:", error);
        res.write(`data: ${JSON.stringify({ 
          type: 'error', 
          error: 'Failed to generate response' 
        })}\n\n`);
        res.end();
      }

    } catch (error) {
      console.error("Admin chat with user project error:", error);
      res.status(500).json({ 
        message: "Failed to chat with user project",
        error: error instanceof Error ? error.message : "Unknown error"
      });
    }
  });

  // CORS middleware for embed endpoints
  const handleEmbedCORS = (req: any, res: any, next: any) => {
    const origin = req.get('Origin') || req.get('Referer');
    
    // For development, allow localhost
    if (process.env.NODE_ENV === 'development' && origin) {
      try {
        const originUrl = new URL(origin);
        if (originUrl.hostname === 'localhost' || originUrl.hostname === '127.0.0.1') {
          res.setHeader('Access-Control-Allow-Origin', originUrl.origin);
          res.setHeader('Access-Control-Allow-Credentials', 'true');
        }
      } catch {
        // Invalid URL, continue
      }
    }
    
    // Always set basic CORS headers for embed endpoints
    res.setHeader('Access-Control-Allow-Methods', 'GET, POST, OPTIONS, DELETE');
    res.setHeader('Access-Control-Allow-Headers', 'Content-Type, Authorization');
    res.setHeader('Access-Control-Max-Age', '86400'); // 24 hours
    res.setHeader('X-Frame-Options', 'ALLOWALL'); // Allow embedding in iframes
    
    if (req.method === 'OPTIONS') {
      return res.sendStatus(200);
    }
    
    next();
  };

  // Apply CORS middleware to all embed routes
  app.use('/api/embed/*', handleEmbedCORS);

  // EMBED DOMAIN MANAGEMENT ENDPOINTS
  
  // Get embed domains for user/project
  app.get("/api/embed/domains", authenticateUser, async (req: AuthenticatedRequest, res) => {
    try {
      const projectId = req.query.projectId ? parseInt(req.query.projectId as string) : undefined;
      console.log("Getting embed domains for user:", req.user!.id, "projectId:", projectId);
      const domains = await storage.getEmbedDomains(req.user!.id, projectId);
      console.log("Found embed domains:", domains.length);
      res.json(domains);
    } catch (error) {
      console.error("Get embed domains error:", error);
      res.status(500).json({ message: "Failed to get embed domains" });
    }
  });

  // Add embed domain
  app.post("/api/embed/domains", authenticateUser, async (req: AuthenticatedRequest, res) => {
    try {
      const { domain, projectId } = req.body;
      
      if (!domain || !isValidDomain(domain)) {
        return res.status(400).json({ message: "Invalid domain format" });
      }

      const embedDomain = await storage.addEmbedDomain(req.user!.id, domain, projectId);
      res.json(embedDomain);
    } catch (error) {
      console.error("Add embed domain error:", error);
      if (error instanceof Error && error.message.includes('duplicate key')) {
        return res.status(400).json({ message: "Domain already exists for this project" });
      }
      res.status(500).json({ message: "Failed to add embed domain" });
    }
  });

  // Remove embed domain
  app.delete("/api/embed/domains/:id", authenticateUser, async (req: AuthenticatedRequest, res) => {
    try {
      const domainId = parseInt(req.params.id);
      if (isNaN(domainId)) {
        return res.status(400).json({ message: "Invalid domain ID" });
      }
      
      await storage.removeEmbedDomain(domainId, req.user!.id);
      res.json({ message: "Domain removed successfully" });
    } catch (error) {
      console.error("Remove embed domain error:", error);
      res.status(500).json({ message: "Failed to remove embed domain" });
    }
  });

  // Generate embed token
  app.post("/api/embed/token", authenticateUser, async (req: AuthenticatedRequest, res) => {
    try {
      const { documentId, projectId } = req.body;
      
      if (documentId) {
        const document = await storage.getDocument(documentId);
        if (!document || document.userId !== req.user!.id) {
          return res.status(404).json({ message: "Document not found or access denied" });
        }
      }
      
      if (projectId) {
        const project = await storage.getProject(projectId);
        if (!project || project.userId !== req.user!.id) {
          return res.status(404).json({ message: "Project not found or access denied" });
        }
      }

      const token = generateEmbedToken(req.user!.id, documentId, projectId);
      res.json({ token });
    } catch (error) {
      console.error("Generate embed token error:", error);
      res.status(500).json({ message: "Failed to generate embed token" });
    }
  });

  // SECURED EMBED API ENDPOINTS
  
  // Get document for embed (secured)
  app.get("/api/embed/documents/:id", embedRateLimit, async (req, res) => {
    try {
      const id = parseInt(req.params.id);
      const document = await storage.getDocument(id);
      
      if (!document) {
        return res.status(404).json({ message: "Document not found" });
      }

      // Only return safe information for public embed
      res.json({
        id: document.id,
        filename: document.filename,
        status: document.status,
        createdAt: document.createdAt,
      });
    } catch (error) {
      console.error("Get embed document error:", error);
      res.status(500).json({ message: "Failed to get document" });
    }
  });

  // Get project for embed (public)
  app.get("/api/embed/projects/:id", async (req, res) => {
    try {
      const id = parseInt(req.params.id);
      const project = await storage.getProject(id);
      
      if (!project) {
        return res.status(404).json({ message: "Project not found" });
      }

      // Get document count
      const documents = await storage.getProjectDocuments(id);
      const readyDocuments = documents.filter(doc => doc.status === 'ready');

      // Only return safe information for public embed
      res.json({
        id: project.id,
        name: project.name,
        description: project.description,
        documentCount: documents.length,
        readyDocumentCount: readyDocuments.length,
        createdAt: project.createdAt,
      });
    } catch (error) {
      console.error("Get embed project error:", error);
      res.status(500).json({ message: "Failed to get project" });
    }
  });

  // Ask question for embed (secured)
  app.post("/api/embed/chat/ask", embedRateLimit, async (req, res) => {
    try {
      const validated = embedQuestionSchema.parse(req.body);
      const { projectId, documentId, question, token } = validated;

      // Get the owner of the document/project for credit deduction and authorization
      let ownerId: string | null = null;
      let targetProject: any = null;
      let targetDocument: any = null;
      
      if (documentId) {
        const document = await storage.getDocument(documentId);
        if (!document) {
          return res.status(404).json({ message: "Document not found" });
        }
        ownerId = document.userId;
        targetDocument = document;
      } else if (projectId) {
        const project = await storage.getProject(projectId);
        if (!project) {
          return res.status(404).json({ message: "Project not found" });
        }
        ownerId = project.userId;
        targetProject = project;
      }

      if (!ownerId) {
        return res.status(404).json({ message: "Document or project not found" });
      }

      // Check owner's limits before processing chat
      const limits = await storage.checkUserLimits(ownerId);
      if (!limits.canUseChat) {
        return res.status(403).json({ 
          message: "Insufficient credits for chat message",
          code: "CHAT_LIMIT_REACHED"
        });
      }

      // Verify embed token if provided, otherwise check referrer
      let isAuthorized = false;
      
      if (token) {
        const decoded = verifyEmbedToken(token);
        if (decoded && 
            ((decoded.documentId === documentId) || (decoded.projectId === projectId))) {
          isAuthorized = true;
        }
      } else {
        // Fallback to referrer check
        const referrer = req.get('Referer') || req.get('Origin');
        isAuthorized = await isEmbedAllowed(documentId || null, projectId || null, referrer);
      }

      if (!isAuthorized) {
        return res.status(403).json({ 
          message: "Embed not authorized for this domain. Please add your domain to the whitelist." 
        });
      }

      let context = "";

      if (projectId) {
        // Project-based chat (targetProject already fetched above)

        // Get all ready documents in the project
        const documents = await storage.getProjectDocuments(projectId);
        const readyDocuments = documents.filter(doc => doc.status === "ready");

        if (readyDocuments.length === 0) {
          return res.status(400).json({ message: "No ready documents in project" });
        }

        // Combine context from all documents
        context = readyDocuments
          .map(doc => {
            const parsedData = doc.parsedData as any;
            return `--- ${doc.filename} ---\n${parsedData?.text || "No content available"}`;
          })
          .join("\n\n");

      } else if (documentId) {
        // Document-based chat (targetDocument already fetched above)
        if (targetDocument.status !== "ready") {
          return res.status(400).json({ message: "Document is not ready for querying" });
        }

        const parsedData = targetDocument.parsedData as any;
        context = parsedData?.text || "No content available";
      }

      // Set headers for streaming with secure CORS
      res.setHeader('Content-Type', 'text/event-stream');
      res.setHeader('Cache-Control', 'no-cache');
      res.setHeader('Connection', 'keep-alive');
      
      // Set CORS based on referrer validation
      const referrer = req.get('Referer') || req.get('Origin');
      if (referrer) {
        try {
          const referrerUrl = new URL(referrer);
          res.setHeader('Access-Control-Allow-Origin', referrerUrl.origin);
        } catch {
          res.setHeader('Access-Control-Allow-Origin', 'null');
        }
      } else {
        res.setHeader('Access-Control-Allow-Origin', 'null');
      }
      
      res.setHeader('Access-Control-Allow-Headers', 'Content-Type');
      res.setHeader('X-Frame-Options', 'SAMEORIGIN'); // Prevent iframe embedding on unauthorized domains
      res.flushHeaders();

      let fullResponse = "";
      
      console.log(`Processing embed question: "${question}" for ${projectId ? 'project' : 'document'} ${projectId || documentId}`);
      
      try {
        // Stream response from OpenRouter
        const stream = await openRouterService.generateStreamResponse(question, context);
        
        for await (const chunk of stream) {
          fullResponse += chunk;
          res.write(`data: ${JSON.stringify({ content: chunk })}\n\n`);
        }
        
        // Check if OpenRouter response seems incomplete
        if (fullResponse.length > 0) {
          const trimmedResponse = fullResponse.trim();
          const isIncompleteResponse = 
            trimmedResponse.length > 10 && 
            !trimmedResponse.match(/[.!?]$/) && 
            !trimmedResponse.endsWith('**') &&
            !trimmedResponse.endsWith(':') &&
            trimmedResponse.length < 50; // Very short responses are likely incomplete
          
          if (isIncompleteResponse) {
            console.log(`OpenRouter response appears incomplete (${trimmedResponse.length} chars): "${trimmedResponse.slice(-30)}"`);
            console.log('Clearing response to trigger fallback');
            fullResponse = ""; // Clear to trigger fallback
          } else {
            console.log(`OpenRouter response completed successfully (${trimmedResponse.length} chars)`);
          }
        }
      } catch (error) {
        console.log(`OpenRouter failed, using enhanced content-based response for: "${question}"`);
        
        // Fallback to sending response in chunks
        const lowerQuestion = question.toLowerCase();
        const contextLines = context.split('\n').filter((line: string) => line.trim().length > 0);
        
        let fallbackResponse = "";
        
        // Add a notice about using fallback response
        const fallbackNotice = "*(Note: Using content-based response due to AI service interruption)*\n\n";
        
        // Enhanced keyword-based response generation
        if (lowerQuestion.includes('summary') || lowerQuestion.includes('summarize')) {
          fallbackResponse = fallbackNotice + `## Document Summary\n\nBased on the document content, here are the key points:\n\n`;
          const importantLines = contextLines
            .filter(line => line.length > 50)
            .slice(0, 5)
            .map((line, i) => `${i + 1}. ${line.trim()}`);
          fallbackResponse += importantLines.join('\n\n');
        } else if (lowerQuestion.includes('main') || lowerQuestion.includes('key')) {
          fallbackResponse = fallbackNotice + `## Key Information\n\nThe main points from the document include:\n\n`;
          const relevantLines = contextLines
            .filter(line => line.toLowerCase().includes('important') || line.toLowerCase().includes('key') || line.toLowerCase().includes('main'))
            .slice(0, 3);
          if (relevantLines.length === 0) {
            fallbackResponse += contextLines.slice(0, 3).map(line => `• ${line.trim()}`).join('\n');
          } else {
            fallbackResponse += relevantLines.map(line => `• ${line.trim()}`).join('\n');
          }
        } else {
          // Generic search through content
          const keywords = question.toLowerCase().split(' ').filter(word => word.length > 3);
          const relevantLines = contextLines.filter(line => 
            keywords.some(keyword => line.toLowerCase().includes(keyword))
          ).slice(0, 3);
          
          if (relevantLines.length > 0) {
            fallbackResponse = fallbackNotice + `Based on your question about "${question}", here's what I found:\n\n`;
            fallbackResponse += relevantLines.map(line => `• ${line.trim()}`).join('\n\n');
          } else {
            fallbackResponse = fallbackNotice + `I found information related to your question in the document:\n\n`;
            fallbackResponse += contextLines.slice(0, 2).map(line => `• ${line.trim()}`).join('\n\n');
            fallbackResponse += `\n\nFor more specific information, please try rephrasing your question or asking about specific topics mentioned in the document.`;
          }
        }
        
        // Split into chunks and stream
        const chunkSize = 50;
        for (let i = 0; i < fallbackResponse.length; i += chunkSize) {
          const chunk = fallbackResponse.slice(i, i + chunkSize);
          fullResponse += chunk;
          res.write(`data: ${JSON.stringify({ content: chunk })}\n\n`);
          await new Promise(resolve => setTimeout(resolve, 50)); // Simulate streaming
        }
      }

      // Deduct credits from the owner for embed chat usage
      try {
        const creditsDeducted = await storage.deductCredits(ownerId, 'chat_message');
        if (!creditsDeducted && limits.subscriptionStatus === "free") {
          console.error("Insufficient credits for embed chat message from owner:", ownerId);
        }
        
        // Log the embed usage for analytics
        await storage.trackEmbedUsage(
          ownerId, 
          documentId || undefined, 
          projectId || undefined, 
          req.get('Referer') || req.get('Origin'),
          req.ip,
          req.get('User-Agent')
        );

        console.log(`Embed chat completed - ${projectId ? 'Project' : 'Document'} ${projectId || documentId}: "${question}" -> ${fullResponse.length} chars, credits deducted: ${creditsDeducted}`);
      } catch (error) {
        console.error("Failed to deduct credits for embed chat:", error);
        // Don't break the response if credit deduction fails
      }

      res.write(`data: [DONE]\n\n`);
      res.end();
    } catch (error) {
      console.error("Embed ask question error:", error);
      
      // Check if headers have been sent (streaming started)
      if (res.headersSent) {
        // If streaming has started, just end the response
        res.write(`data: {"error": "An error occurred"}\n\n`);
        res.end();
      } else {
        // If streaming hasn't started, send JSON error
        res.status(500).json({ message: error instanceof Error ? error.message : "Failed to process question" });
      }
    }
  });

  // DISABLED: Embed chat history endpoints
  // These endpoints have been disabled to ensure embed sessions start fresh
  // Chat history is only available for authenticated platform users
  
  // app.get("/api/embed/chat/document/:id", async (req, res) => {
  //   // DISABLED: Embeds now start with fresh chat sessions for privacy
  //   res.status(404).json({ message: "Endpoint disabled - embeds start fresh" });
  // });

  // app.get("/api/embed/chat/project/:id", async (req, res) => {
  //   // DISABLED: Embeds now start with fresh chat sessions for privacy  
  //   res.status(404).json({ message: "Endpoint disabled - embeds start fresh" });
  // });

  // Delete a document
  app.delete("/api/documents/:id", authenticateUser, async (req, res) => {
    try {
      const documentId = req.params.id;
      
      // Get document details first (needed for vector DB deletion)
      const document = await storage.getDocument(documentId);
      if (!document) {
        return res.status(404).json({ error: "Document not found" });
      }

      // Get project to access indexId
      const project = await storage.getProject(document.projectId);
      if (!project) {
        return res.status(404).json({ error: "Project not found" });
      }

      // Delete from vector database first (if indexId exists)
      if (project.indexId) {
        try {
          // Try multiple deletion methods based on what LlamaIndex Cloud supports
          if (document.filename) {
            // Method 1: Remove by document name
            await llamaIndexService.removeDocumentFromIndex(project.indexId, document.filename);
          } else {
            // Method 2: Delete by document ID (if supported)
            await llamaIndexService.deleteDocument(project.indexId, documentId);
          }
          console.log(`Successfully removed document ${documentId} from vector index ${project.indexId}`);
        } catch (vectorError) {
          console.warn(`Failed to remove document from vector database: ${vectorError}`);
          // Continue with database deletion even if vector deletion fails
        }
      }

      // Delete from database
      const deleted = await storage.deleteDocument(documentId);
      
      if (!deleted) {
        return res.status(404).json({ error: "Document not found" });
      }

      res.json({ 
        success: true, 
        message: "Document and its embeddings deleted successfully" 
      });
    } catch (error) {
      console.error("Error deleting document:", error);
      res.status(500).json({ error: "Failed to delete document" });
    }
  });

  const httpServer = createServer(app);
  return httpServer;
}

// Background document processing
async function processDocumentAsync(documentId: number, fileBuffer: Buffer, filename: string) {
  try {
    console.log(`Starting processing for document ${documentId}`);
    
    // Update status to parsing
    await storage.updateDocumentStatus(documentId, "parsing");
    console.log(`Document ${documentId} status updated to parsing`);

    // Simulate parsing delay - longer so UI shows progress
    await new Promise(resolve => setTimeout(resolve, 5000));

    // Process different file types appropriately
    let parsedText = "";
    let pageCount = 1;
    let wordCount = 0;

    const fileExtension = filename.toLowerCase().split('.').pop();
    const textExtensions = ['txt', 'csv', 'html', 'htm', 'md', 'xml'];

    if (textExtensions.includes(fileExtension || '')) {
      // Handle plain text files directly
      parsedText = fileBuffer.toString('utf-8');
      wordCount = parsedText.split(/\s+/).filter(word => word.length > 0).length;
      console.log(`Processed text file: ${wordCount} words`);
    } else {
      // Use LlamaParse for all other supported formats (PDF, Office, images, etc.)
      try {
        console.log(`Using LlamaParse for ${fileExtension} file: ${filename}`);
        const parseResult = await llamaParseService.parseFile(fileBuffer, filename);
        
        if (parseResult.status === "error") {
          throw new Error(parseResult.error || "Document parsing failed");
        }

        if (!parseResult.result) {
          throw new Error("Document parsing completed but no content was extracted");
        }

        parsedText = parseResult.result.text;
        pageCount = parseResult.result.metadata.page_count;
        wordCount = parseResult.result.metadata.word_count;
        console.log(`LlamaParse processed: ${pageCount} pages, ${wordCount} words`);
      } catch (error) {
        // Log the actual error for debugging
        console.error(`Document processing error for ${filename}:`, error);
        
        // Return user-friendly error message
        if (error instanceof Error && error.message.includes("Document parsing")) {
          // Already sanitized from LlamaParse service
          throw error;
        }
        
        // Generic fallback for any other errors
        throw new Error("Unable to process document. Please check the file format and try again.");
      }
    }

    // Update status to indexing
    await storage.updateDocumentStatus(documentId, "indexing", {
      parsedData: { text: parsedText, metadata: { page_count: pageCount, word_count: wordCount } },
      pageCount,
      wordCount,
    });
    console.log(`Document ${documentId} status updated to indexing`);

    // Simulate indexing delay - longer so UI shows progress
    await new Promise(resolve => setTimeout(resolve, 4000));

    // For testing: generate a mock index ID
    const indexId = `test_index_${documentId}_${Date.now()}`;

    // Generate AI summary (async, after document is ready)
    let documentSummary = null;
    try {
      if (summarizationService.shouldSummarize("ready", wordCount)) {
        console.log(`Generating AI summary for document ${documentId}`);
        documentSummary = await summarizationService.generateDocumentSummary(
          parsedText, 
          filename, 
          pageCount, 
          wordCount
        );
        console.log(`AI summary generated for document ${documentId}`);
      }
    } catch (summaryError) {
      console.error(`Failed to generate summary for document ${documentId}:`, summaryError);
      // Don't fail the entire process if summarization fails
    }

    // Update status to ready with summary
    await storage.updateDocumentStatus(documentId, "ready", {
      indexId: indexId,
      summary: documentSummary,
    });

    console.log(`Document ${documentId} processed successfully with index ${indexId} and summary`);

  } catch (error) {
    console.error("Document processing error:", error);
    await storage.updateDocumentError(
      documentId, 
      error instanceof Error ? error.message : "Processing failed"
    );
  }
}

