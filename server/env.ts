import dotenv from "dotenv";
import { fileURLToPath } from "url";
import { dirname, resolve } from "path";
import fs from "fs";

// Get the directory of the current module safely
let __dirname;
try {
  const __filename = fileURLToPath(import.meta.url);
  __dirname = dirname(__filename);
} catch (error) {
  // Fallback for production builds
  __dirname = process.cwd();
}

// Load .env file if it exists (for local development and testing)
// In actual production (Railway, etc.), environment variables are provided directly
const envPath = resolve(__dirname, "../.env");

if (fs.existsSync(envPath)) {
  if (process.env.NODE_ENV !== 'production') {
    console.log("Development: Loading environment variables from .env file");
  }
  dotenv.config({ path: envPath });
} else if (process.env.NODE_ENV !== "production") {
  dotenv.config(); // Try default location
}

// Secure environment variable validation
const isProduction = process.env.NODE_ENV === "production";

if (isProduction) {
  console.log("Production environment initialized");
  
  // Only log basic status information in production
  const requiredVars = ["PORT"];
  const missingVars = requiredVars.filter(varName => !process.env[varName]);
  
  if (missingVars.length > 0) {
    console.error("Critical configuration missing - application may not function properly");
  } else {
    console.log("Required environment variables configured");
  }
} else {
  // Development logging - more verbose but still secure
  console.log("Development environment:");
  console.log("Configuration status:");
  console.log("- LLAMA_CLOUD_API_KEY:", process.env.LLAMA_CLOUD_API_KEY ? "✓ SET" : "✗ NOT SET");
  console.log("- DATABASE_URL:", process.env.DATABASE_URL ? "✓ SET" : "✗ NOT SET");
  console.log("- SUPABASE_URL:", process.env.SUPABASE_URL ? "✓ SET" : "✗ NOT SET");
  console.log("- SUPABASE_SERVICE_ROLE_KEY:", process.env.SUPABASE_SERVICE_ROLE_KEY ? "✓ SET" : "✗ NOT SET");
  console.log("- OPENROUTER_API_KEY:", process.env.OPENROUTER_API_KEY ? "✓ SET" : "✗ NOT SET");
  console.log("- EMBED_JWT_SECRET:", process.env.EMBED_JWT_SECRET ? "✓ SET" : "✗ NOT SET");
}
