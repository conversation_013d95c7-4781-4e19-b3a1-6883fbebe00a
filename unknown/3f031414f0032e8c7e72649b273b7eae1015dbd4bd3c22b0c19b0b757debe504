import {
  <PERSON><PERSON><PERSON>,
  Column,
  PrimaryGeneratedColumn,
  OneToOne,
  OneToMany,
  CreateDateColumn,
  UpdateDateColumn,
  JoinColumn,
} from 'typeorm';
import { Application } from '../../application/entities/application.entity';
import { ChatAiDocument } from './document.entity';
import { ChatAiMessage } from './message.entity';
import { ChatAiCreditUsage } from './credit-usage.entity';
import { ChatAiApiTransaction } from './transaction.entity';

@Entity('chat_ai_projects')
export class ChatAi {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column({ nullable: false })
  name: string;

  @Column({ type: 'text', nullable: true })
  description: string;

  @Column({ nullable: false })
  userId: string; // Supabase user ID

  @Column({ type: 'boolean', default: true })
  isActive: boolean;

  @Column({ type: 'int', default: 1000 })
  credits: number;

  @Column({ type: 'varchar', default: 'free' })
  subscriptionStatus: string; // free, pro, enterprise

  @Column({ type: 'boolean', default: true })
  notificationsEnabled: boolean;

  @Column({ type: 'varchar', nullable: true })
  notificationEmail: string;

  @Column({ type: 'jsonb', nullable: true })
  settings: any; // Additional project settings

  @CreateDateColumn({ type: 'timestamp', default: () => 'CURRENT_TIMESTAMP' })
  createdAt: Date;

  @UpdateDateColumn({
    type: 'timestamp',
    default: () => 'CURRENT_TIMESTAMP',
    onUpdate: 'CURRENT_TIMESTAMP',
  })
  updatedAt: Date;

  // Relationships
  @OneToOne(() => Application, (application) => application.chatAi, {
    onDelete: 'CASCADE',
  })
  @JoinColumn({ name: 'appId' })
  app: Application;

  @Column({ nullable: false })
  appId: string;

  @OneToMany(() => ChatAiDocument, (document) => document.project)
  documents: ChatAiDocument[];

  @OneToMany(() => ChatAiMessage, (message) => message.chatAi)
  messages: ChatAiMessage[];

  @OneToMany(() => ChatAiCreditUsage, (creditUsage) => creditUsage.chatAi)
  creditUsage: ChatAiCreditUsage[];

  @OneToMany(() => ChatAiApiTransaction, (transaction) => transaction.chatAi)
  transactions: ChatAiApiTransaction[];
}
