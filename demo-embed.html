<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Enhanced Document Chat Embed Demo</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            margin: 0;
            padding: 40px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            padding: 40px;
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
        }
        .header {
            text-align: center;
            margin-bottom: 40px;
        }
        .header h1 {
            color: #2d3748;
            font-size: 2.5rem;
            margin-bottom: 10px;
            font-weight: 700;
        }
        .header p {
            color: #718096;
            font-size: 1.1rem;
        }
        .features-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin-bottom: 40px;
        }
        .feature-card {
            background: linear-gradient(135deg, #4299e1 0%, #3182ce 100%);
            color: white;
            padding: 20px;
            border-radius: 15px;
            text-align: center;
        }
        .feature-card h3 {
            margin: 0 0 10px 0;
            font-size: 1.2rem;
        }
        .embed-section {
            margin: 30px 0;
            padding: 25px;
            border: 2px solid #e2e8f0;
            border-radius: 15px;
            background: #f8fafc;
        }
        .embed-title {
            font-size: 20px;
            font-weight: bold;
            margin-bottom: 15px;
            color: #2d3748;
            display: flex;
            align-items: center;
        }
        .embed-title .icon {
            margin-right: 10px;
            font-size: 24px;
        }
        .embed-iframe {
            width: 100%;
            height: 600px;
            border: 1px solid #e2e8f0;
            border-radius: 12px;
            margin: 15px 0;
        }
        .embed-iframe.minimal {
            height: 400px;
        }
        .code-block {
            background: #1a202c;
            color: #e2e8f0;
            padding: 20px;
            border-radius: 10px;
            font-family: 'Monaco', 'Consolas', monospace;
            font-size: 14px;
            margin: 15px 0;
            overflow-x: auto;
            position: relative;
        }
        .copy-btn {
            background: linear-gradient(135deg, #4299e1 0%, #3182ce 100%);
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 8px;
            cursor: pointer;
            margin: 8px 5px;
            font-weight: 500;
            transition: all 0.2s;
        }
        .copy-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(66, 153, 225, 0.4);
        }
        .tabs {
            display: flex;
            border-bottom: 2px solid #e2e8f0;
            margin-bottom: 20px;
        }
        .tab {
            padding: 10px 20px;
            cursor: pointer;
            border-bottom: 2px solid transparent;
            font-weight: 500;
            color: #718096;
        }
        .tab.active {
            color: #3182ce;
            border-bottom-color: #3182ce;
        }
        .tab-content {
            display: none;
        }
        .tab-content.active {
            display: block;
        }
        .customization-options {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 15px;
            margin: 20px 0;
        }
        .option-card {
            background: white;
            border: 2px solid #e2e8f0;
            padding: 15px;
            border-radius: 10px;
            text-align: center;
        }
        .option-card h4 {
            margin: 0 0 10px 0;
            color: #2d3748;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🚀 Enhanced Chat Embed</h1>
            <p>Embed AI-powered document chat into any website with advanced customization options</p>
        </div>

        <!-- Features Grid -->
        <div class="features-grid">
            <div class="feature-card">
                <h3>📄 Document Chat</h3>
                <p>Single document Q&A with intelligent responses</p>
            </div>
            <div class="feature-card">
                <h3>📁 Project Chat</h3>
                <p>Multi-document conversations across entire projects</p>
            </div>
            <div class="feature-card">
                <h3>🎨 Customizable Themes</h3>
                <p>Light, dark, and minimal themes available</p>
            </div>
            <div class="feature-card">
                <h3>⚡ Real-time Streaming</h3>
                <p>Live response streaming for better UX</p>
            </div>
        </div>

        <!-- Document Embed Demo -->
        <div class="embed-section">
            <div class="embed-title">
                <span class="icon">📄</span>
                Document Chat Embed
            </div>
            
            <div class="tabs">
                <div class="tab active" onclick="switchTab('doc', 'url')">Direct URL</div>
                <div class="tab" onclick="switchTab('doc', 'iframe')">Embed Code</div>
                <div class="tab" onclick="switchTab('doc', 'custom')">Customization</div>
            </div>

            <div id="doc-url" class="tab-content active">
                <p>Share this URL directly or open in new window:</p>
                <div class="code-block">${window.location.origin}/embed-chat?documentId=1</div>
                <button class="copy-btn" onclick="copyToClipboard('${window.location.origin}/embed-chat?documentId=1')">Copy URL</button>
            </div>

            <div id="doc-iframe" class="tab-content">
                <p>Copy this HTML code to embed in your website:</p>
                <div class="code-block">&lt;iframe src="${window.location.origin}/embed-chat?documentId=1" width="100%" height="600" style="border: 1px solid #e2e8f0; border-radius: 8px;" title="Document Chat Widget"&gt;&lt;/iframe&gt;</div>
                <button class="copy-btn" onclick="copyEmbedCode('document')">Copy Embed Code</button>
            </div>

            <div id="doc-custom" class="tab-content">
                <p>Customize the embed appearance and behavior:</p>
                <div class="customization-options">
                    <div class="option-card">
                        <h4>🎯 Minimal Mode</h4>
                        <div class="code-block">${window.location.origin}/embed-chat?documentId=1&minimal=true</div>
                        <button class="copy-btn" onclick="copyToClipboard('${window.location.origin}/embed-chat?documentId=1&minimal=true')">Copy</button>
                    </div>
                    <div class="option-card">
                        <h4>🌙 Dark Theme</h4>
                        <div class="code-block">${window.location.origin}/embed-chat?documentId=1&theme=dark</div>
                        <button class="copy-btn" onclick="copyToClipboard('${window.location.origin}/embed-chat?documentId=1&theme=dark')">Copy</button>
                    </div>
                    <div class="option-card">
                        <h4>🙈 Hide Header</h4>
                        <div class="code-block">${window.location.origin}/embed-chat?documentId=1&hideHeader=true</div>
                        <button class="copy-btn" onclick="copyToClipboard('${window.location.origin}/embed-chat?documentId=1&hideHeader=true')">Copy</button>
                    </div>
                </div>
            </div>
        </div>

        <!-- Project Embed Demo -->
        <div class="embed-section">
            <div class="embed-title">
                <span class="icon">📁</span>
                Project Chat Embed (NEW!)
            </div>
            
            <div class="tabs">
                <div class="tab active" onclick="switchTab('proj', 'url')">Direct URL</div>
                <div class="tab" onclick="switchTab('proj', 'iframe')">Embed Code</div>
                <div class="tab" onclick="switchTab('proj', 'custom')">Customization</div>
            </div>

            <div id="proj-url" class="tab-content active">
                <p>Chat with multiple documents in a project:</p>
                <div class="code-block">${window.location.origin}/embed-chat?projectId=1</div>
                <button class="copy-btn" onclick="copyToClipboard('${window.location.origin}/embed-chat?projectId=1')">Copy URL</button>
            </div>

            <div id="proj-iframe" class="tab-content">
                <p>Embed project chat in your website:</p>
                <div class="code-block">&lt;iframe src="${window.location.origin}/embed-chat?projectId=1" width="100%" height="600" style="border: 1px solid #e2e8f0; border-radius: 8px;" title="Project Chat Widget"&gt;&lt;/iframe&gt;</div>
                <button class="copy-btn" onclick="copyEmbedCode('project')">Copy Embed Code</button>
            </div>

            <div id="proj-custom" class="tab-content">
                <p>Customize the project chat embed:</p>
                <div class="customization-options">
                    <div class="option-card">
                        <h4>🎯 Minimal Project Chat</h4>
                        <div class="code-block">${window.location.origin}/embed-chat?projectId=1&minimal=true</div>
                        <button class="copy-btn" onclick="copyToClipboard('${window.location.origin}/embed-chat?projectId=1&minimal=true')">Copy</button>
                    </div>
                    <div class="option-card">
                        <h4>🌙 Dark Project Theme</h4>
                        <div class="code-block">${window.location.origin}/embed-chat?projectId=1&theme=dark</div>
                        <button class="copy-btn" onclick="copyToClipboard('${window.location.origin}/embed-chat?projectId=1&theme=dark')">Copy</button>
                    </div>
                </div>
            </div>
        </div>

        <!-- Live Demo Section -->
        <div class="embed-section">
            <div class="embed-title">
                <span class="icon">💬</span>
                Live Demo - Default Theme
            </div>
            <p>Try the chat widget below (embedded with default settings):</p>
            <iframe 
                src="${window.location.origin}/embed-chat?documentId=1" 
                class="embed-iframe"
                title="Document Chat Widget Demo">
            </iframe>
        </div>

        <!-- Minimal Demo Section -->
        <div class="embed-section">
            <div class="embed-title">
                <span class="icon">🎯</span>
                Live Demo - Minimal Theme
            </div>
            <p>Compact version perfect for smaller spaces:</p>
            <iframe 
                src="${window.location.origin}/embed-chat?documentId=1&minimal=true" 
                class="embed-iframe minimal"
                title="Minimal Document Chat Widget Demo">
            </iframe>
        </div>

        <!-- API Information -->
        <div class="embed-section">
            <div class="embed-title">
                <span class="icon">🔧</span>
                Available Parameters
            </div>
            <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 15px;">
                <div style="background: white; padding: 15px; border-radius: 10px; border: 1px solid #e2e8f0;">
                    <strong>documentId</strong><br>
                    <small>ID of document to chat with</small>
                </div>
                <div style="background: white; padding: 15px; border-radius: 10px; border: 1px solid #e2e8f0;">
                    <strong>projectId</strong><br>
                    <small>ID of project to chat with</small>
                </div>
                <div style="background: white; padding: 15px; border-radius: 10px; border: 1px solid #e2e8f0;">
                    <strong>minimal</strong><br>
                    <small>true/false - Compact view</small>
                </div>
                <div style="background: white; padding: 15px; border-radius: 10px; border: 1px solid #e2e8f0;">
                    <strong>theme</strong><br>
                    <small>default/dark/minimal</small>
                </div>
                <div style="background: white; padding: 15px; border-radius: 10px; border: 1px solid #e2e8f0;">
                    <strong>hideHeader</strong><br>
                    <small>true/false - Hide title bar</small>
                </div>
            </div>
        </div>
    </div>

    <script>
        function switchTab(section, tab) {
            // Hide all tab contents for this section
            const contents = document.querySelectorAll(`#${section}-url, #${section}-iframe, #${section}-custom`);
            contents.forEach(content => content.classList.remove('active'));
            
            // Remove active class from all tabs in this section
            const tabs = document.querySelectorAll(`[onclick*="${section}"]`);
            tabs.forEach(t => t.classList.remove('active'));
            
            // Show selected tab content
            document.getElementById(`${section}-${tab}`).classList.add('active');
            
            // Add active class to clicked tab
            event.target.classList.add('active');
        }

        function copyToClipboard(text) {
            navigator.clipboard.writeText(text).then(function() {
                showNotification('URL copied to clipboard!');
            });
        }

        function copyEmbedCode(type) {
            let embedCode;
            if (type === 'document') {
                embedCode = `<iframe src="${window.location.origin}/embed-chat?documentId=1" width="100%" height="600" style="border: 1px solid #e2e8f0; border-radius: 8px;" title="Document Chat Widget"></iframe>`;
            } else {
                embedCode = `<iframe src="${window.location.origin}/embed-chat?projectId=1" width="100%" height="600" style="border: 1px solid #e2e8f0; border-radius: 8px;" title="Project Chat Widget"></iframe>`;
            }
            
            navigator.clipboard.writeText(embedCode).then(function() {
                showNotification('Embed code copied to clipboard!');
            });
        }

        function showNotification(message) {
            // Create notification element
            const notification = document.createElement('div');
            notification.style.cssText = `
                position: fixed;
                top: 20px;
                right: 20px;
                background: linear-gradient(135deg, #48bb78 0%, #38a169 100%);
                color: white;
                padding: 15px 20px;
                border-radius: 10px;
                box-shadow: 0 10px 25px rgba(0,0,0,0.2);
                z-index: 1000;
                font-weight: 500;
                animation: slideIn 0.3s ease-out;
            `;
            notification.textContent = message;
            
            // Add animation
            const style = document.createElement('style');
            style.textContent = `
                @keyframes slideIn {
                    from { transform: translateX(100%); opacity: 0; }
                    to { transform: translateX(0); opacity: 1; }
                }
            `;
            document.head.appendChild(style);
            
            document.body.appendChild(notification);
            
            // Remove after 3 seconds
            setTimeout(() => {
                notification.remove();
            }, 3000);
        }

        // Replace ${window.location.origin} with actual origin when page loads
        document.addEventListener('DOMContentLoaded', function() {
            const origin = window.location.origin;
            document.body.innerHTML = document.body.innerHTML.replace(/\$\{window\.location\.origin\}/g, origin);
        });
    </script>
</body>
</html>