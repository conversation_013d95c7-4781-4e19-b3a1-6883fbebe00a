import { Application } from '../../application/entities/application.entity';
import {
  Entity,
  PrimaryGeneratedColumn,
  Column,
  ManyToOne,
  JoinColumn,
  CreateDateColumn,
  UpdateDateColumn,
} from 'typeorm';

@Entity()
export class Transaction {
  @PrimaryGeneratedColumn()
  id: number;

  @Column({ nullable: true })
  hash: string;

  @Column({ nullable: true })
  type: number;

  @Column({ nullable: true })
  blockHash: string;

  @Column({ nullable: true })
  blockNumber: number;

  @Column({ nullable: true })
  from: string;

  @Column({
    default: 0,
    type: 'decimal',
    precision: 30,
    scale: 4,
    transformer: {
      to(value) {
        return value;
      },
      from(value) {
        return parseFloat(value);
      },
    },
  })
  gasPrice: string;

  @Column({
    default: 0,
    type: 'decimal',
    precision: 30,
    scale: 4,
    transformer: {
      to(value) {
        return value;
      },
      from(value) {
        return parseFloat(value);
      },
    },
  })
  maxPriorityFeePerGas: string;

  @Column({
    default: 0,
    type: 'decimal',
    precision: 30,
    scale: 4,
    transformer: {
      to(value) {
        return value;
      },
      from(value) {
        return parseFloat(value);
      },
    },
  })
  maxFeePerGas: string;

  @Column({
    default: 0,
    type: 'decimal',
    precision: 30,
    scale: 4,
    transformer: {
      to(value) {
        return value;
      },
      from(value) {
        return parseFloat(value);
      },
    },
  })
  gasLimit: string;

  @Column({ nullable: true })
  to: string;

  @Column({
    default: 0,
    type: 'decimal',
    precision: 30,
    scale: 4,
    transformer: {
      to(value) {
        return value;
      },
      from(value) {
        return parseFloat(value);
      },
    },
  })
  value: string;

  @Column({ nullable: true })
  nonce: number;

  @Column({ nullable: true })
  data: string;

  @Column({ nullable: true })
  creates: string;

  @Column({ nullable: true })
  chainId: number;

  @Column({
    type: 'enum',
    default: 'deposit',
    enum: ['deposit', 'withdraw'],
  })
  txType: string;

  @Column({
    type: 'enum',
    default: 'pending',
    enum: ['pending', 'confirmed', 'failed', 'rejected'],
  })
  status: string;

  @Column({
    type: 'text',
    nullable: true,
    transformer: {
      to(value) {
        return JSON.stringify(value);
      },
      from(value) {
        return JSON.parse(value);
      },
    },
  })
  receipt: string;

  @Column({
    type: 'enum',
    default: 'paymaster',
    enum: ['paymaster', 'relayer'],
  })
  service: string;

  @ManyToOne(() => Application, (app) => app.transactions, {
    cascade: ['remove'],
    onDelete: 'CASCADE',
  })
  @JoinColumn()
  app: Application;

  @CreateDateColumn({ type: 'timestamp', default: () => 'CURRENT_TIMESTAMP' })
  createdAt: Date;

  @UpdateDateColumn({
    type: 'timestamp',
    default: () => 'CURRENT_TIMESTAMP',
    onUpdate: 'CURRENT_TIMESTAMP',
  })
  updatedAt: Date;
}
