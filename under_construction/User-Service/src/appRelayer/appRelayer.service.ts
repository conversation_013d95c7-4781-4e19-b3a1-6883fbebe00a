import { HttpStatus, Injectable, Logger } from '@nestjs/common';
import {
  DataSource,
  Repository,
  In,
  Not,
  FindOneOptions,
  EntityTarget,
  ObjectLiteral,
  ILike,
} from 'typeorm';
import { AppRelayer } from './entities/appRelayer.entity';
import { InjectRepository } from '@nestjs/typeorm';
import {
  GetAppTransactionDto,
  GetContractAbiDto,
  CreateSmartContractDto,
  UpdateSmartContractDto,
  ListSmartContractsDto,
  RemoveSmartContractDto,
  CreateRelayerDto,
  UpdateRelayerDto,
  FetchSingleRelayerDto,
  FetchRelayersDto,
  RemoveRelayerDto,
  UpdateRelayerSettingDto,
  GetAppRelayerTransactionDto,
} from './dto/appRelayer.dto';
import {
  CommonMessage,
  AppMessage,
  TransactionMessage,
  UserMessage,
} from '../CommonMessages/CommonMessages';
import { compareArrays, paginate } from '../utils/common.service';
import { isValidContractAddress } from '../utils/utils.ethers';
import { Transaction } from '../transaction/entity/transaction.entity';
import { TransactionService } from '../transaction/transaction.service';
import { ValidForABi } from '../utils/constants';
import { AbiService } from '../utils/abi.service';
import { Application } from '../application/entities/application.entity';
import { OriginsService } from '../origins/origins.service';
import { Bundler } from 'src/bundler/entities/bundler.entity';
import { OriginType } from 'src/origins/dto/origins.dto';
import {
  Paymaster,
  SmartContract,
} from 'src/paymaster/entities/paymaster.entity';
import Decimal from 'decimal.js';
import { RelayersTransaction } from './entities/transaction.entity';

@Injectable()
export class AppRelayerService {
  private readonly logger = new Logger(AppRelayerService.name, {
    timestamp: true,
  });
  constructor(
    @InjectRepository(Application)
    private readonly applicationRepository: Repository<Application>,
    @InjectRepository(AppRelayer)
    private readonly appRelayerRepository: Repository<AppRelayer>,
    private readonly transactionService: TransactionService,
    private readonly dataSource: DataSource,
    private readonly abiService: AbiService,
    private readonly originsService: OriginsService,
  ) {}

  async setupRelayer(userId: number, payload: CreateRelayerDto) {
    try {
      const app = await this.applicationRepository.findOne({
        where: { id: payload.appId, user: { id: userId } },
      });
      if (!app) {
        return {
          error: true,
          statusCode: HttpStatus.BAD_REQUEST,
          message: AppMessage.AppNotFound(),
        };
      }
      if (!app.isActive) {
        return {
          error: true,
          statusCode: HttpStatus.BAD_REQUEST,
          message: AppMessage.AppIsInactive,
        };
      }
      const relayer = await this.appRelayerRepository.findOne({
        where: { app: { id: payload.appId } },
      });
      if (relayer) {
        return {
          error: true,
          statusCode: HttpStatus.BAD_REQUEST,
          message: AppMessage.AlreadySetup('relayer'),
        };
      }
      const newRelayer = this.appRelayerRepository.create({
        relayerName: payload.name,
        app: { id: payload.appId },
      });
      await this.appRelayerRepository.save(newRelayer);
      return {
        error: false,
        statusCode: HttpStatus.CREATED,
        message: AppMessage.AppCreated('Relayer'),
      };
    } catch (error) {
      return {
        error: true,
        statusCode: HttpStatus.INTERNAL_SERVER_ERROR,
        message: CommonMessage.InternalError,
      };
    }
  }

  async updateRelayer(userId: number, updateRelayerDto: UpdateRelayerDto) {
    try {
      let responseMessage = null;
      const relayer = await this.dataSource
        .getRepository(AppRelayer)
        .createQueryBuilder('relayer')
        .innerJoinAndSelect('relayer.app', 'app', 'app.id = :id', {
          id: updateRelayerDto.appId,
        })
        .innerJoin('app.user', 'user', 'user.id = :userId', {
          userId: userId,
        })
        .getOne();
      if (!relayer) {
        return {
          error: true,
          statusCode: HttpStatus.NOT_FOUND,
          message: AppMessage.AppNotFound('relayer'),
        };
      }
      if (!relayer.app.isActive) {
        return {
          error: true,
          statusCode: HttpStatus.BAD_REQUEST,
          message: AppMessage.AppIsInactive,
        };
      }
      if (
        updateRelayerDto.relayerName &&
        updateRelayerDto.relayerName === relayer.relayerName
      ) {
        return {
          error: true,
          statusCode: HttpStatus.BAD_REQUEST,
          message: AppMessage.SameValues,
        };
      }

      if (updateRelayerDto.relayerName !== undefined) {
        responseMessage = `Relayer updated`;
        relayer.relayerName = updateRelayerDto.relayerName;
      }
      if (updateRelayerDto.isActive !== undefined) {
        if (relayer.isActive === updateRelayerDto.isActive) {
          return {
            error: true,
            statusCode: HttpStatus.BAD_REQUEST,
            message: AppMessage.AppAlreadySetSame('isActive is'),
          };
        }
        responseMessage = `Relayer ${updateRelayerDto.isActive ? 'active' : 'inactive'}`;
        relayer.isActive = updateRelayerDto.isActive;
      }
      await this.appRelayerRepository.save(relayer);
      return {
        error: false,
        statusCode: HttpStatus.OK,
        message: responseMessage
          ? AppMessage.AppUpdated(responseMessage)
          : AppMessage.AppUpdated('Relayer updated'),
      };
    } catch (error) {
      this.logger.error(
        'Error in catch at updateRelayer app:',
        JSON.stringify(error),
      );
      return {
        error: true,
        statusCode: HttpStatus.INTERNAL_SERVER_ERROR,
        message: CommonMessage.InternalError,
      };
    }
  }

  async updateRelayerSettings(
    userId: number | null,
    updateRelayerSettingDto: UpdateRelayerSettingDto,
  ) {
    try {
      const { appId, ...updateFields } = updateRelayerSettingDto;

      // Build query to fetch the relayer with the associated app
      let query = this.dataSource
        .getRepository(AppRelayer)
        .createQueryBuilder('relayer')
        .innerJoinAndSelect('relayer.app', 'app', 'app.id = :appId', { appId });

      // If userId is provided, check user ownership
      if (userId) {
        query = query.innerJoin('app.user', 'user', 'user.id = :userId', {
          userId,
        });
      }

      const relayer = await query.getOne();

      if (!relayer) {
        return {
          error: true,
          statusCode: HttpStatus.NOT_FOUND,
          message: AppMessage.AppNotFound('relayer'),
        };
      }

      if (!relayer.app.isActive) {
        return {
          error: true,
          statusCode: HttpStatus.BAD_REQUEST,
          message: AppMessage.AppIsInactive,
        };
      }

      let updated = false;
      const updatedFields: Partial<AppRelayer> = {};

      for (const key in updateFields) {
        if (
          updateFields.hasOwnProperty(key) &&
          relayer.hasOwnProperty(key) &&
          updateFields[key] !== undefined
        ) {
          // Special handling for boolean fields
          if (typeof updateFields[key] === 'boolean') {
            if (updateFields[key] !== relayer[key]) {
              updatedFields[key] = updateFields[key];
              updated = true;
            }
          }
          // Handle numeric fields that need to be stored as strings
          else if (['balance', 'blockedBalance', 'threshold'].includes(key)) {
            if (String(updateFields[key]) !== String(relayer[key])) {
              updatedFields[key] = String(updateFields[key]);
              updated = true;
            }
          }
          // Handle all other fields
          else if (updateFields[key] !== relayer[key]) {
            updatedFields[key] = updateFields[key];
            updated = true;
          }
        }
      }

      if (!updated) {
        return {
          error: true,
          statusCode: HttpStatus.BAD_REQUEST,
          message: AppMessage.SameValues,
        };
      }

      // Update the relayer
      Object.assign(relayer, updatedFields);
      await this.appRelayerRepository.save(relayer);

      return {
        error: false,
        statusCode: HttpStatus.OK,
        message: AppMessage.AppUpdated('Relayer settings updated'),
      };
    } catch (error) {
      this.logger.error(
        'Error in updateRelayerSettings:',
        JSON.stringify(error),
      );
      return {
        error: true,
        statusCode: HttpStatus.INTERNAL_SERVER_ERROR,
        message: CommonMessage.InternalError,
      };
    }
  }

  async updateAppRelayerBalances(updateData): Promise<AppRelayer | null> {
    console.log('updateData', updateData);
    const queryRunner = this.dataSource.createQueryRunner();
    await queryRunner.connect();
    await queryRunner.startTransaction();

    try {
      // Find the app relayer
      const appRelayer = await this.appRelayerRepository.findOne({
        where: { app: { id: updateData.appId } },
        relations: ['app'],
      });

      if (!appRelayer) {
        this.logger.error(
          `AppRelayer not found for appId: ${updateData.appId}`,
        );
        throw new Error('AppRelayer not found');
      }

      // Use Decimal.js for precise calculations
      const currentBlockedBalance = new Decimal(
        appRelayer.blockedBalance || '0',
      );
      const currentBalance = new Decimal(appRelayer.balance || '0');

      // Update blocked balance (addInBA)
      if (updateData.addInBA !== undefined) {
        const addInBA = new Decimal(updateData.addInBA);
        const newBlockedBalance = currentBlockedBalance.add(addInBA);

        if (newBlockedBalance.isNegative()) {
          this.logger.warn(
            `Blocked balance cannot be negative: ${newBlockedBalance.toFixed(18)}`,
          );
          throw new Error('Insufficient blocked balance');
        }

        appRelayer.blockedBalance = newBlockedBalance.toFixed(18);
        this.logger.log(
          `Updated Blocked Balance: ${appRelayer.blockedBalance}`,
        );
      }

      // Update total balance (addInTA)
      if (updateData.addInTA !== undefined) {
        const addInTA = new Decimal(updateData.addInTA);
        const newBalance = currentBalance.add(addInTA);

        if (newBalance.isNegative()) {
          this.logger.warn(
            `Balance cannot be negative: ${newBalance.toFixed(18)}`,
          );
          throw new Error('Insufficient balance');
        }

        appRelayer.balance = newBalance.toFixed(18);
        this.logger.log(`Updated Balance: ${appRelayer.balance}`);
      }

      // Save the updated relayer
      const updatedRelayer = await this.appRelayerRepository.save(appRelayer);

      await queryRunner.commitTransaction();
      return updatedRelayer;
    } catch (error) {
      await queryRunner.rollbackTransaction();
      this.logger.error(
        `Error updating AppRelayer: ${error.message}`,
        error.stack,
      );
      return null;
    } finally {
      await queryRunner.release();
    }
  }

  async getSingleRelayer(userId: number, query: FetchSingleRelayerDto) {
    try {
      const relayer = await this.dataSource
        .getRepository(AppRelayer)
        .createQueryBuilder('relayer')
        .innerJoinAndSelect('relayer.app', 'app', 'app.id = :id', {
          id: query.appId,
        })
        .innerJoin('app.user', 'user', 'user.id = :userId', {
          userId: userId,
        })
        .getOne();
      if (!relayer) {
        return {
          error: true,
          statusCode: HttpStatus.BAD_REQUEST,
          message: CommonMessage.NoDataFound,
        };
      }
      return {
        error: false,
        statusCode: HttpStatus.ACCEPTED,
        message: AppMessage.ServiceFetched('Relayer'),
        result: relayer,
      };
    } catch (error) {
      return {
        error: true,
        statusCode: HttpStatus.INTERNAL_SERVER_ERROR,
        message: CommonMessage.InternalError,
      };
    }
  }

  async getAllRelayers(userId: number, query: FetchRelayersDto) {
    try {
      let whereClause: any = {};

      if (query.s) {
        whereClause = {
          ...whereClause,
          relayerName: ILike(`%${query.s}%`),
        };
      }
      const skip = paginate(query.page, query.limit);

      const relayer = await this.dataSource
        .getRepository(AppRelayer)
        .createQueryBuilder('relayer')
        .innerJoinAndSelect('relayer.app', 'app', 'app.userId = :id', {
          id: userId,
        })
        .where(whereClause)
        .skip(skip)
        .orderBy('relayer.createdAt', 'DESC')
        .take(query.limit)
        .getManyAndCount();

      return {
        error: false,
        statusCode: HttpStatus.ACCEPTED,
        message:
          relayer[1] > 0
            ? AppMessage.ServiceFetched('Relayers')
            : CommonMessage.NoDataFound,
        result: relayer[0],
        totalCount: relayer[1],
      };
    } catch (error) {
      return {
        error: true,
        statusCode: HttpStatus.INTERNAL_SERVER_ERROR,
        message: CommonMessage.InternalError,
      };
    }
  }

  async getAllTransactions(userId: number, query: GetAppTransactionDto) {
    try {
      const transactionsType = ['deposit', 'withdraw'];
      let messageType = 'All';
      let whereClause: any = {
        app: { id: query.appId, user: { id: userId } },
        txType: In(transactionsType),
        status: Not('pending'),
        service: 'relayer',
      };
      if (query.s && query.s.trim()) {
        query.s = query.s.trim();
        whereClause = {
          ...whereClause,
          hash: ILike(`%${query.s}%`),
        };
      }

      if (query.type && transactionsType.includes(query.type)) {
        whereClause['txType'] = query.type;
        messageType = query.type.replace(/^./, (match) => match.toUpperCase());
      }

      // const skip = query.limit * (query.page - 1);
      const skip = paginate(query.page, query.limit);

      const [result, totalCount] = await this.dataSource
        .getRepository(Transaction)
        .findAndCount({
          where: whereClause,
          select: ['hash', 'txType', 'value', 'createdAt', 'id'],
          take: query.limit,
          skip: skip,
          order: { createdAt: 'DESC' },
        });

      return {
        error: false,
        statusCode: HttpStatus.ACCEPTED,
        message:
          totalCount > 0
            ? TransactionMessage.LIST_FOUND(messageType)
            : CommonMessage.NoDataFound,
        result: result,
        totalCount: totalCount,
      };
    } catch (error) {
      this.logger.error(
        'error in catch at getAllTransactions',
        JSON.stringify(error),
      );
      return {
        error: true,
        statusCode: HttpStatus.INTERNAL_SERVER_ERROR,
        message: CommonMessage.InternalError,
      };
    }
  }

  async getAllRelayerTransactions(
    userId: number,
    query: GetAppRelayerTransactionDto,
  ) {
    try {
      const appRelayer = await this.dataSource
        .getRepository(AppRelayer)
        .findOne({
          where: {
            id: query.appRelayerId,
          },
          relations: ['app', 'app.user'],
        });

      if (!appRelayer || appRelayer.app?.user?.id !== userId) {
        return {
          error: true,
          statusCode: HttpStatus.FORBIDDEN,
          message: 'You are not authorized to access these transactions.',
        };
      }

      const whereClause: any = {
        appRelayerId: query.appRelayerId,
        service: 'relayer',
      };

      if (query.s && query.s.trim()) {
        whereClause.hash = ILike(`%${query.s.trim()}%`);
      }

      const skip = paginate(query.page, query.limit);

      const [result, totalCount] = await this.dataSource
        .getRepository(RelayersTransaction)
        .findAndCount({
          where: whereClause,
          select: ['id', 'hash', 'status', 'receipt', 'createdAt', 'reason'],
          take: query.limit,
          skip,
          order: { createdAt: 'DESC' },
        });

      return {
        error: false,
        statusCode: HttpStatus.ACCEPTED,
        message:
          totalCount > 0
            ? TransactionMessage.LIST_FOUND('Relayer')
            : CommonMessage.NoDataFound,
        result,
        totalCount,
      };
    } catch (error) {
      this.logger.error(
        'error in catch at getAllRelayerTransactions',
        JSON.stringify(error),
      );
      return {
        error: true,
        statusCode: HttpStatus.INTERNAL_SERVER_ERROR,
        message: CommonMessage.InternalError,
      };
    }
  }

  async getContractAbi(userId: number, query: GetContractAbiDto) {
    try {
      const validateContractAddress = isValidContractAddress(
        query.contractAddress,
      );
      if (!validateContractAddress) {
        return {
          error: true,
          statusCode: HttpStatus.BAD_REQUEST,
          message: CommonMessage.InvalidContractAddress,
        };
      }

      const relayer = await this.dataSource
        .getRepository(AppRelayer)
        .createQueryBuilder('relayer')
        .innerJoinAndSelect('relayer.app', 'app', 'app.id = :id', {
          id: query.appId,
        })
        .innerJoin('app.user', 'user', 'user.id = :userId', {
          userId: userId,
        })
        .getOne();
      if (!relayer) {
        return {
          error: true,
          statusCode: HttpStatus.NOT_FOUND,
          message: AppMessage.AppNotFound('relayer'),
        };
      }

      const chainId = relayer.app.chainId;

      if (!ValidForABi.includes(chainId)) {
        return {
          error: true,
          statusCode: HttpStatus.BAD_REQUEST,
          message: AppMessage.ContractNotSupported,
        };
      }

      const abi: any = await this.abiService.getAbi(
        chainId,
        query.contractAddress,
      );
      return abi;
    } catch (error) {
      return {
        error: true,
        statusCode: HttpStatus.INTERNAL_SERVER_ERROR,
        message: CommonMessage.InternalError,
      };
    }
  }

  async validator(
    options: FindOneOptions<ObjectLiteral>,
    entity: EntityTarget<ObjectLiteral>,
    notFoundMessage: string,
  ) {
    try {
      const isValidEntity = await this.dataSource
        .getRepository(entity)
        .findOne(options);
      if (!isValidEntity) {
        return {
          error: true,
          statusCode: HttpStatus.BAD_REQUEST,
          message: notFoundMessage,
          result: null,
        };
      }

      return {
        error: false,
        statusCode: HttpStatus.OK,
        message: AppMessage.AppFetched,
        result: isValidEntity,
      };
    } catch (error) {
      this.logger.error('Error in catch at validator', JSON.stringify(error));
      return {
        error: true,
        statusCode: HttpStatus.INTERNAL_SERVER_ERROR,
        message: CommonMessage.InternalError,
        result: null,
      };
    }
  }

  async smartContractValidator(options: FindOneOptions<SmartContract>) {
    return await this.validator(
      options,
      SmartContract,
      AppMessage.AppNotFound(),
    );
  }

  async validatorUserAppByQuery(options: FindOneOptions<Application>): Promise<{
    error: boolean;
    statusCode: HttpStatus;
    message: string;
    result: ObjectLiteral | Application;
  }> {
    return await this.validator(
      options,
      Application,
      AppMessage.ContractNotFound,
    );
  }

  async createSmartContract(
    userId: number,
    createSmartContractDto: CreateSmartContractDto,
  ) {
    // let functionsWhitelisted = createSmartContractDto.methods;
    let functionsWhitelisted = [];
    try {
      if (
        !createSmartContractDto.abi ||
        !JSON.parse(createSmartContractDto.abi)
      ) {
        return {
          error: true,
          statusCode: HttpStatus.BAD_REQUEST,
          message: CommonMessage.InvalidABI,
        };
      }

      const validateContractAddress = isValidContractAddress(
        createSmartContractDto.address,
      );
      if (!validateContractAddress) {
        return {
          error: true,
          statusCode: HttpStatus.BAD_REQUEST,
          message: CommonMessage.InvalidContractAddress,
        };
      }
      const parsedAbi: any[] = JSON.parse(createSmartContractDto.abi);
      const validAbi = this.abiService.validateAbiFunctions(
        parsedAbi,
        createSmartContractDto.methods,
      );
      if (!validAbi) {
        return {
          error: true,
          statusCode: HttpStatus.BAD_REQUEST,
          message: CommonMessage.InvalidABIMethods,
        };
      }
      if (
        createSmartContractDto.whitelistedMethods &&
        createSmartContractDto.whitelistedMethods.length
      ) {
        const validAbi = this.abiService.validateAbiFunctions(
          parsedAbi,
          createSmartContractDto.whitelistedMethods,
        );
        if (!validAbi) {
          return {
            error: true,
            statusCode: HttpStatus.BAD_REQUEST,
            message: CommonMessage.InvalidABIMethods,
          };
        }
        functionsWhitelisted = createSmartContractDto.whitelistedMethods;
      }
      const isAppValid = await this.validatorUserAppByQuery({
        where: {
          id: createSmartContractDto.appId,
          user: { id: userId },
          relayer: {
            app: {
              id: createSmartContractDto.appId,
            },
          },
        },
        relations: {
          relayer: true,
          smartContracts: true,
        },
      });
      if (!isAppValid || isAppValid.error) {
        return {
          error: true,
          statusCode: HttpStatus.BAD_REQUEST,
          message: AppMessage.AppNotFound(),
        };
      }
      let contractExistsBy = null;
      const contractExists = isAppValid.result.smartContracts.find(
        ({ name, address }) => {
          if (
            name.toLowerCase() === createSmartContractDto.name.toLowerCase()
          ) {
            contractExistsBy = 'name';
            return true;
          } else if (
            address.toLowerCase() ===
            createSmartContractDto.address.toLowerCase()
          ) {
            contractExistsBy = 'address';
            return true;
          }
          return false;
        },
      );
      if (contractExists && contractExistsBy) {
        return {
          error: true,
          statusCode: HttpStatus.BAD_REQUEST,
          message: AppMessage.ContarctAlreadyCreated(contractExistsBy),
        };
      }

      const newSmartContract = new SmartContract();
      newSmartContract.abi = parsedAbi as any;
      newSmartContract.address = createSmartContractDto.address;
      newSmartContract.name = createSmartContractDto.name;
      newSmartContract.app = isAppValid.result as Application;
      newSmartContract.methods = createSmartContractDto.methods as any;
      newSmartContract.whitelistedMethods = functionsWhitelisted as any;
      newSmartContract.type = 'relayer';
      const savedData = await this.dataSource.manager.save(newSmartContract);
      if (!savedData) {
        return {
          error: true,
          statusCode: HttpStatus.OK,
          message: UserMessage.SaveError,
        };
      }
      return {
        error: false,
        statusCode: HttpStatus.OK,
        message: AppMessage.ContractCreated,
      };
    } catch (error) {
      this.logger.error(
        'Error in catch at createSmartContract',
        JSON.stringify(error),
      );
      return {
        error: true,
        statusCode: HttpStatus.INTERNAL_SERVER_ERROR,
        message: CommonMessage.InternalError,
      };
    }
  }

  async updateSmartContract(
    userId: number,
    updateSmartContractDto: UpdateSmartContractDto,
  ) {
    try {
      const isContractValid = await this.validatorUserAppByQuery({
        where: {
          id: updateSmartContractDto.id,
          user: { id: userId },
        },
        relations: {
          smartContracts: true,
        },
      });
      if (!isContractValid || isContractValid.error) {
        return {
          error: true,
          statusCode: HttpStatus.BAD_REQUEST,
          message: AppMessage.AppNotFound(),
        };
      }
      const contractExists = isContractValid.result.smartContracts.find(
        ({ id }) => {
          if (id === updateSmartContractDto.contractId) {
            return true;
          }
          return false;
        },
      );
      if (!contractExists) {
        return {
          error: true,
          statusCode: HttpStatus.BAD_REQUEST,
          message: AppMessage.ContractNotFound,
        };
      }
      const smartContractObj = contractExists as SmartContract;
      if (updateSmartContractDto.name) {
        if (updateSmartContractDto.name === contractExists.name) {
          return {
            error: true,
            statusCode: HttpStatus.BAD_REQUEST,
            message: AppMessage.AppAlreadySetSame('Smart contract name'),
          };
        }
        const isNameValid = isContractValid.result.smartContracts.find(
          ({ name, id }) => {
            if (
              name.toLowerCase() ===
                updateSmartContractDto.name.toLowerCase() &&
              id !== updateSmartContractDto.contractId
            ) {
              return true;
            }
            return false;
          },
        );
        if (isNameValid) {
          return {
            error: true,
            statusCode: HttpStatus.BAD_REQUEST,
            message: AppMessage.ContarctAlreadyCreated(),
          };
        }
        smartContractObj['name'] = updateSmartContractDto.name;
      }

      if (updateSmartContractDto.whitelistedMethods) {
        if (
          compareArrays(
            updateSmartContractDto.whitelistedMethods,
            contractExists.whitelistedMethods,
          )
        ) {
          return {
            error: true,
            statusCode: HttpStatus.BAD_REQUEST,
            message: AppMessage.AppAlreadySetSame(
              'Smart contract whitelistedMethods',
            ),
          };
        }
        smartContractObj['whitelistedMethods'] = contractExists.methods;
        if (updateSmartContractDto.whitelistedMethods.length) {
          const validAbi = this.abiService.validateAbiFunctions(
            contractExists.abi,
            updateSmartContractDto.whitelistedMethods,
          );
          if (!validAbi) {
            return {
              error: true,
              statusCode: HttpStatus.BAD_REQUEST,
              message: CommonMessage.InvalidABIMethods,
            };
          }
          smartContractObj['whitelistedMethods'] =
            updateSmartContractDto.whitelistedMethods as any;
        }
      }

      if (updateSmartContractDto.isActive !== undefined) {
        if (updateSmartContractDto.isActive === contractExists.isActive) {
          return {
            error: true,
            statusCode: HttpStatus.BAD_REQUEST,
            message: AppMessage.AppAlreadySetSame('isActive'),
          };
        }
        smartContractObj.isActive = updateSmartContractDto.isActive;
      }
      const savedData = await this.dataSource.manager.save(smartContractObj);
      if (!savedData) {
        return {
          error: true,
          statusCode: HttpStatus.OK,
          message: UserMessage.SaveError,
        };
      }
      return {
        error: false,
        statusCode: HttpStatus.OK,
        message: AppMessage.ContractUpdated,
      };
    } catch (error) {
      this.logger.error(
        'Error in catch at updateSmartContract',
        JSON.stringify(error),
      );
      return {
        error: true,
        statusCode: HttpStatus.INTERNAL_SERVER_ERROR,
        message: CommonMessage.InternalError,
      };
    }
  }

  async removeSmartContract(
    userId: number,
    removeSmartContractDto: RemoveSmartContractDto,
  ) {
    try {
      const isContractValid = await this.validatorUserAppByQuery({
        where: {
          id: removeSmartContractDto.id,
          user: { id: userId },
        },
        relations: {
          smartContracts: true,
        },
      });
      if (!isContractValid || isContractValid.error) {
        return {
          error: true,
          statusCode: HttpStatus.BAD_REQUEST,
          message: AppMessage.AppNotFound(),
        };
      }
      const contractExists = isContractValid.result.smartContracts.find(
        ({ id }) => {
          if (id === removeSmartContractDto.contractId) {
            return true;
          }
          return false;
        },
      );
      if (!contractExists) {
        return {
          error: true,
          statusCode: HttpStatus.BAD_REQUEST,
          message: AppMessage.ContractNotFound,
        };
      }
      const smartContractObj = contractExists as SmartContract;
      // eslint-disable-next-line @typescript-eslint/no-unused-vars
      const removedData = await this.dataSource
        .getRepository(SmartContract)
        .delete(smartContractObj.id);

      return {
        error: false,
        statusCode: HttpStatus.OK,
        message: AppMessage.ContractRemoved,
      };
    } catch (error) {
      this.logger.error(
        'Error in catch at createSmartContract',
        JSON.stringify(error),
      );
      return {
        error: true,
        statusCode: HttpStatus.INTERNAL_SERVER_ERROR,
        message: CommonMessage.InternalError,
      };
    }
  }

  async getAllSmartContract(
    userId: number,
    getSmartContractDto: ListSmartContractsDto,
  ) {
    try {
      const skip = paginate(
        getSmartContractDto.page,
        getSmartContractDto.limit,
      );
      const [result, totalCount] = await this.dataSource
        .getRepository(SmartContract)
        .findAndCount({
          where: {
            app: {
              id: getSmartContractDto.appId,
              user: { id: userId },
            },
            type: 'relayer',
          },
          take: getSmartContractDto.limit,
          skip: skip,
          order: { createdAt: 'DESC' },
        });

      return {
        error: false,
        statusCode: HttpStatus.ACCEPTED,
        message:
          totalCount > 0
            ? AppMessage.ContractsFetched
            : CommonMessage.NoDataFound,
        result: result,
        totalCount: totalCount,
      };
    } catch (error) {
      this.logger.error(
        'Error in catch at getAllSmartContract',
        JSON.stringify(error),
      );
      return {
        error: true,
        statusCode: HttpStatus.INTERNAL_SERVER_ERROR,
        message: CommonMessage.InternalError,
      };
    }
  }

  async deleteApplicationService(userId: number, query: RemoveRelayerDto) {
    const queryRunner = this.dataSource.createQueryRunner();
    try {
      const app = await this.originsService.getApplicationConfigByType(
        userId,
        query,
        true,
        true,
        true,
      );

      if (!app || app.error || !app.data) {
        return app;
      }
      let response: AppRelayer | Paymaster | Bundler;
      let select: string;
      switch (query.type) {
        case OriginType.BUNDLER:
          response = app.data as Bundler;
          select = 'Bundler';
          break;
        case OriginType.PAYMASTER:
          response = app.data as Paymaster;
          select = 'Paymaster';
          break;
        case OriginType.RELAYER:
          response = app.data as AppRelayer;
          select = 'Relayer';
          break;
        case OriginType.CHATAI:
          // ChatAI deletion should be handled by ChatAI service
          return {
            error: true,
            statusCode: HttpStatus.BAD_REQUEST,
            message: 'ChatAI deletion not supported in relayer service',
          };
        default:
          break;
      }
      // establish real database connection using our new query runner
      await queryRunner.connect();
      // lets now open a new transaction:
      await queryRunner.startTransaction();

      const removeService = await queryRunner.manager.remove(response);
      if (!removeService) {
        await queryRunner.rollbackTransaction();
        this.logger.verbose(
          'rolled back the query runner for register when failed to remove Service',
        );
        return {
          error: true,
          statusCode: HttpStatus.INTERNAL_SERVER_ERROR,
          message: AppMessage.RemoveError(select),
        };
      }

      if (response.app.transactions && response.app.transactions.length) {
        const removeTransaction = await queryRunner.manager.remove(
          response.app.transactions,
        );
        if (!removeTransaction || !removeTransaction.length) {
          await queryRunner.rollbackTransaction();
          this.logger.verbose(
            `rolled back the query runner for register when failed to remove ${query.type} removeTransaction`,
          );
          return {
            error: true,
            statusCode: HttpStatus.INTERNAL_SERVER_ERROR,
            message: AppMessage.RemoveError(select),
          };
        }
      }

      if (response.app.smartContracts && response.app.smartContracts.length) {
        const removeSmartContracts = await queryRunner.manager.remove(
          response.app.smartContracts,
        );
        if (!removeSmartContracts || !removeSmartContracts.length) {
          await queryRunner.rollbackTransaction();
          this.logger.verbose(
            `rolled back the query runner for register when failed to remove ${query.type} smartContracts`,
          );
          return {
            error: true,
            statusCode: HttpStatus.INTERNAL_SERVER_ERROR,
            message: AppMessage.RemoveError(select),
          };
        }
      }

      if (response.app.origins && response.app.origins.length) {
        const removeOrigins = await queryRunner.manager.remove(
          response.app.origins,
        );
        if (!removeOrigins || !removeOrigins.length) {
          await queryRunner.rollbackTransaction();
          this.logger.verbose(
            `rolled back the query runner for register when failed to remove ${query.type} origins`,
          );
          return {
            error: true,
            statusCode: HttpStatus.INTERNAL_SERVER_ERROR,
            message: AppMessage.RemoveError(select),
          };
        }
      }
      // await this.applicationRepository.remove(response.id);
      await queryRunner.commitTransaction();
      this.logger.verbose(
        `commited transaction of the query runner when all ${query.type} related records are removed successfully`,
      );
      return {
        error: false,
        statusCode: HttpStatus.OK,
        message: AppMessage.FeatureRemoved(select),
      };
    } catch (error) {
      this.logger.error(
        'error in catch at deleteApplicationService',
        JSON.stringify(error),
      );
      await queryRunner.rollbackTransaction();
      this.logger.verbose(
        'rolled back the query runner for deleteApplicationService when error in catch',
      );
      return {
        error: true,
        statusCode: HttpStatus.INTERNAL_SERVER_ERROR,
        message: CommonMessage.InternalError,
      };
    } finally {
      this.logger.verbose('Releasing the query runner for register...');
      await queryRunner.release();
      this.logger.verbose('register query runner released!');
    }
  }
}
