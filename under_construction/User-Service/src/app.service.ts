import { Injectable } from '@nestjs/common';
import { CommonMessage } from './CommonMessages/CommonMessages';
import { uptime } from 'process';

@Injectable()
export class AppService {
  getHealth(): object {
    return {
      error: false,
      message: CommonMessage.Welcome,
      port: process.env.PORT ? process.env.PORT : 3000,
      timestamp: Date.now(),
      up_since_in_sec: uptime(),
    };
  }
  // async onModuleInit() {
  //   console.log('AppService initialized');
  //   await this.runTask();
  // }

  // async runTask() {
  //   const res = await decodeRecursive({
  //     calldata:
  //       '0x0c53c51c0000000000000000000000001671fc001505af8433b259a60dc2638ae6dabf0b00000000000000000000000000000000000000000000000000000000000000a02d318f3c58526afa6ad26e90df0523c6e3426ee739a274b77674417203632dea7bbe2cf09947314d4ed5d9643d62a10b71f01a27d77ff429bac7403e7256215e000000000000000000000000000000000000000000000000000000000000001b0000000000000000000000000000000000000000000000000000000000000044095ea7b3000000000000000000000000763d37ab388c5cdd2fb0849d6275802f959fbf30000000000000000000000000000000000000000000000000000000000000271000000000000000000000000000000000000000000000000000000000',
  //   });
  //   console.log('res', res);
  // }
}
