import { Injectable, Logger } from '@nestjs/common';
import { MailerService } from '@nestjs-modules/mailer';
import * as ejs from 'ejs';
import * as fs from 'fs';
import { DEFAULT_THRESHOLD } from 'src/utils/constants';
import Decimal from 'decimal.js';

interface IGetEmailBody {
  subject: string;
  text: string;
  html: string;
}

@Injectable()
export class MailService {
  private readonly logger = new Logger(MailService.name, { timestamp: true });
  constructor(private readonly mailerService: MailerService) {}

  async sendEmail(emailId: string, status: string, variables: any[]) {
    try {
      const getContents = await this.getHTMLBody(status, variables);
      const mailOptions = {
        from: `"${process.env.PROJECT_NAME}"<${process.env.SMTP_FROM}>`,
        to: emailId,
        subject: getContents.subject,
        text: getContents.text,
        html: getContents.html,
      };
      const mailHandler = await this.mailerService.sendMail(mailOptions);
      if (mailHandler) {
        return true;
      } else {
        return false;
      }
    } catch (error) {
      this.logger.error(
        'Error in catch at sendEmail of MailService',
        JSON.stringify(error),
      );
      return false;
    }
  }

  async getHTMLBody(
    queryType: string,
    variables: any[],
  ): Promise<IGetEmailBody> {
    let returnVal;
    switch (queryType) {
      case 'verifyToken':
        returnVal = {
          subject: `Welcome to ${process.env.PROJECT_NAME}`,
          text: 'User Registration',
          html: await this.emailHtmlParser({
            content: `You're almost ready to get started. Please click on the button below to verify your email address
              and enjoy exclusive services with us!`,
            heading: `WELCOME TO`,
            optional_project_name: `ABSTRAXN`,
            link: variables[0],
            link_label: 'Verify Your Email',
          }),
        };
        break;

      case 'resetPassToken':
        returnVal = {
          subject: `Forgot Password - ${process.env.PROJECT_NAME}`,
          text: 'Forgot Password',
          html: await this.emailHtmlParser({
            content: `You're almost ready to reset your password. Please click on the button below to verify your email address and reset your password.`,
            heading: `Forgot Your Password?`,
            optional_project_name: ``,
            link: variables[0],
            link_label: 'Reset Password',
          }),
        };
        break;

      case 'userBlock':
        returnVal = {
          subject: `Temporary Blocked - ${process.env.PROJECT_NAME}`,
          text: 'Temporary Blocked',
          html: await this.emailHtmlParser({
            content: `You are temporarily blocked due to multiple wrong credential attempts until ${variables[0]}.`,
            heading: ``,
            optional_project_name: `Temporary Blocked`,
            link: '',
            link_label: '',
          }),
        };
        break;

      case 'lowGasTankBalance':
        // Extract unique application names
        const uniqueApps = [
          ...new Set(variables.map((relayer) => relayer.appName)),
        ].join(', ');

        const relayersList = variables
          .map((relayer) => {
            const balance = new Decimal(relayer.balance)
              .toDecimalPlaces(6)
              .toString(); // Removes trailing zeros

            const thresholdValue = new Decimal(relayer.threshold);
            const threshold = thresholdValue.lte(0)
              ? DEFAULT_THRESHOLD
              : thresholdValue.toDecimalPlaces(6).toString(); // Removes trailing zeros

            return `<p style="color: #fff;"><b>${relayer.relayerName}</b>: Balance <b>${balance}</b>, Recommended: <b>${threshold}</b></p>`;
          })
          .join('');

        returnVal = {
          subject: `⚠️ WARNING: Gas Tank Balance is low!`,
          text: 'Gas Tank Balance is low for your relayers.',
          html: await this.emailHtmlParser({
            content: `
                  <p style="color: #fff;">The following relayers have a low gas balance:</p>
                  ${relayersList}
                  <p style="color: #fff;">Please refill your gas tank to ensure smooth operations.</p>
                `,
            heading: `Gas Tank Balance Alert!`,
            optional_project_name: `Applications: ${uniqueApps}`,
            link: '',
            link_label: '',
          }),
        };
        break;

      default:
        returnVal = '';
        break;
    }
    return returnVal;
  }

  /**
   * Parse Email html
   * @param {*} content
   */
  async emailHtmlParser(emailContent: {
    heading: string;
    content: string;
    link: string | null;
    link_label: string | null;
    optional_project_name: string | null;
  }) {
    const str = fs.readFileSync(__dirname + '/templates/common.ejs', 'utf8');

    const messageHtml = ejs.render(str, {
      heading: emailContent.heading,
      optional_project_name: emailContent.optional_project_name,
      content: emailContent.content,
      link: emailContent.link,
      link_label: emailContent.link_label,
      current_year: new Date().getFullYear(),
      logo: 'https://www.abstraxn.com/wp-content/uploads/2024/06/new-logo.png',
    });
    return messageHtml;
  }
}
