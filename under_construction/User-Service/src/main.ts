import { ValidationPipe, Logger } from '@nestjs/common';
import { NestFactory } from '@nestjs/core';
import { MicroserviceOptions, Transport } from '@nestjs/microservices';
import { DocumentBuilder, SwaggerModule } from '@nestjs/swagger';
import { AppModule } from './app.module';
import helmet from 'helmet';
import { PodCoordinatorService } from './pod-coordinator/pod-coordinator.service';

const logger = new Logger('Bootstrap', { timestamp: true });

function createMicroserviceOptions(queueName: string): MicroserviceOptions {
  return {
    transport: Transport.RMQ,
    options: {
      urls: [process.env.RMQ_URL],
      queue: queueName,
      queueOptions: {
        durable: true,
        autoDelete: true,
      },
      noAck: false,
      prefetchCount: 1,
    },
  };
}

async function bootstrap() {
  const app = await NestFactory.create(AppModule);

  // Enable CORS with permissive settings for development/testing
  app.enableCors({
    origin: true, // Allow all origins
    methods: ['GET', 'POST', 'PUT', 'DELETE', 'PATCH', 'OPTIONS'],
    allowedHeaders: [
      'Content-Type',
      'Authorization',
      'Accept',
      'Origin',
      'X-Requested-With',
      'Access-Control-Allow-Origin',
      'Access-Control-Allow-Headers',
      'Access-Control-Allow-Methods',
      'Access-Control-Allow-Credentials',
      'Cache-Control',
      'Pragma',
      'Expires',
      'User-Agent',
      'Referer',
      'Host',
      'Connection',
      'Upgrade-Insecure-Requests',
      'Sec-Ch-Ua',
      'Sec-Ch-Ua-Mobile',
      'Sec-Ch-Ua-Platform',
      'Sec-Fetch-Site',
      'Sec-Fetch-Mode',
      'Sec-Fetch-Dest',
      'Accept-Encoding',
      'Accept-Language'
    ],
    credentials: true, // Allow credentials (cookies, authorization headers)
    preflightContinue: false,
    optionsSuccessStatus: 204,
  });

  // Configure helmet with permissive settings for development
  app.use(
    helmet({
      crossOriginOpenerPolicy: false, // Disable COOP to prevent the warning
      crossOriginEmbedderPolicy: false, // Disable COEP for better compatibility
      contentSecurityPolicy: false, // Disable CSP for Swagger UI compatibility
      crossOriginResourcePolicy: { policy: 'cross-origin' }, // Allow cross-origin requests
    }),
  );

  // Get the PodCoordinatorService
  const podCoordinatorService = app.get(PodCoordinatorService);

  // Setup microservices only when pod becomes leader
  podCoordinatorService.onLeadershipChange((isLeader: boolean) => {
    if (isLeader) {
      logger.log('Leader pod connecting to message queues...');
      const queueNames = ['paymaster_deposit_q', 'update_app_relayer_q'];
      queueNames.forEach(async (queueName) => {
        try {
          await app.connectMicroservice<MicroserviceOptions>(
            createMicroserviceOptions(queueName),
          );
        } catch (error) {
          logger.error(`Failed to connect to queue ${queueName}:`, error);
        }
      });
      app
        .startAllMicroservices()
        .then(() => logger.log('Leader pod connected to all message queues'))
        .catch((err) => logger.error('Failed to start microservices:', err));
    } else {
      logger.log('Non-leader pod - not connecting to message queues');
    }
  });

  app.useGlobalPipes(new ValidationPipe({ transform: true }));
  const config = new DocumentBuilder()
    .setTitle('Abstraxn User Service')
    .setDescription('The Abstraxn User API Documentations')
    .setVersion('1.0')
    .addBearerAuth()
    .build();

  const document = SwaggerModule.createDocument(app, config);
  SwaggerModule.setup('/users/api', app, document);
  const port = parseInt(process.env.PORT) || 3000;
  await app.listen(port, '0.0.0.0'); // Bind to all interfaces to allow LAN access
  logger.verbose(`Application is running on: http://0.0.0.0:${port}`);
}
bootstrap();
