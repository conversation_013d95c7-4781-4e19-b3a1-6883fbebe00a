// socket/socket.gateway.ts
import {
  WebSocketGateway,
  OnGatewayConnection,
  WebSocketServer,
} from '@nestjs/websockets';
import { Server, Socket } from 'socket.io';
import { Injectable, Logger, OnModuleDestroy } from '@nestjs/common';
import { SocketService } from './socket.service';

@WebSocketGateway({
  cors: {
    origin: '*',
  },
})
@Injectable()
export class SocketGateway implements OnGatewayConnection, OnModuleDestroy {
  @WebSocketServer() server: Server;
  private readonly logger = new Logger(SocketGateway.name, { timestamp: true });

  constructor(private readonly socketService: SocketService) {}

  handleConnection(socket: Socket) {
    this.socketService.handleConnection(socket, this.server);
  }

  onModuleDestroy() {
    this.server.close(() => {
      this.logger.debug('Socket.IO server disconnected');
    });
  }
}
