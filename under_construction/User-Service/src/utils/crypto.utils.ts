import { Logger } from '@nestjs/common';
import { AES, enc } from 'crypto-js';

export class CryptoUtils {
  private readonly logger = new Logger(CryptoUtils.name, { timestamp: true });
  password = '';
  constructor() {
    this.password = process.env.PASSWORD;
  }

  async encryptData(data) {
    try {
      const cipher = AES.encrypt(data, this.password);
      const encryptedText = cipher.toString();
      return {
        error: false,
        data: encryptedText,
      };
    } catch (error) {
      this.logger.error('error in catch at encryptData', JSON.stringify(error));

      return {
        error: true,
        data: null,
      };
    }
  }

  async decryptData(data) {
    try {
      const decipher = AES.decrypt(data, this.password);
      const decryptedText = decipher.toString(enc.Utf8);
      if (!decryptedText) {
        return {
          error: true,
          data: null,
        };
      }
      return {
        error: false,
        data: decryptedText,
      };
    } catch (error) {
      this.logger.error('error in catch at decryptData', JSON.stringify(error));
      return {
        error: true,
        data: null,
      };
    }
  }
}
