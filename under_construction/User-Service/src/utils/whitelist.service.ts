import { HttpStatus } from '@nestjs/common';
import {
  decodeWithCalldata,
  sigHashFromCalldata,
} from './callDataDecoder/decodeBySigHash';
import { Interface } from 'ethers';
import { decodeRecursive } from './decoderV2/decoder';
import { Application } from 'src/application/entities/application.entity';
import { DEFAULT_ABI } from './default.abi';

export async function whitelistValidator(callData: string) {
  try {
    const mappedResults: any = await decoder(callData);
    if (!mappedResults.length) {
      return {
        error: false,
        type: 'none',
      };
    }
    if (
      mappedResults[0].fnName === 'executeBatch' ||
      mappedResults[0].fnName === 'executeBatch_y6U'
    ) {
      return {
        error: false,
        result: mappedResults[0].decoded,
        type: 'batch',
      };
    } else if (
      mappedResults[0].fnName === 'execute' ||
      mappedResults[0].fnName === 'execute_ncC'
    ) {
      return {
        error: false,
        result: mappedResults[0].decoded,
        type: 'single',
      };
    }
  } catch (error) {
    return {
      error: true,
      statusCode: HttpStatus.BAD_REQUEST,
      message: error.message || error.toString(),
      result: null,
    };
  }
}

// export async function whitelistValidatorV2(callData: string) {
//   try {
//     const res = await decodeRecursive({
//       calldata: callData,
//     });
//     if (!res) {
//       return {
//         error: false,
//         type: 'none',
//       };
//     }
//     if (
//       res?.functionName === 'executeBatch' ||
//       res?.functionName === 'executeBatch_y6U'
//     ) {
//       return {
//         error: false,
//         result: res?.rawArgs[2],
//         type: 'batch',
//       };
//     } else if (
//       res?.functionName === 'execute' ||
//       res?.functionName === 'execute_ncC'
//     ) {
//       return {
//         error: false,
//         result: res?.rawArgs[2],
//         type: 'single',
//       };
//     }
//   } catch (error) {
//     return {
//       error: true,
//       statusCode: HttpStatus.BAD_REQUEST,
//       message: error.message || error.toString(),
//       result: null,
//     };
//   }
// }
export async function whitelistValidatorV2(
  callData: string,
  appData: Application,
) {
  try {
    const res = await decodeRecursive({
      calldata: callData,
    });

    const { functionName, rawArgs } = res;

    const isBatch = functionName.startsWith('executeBatch');
    const contractAddresses = isBatch ? rawArgs[0] : [rawArgs[0]];
    const paramData = isBatch ? rawArgs[2] : [rawArgs[2]];

    const decodedCalls = [];

    for (let i = 0; i < contractAddresses.length; i++) {
      const contractAddress = contractAddresses[i];
      const encodedCallData = paramData[i];

      if (!appData.smartContracts || !appData.smartContracts.length) {
        return {
          error: false,
          statusCode: HttpStatus.OK,
        };
      }

      // Find matching contract
      const matchedContract = appData.smartContracts.find(
        (contract) =>
          contract.address.toLowerCase() === contractAddress.toLowerCase(),
      );

      if (!matchedContract) {
        return {
          error: true,
          statusCode: HttpStatus.BAD_REQUEST,
          message: `No matching contract found for address: ${contractAddress}`,
          result: null,
        };
      }

      if (!matchedContract.isActive) {
        continue;
      }

      const { abi } = matchedContract;
      const nestedDecodedResult = await decodeRecursive({
        calldata: encodedCallData,
        abi: abi,
      });

      decodedCalls.push({
        contractAddress,
        decodedData: nestedDecodedResult.functionName,
      });

      const nonWhitelistedMethods = decodedCalls
        .filter(
          (item) =>
            !matchedContract.whitelistedMethods.includes(item.decodedData),
        )
        .map((item) => item.decodedData);

      if (nonWhitelistedMethods.length > 0) {
        return {
          error: true,
          statusCode: HttpStatus.BAD_REQUEST,
          message: `Contract method '${nonWhitelistedMethods.join(', ')}' is not whitelisted.`,
          result: null,
        };
      }
    }
    return {
      error: false,
      statusCode: HttpStatus.OK,
    };
  } catch (error) {
    return {
      error: true,
      statusCode: HttpStatus.BAD_REQUEST,
      message: error.message || error.toString(),
      result: null,
    };
  }
}

export async function whitelistValidatorForRelayerV2(
  payload: any,
  appData: Application,
) {
  try {
    const res = await decodeRecursive({
      calldata: payload?.params?.data,
    });

    const { functionName, rawArgs } = res;

    const isMetaTx = functionName.startsWith('executeMetaTransaction');
    const contractAddresses = [payload?.params?.contractAddress];
    const paramData = isMetaTx ? [rawArgs[1]] : '';

    const decodedCalls = [];
    let contractAbi: any;

    for (let i = 0; i < contractAddresses.length; i++) {
      const contractAddress = contractAddresses[i];
      const encodedCallData = paramData[i];

      // Find matching contract
      const matchedContract = appData.smartContracts.find(
        (contract) =>
          contract.address.toLowerCase() === contractAddress.toLowerCase(),
      );

      if (!matchedContract) {
        return {
          error: true,
          statusCode: HttpStatus.BAD_REQUEST,
          message: `No matching contract found for address: ${contractAddress} in policy`,
          result: null,
        };
      }

      if (!matchedContract.isActive) {
        return {
          error: true,
          statusCode: HttpStatus.BAD_REQUEST,
          message: `Smart contract is not active`,
          result: null,
        };
      }

      if (!matchedContract) {
        return {
          error: true,
          statusCode: HttpStatus.BAD_REQUEST,
          message: `No matching contract found for address: ${contractAddress}`,
          result: null,
        };
      }

      if (!matchedContract.isActive) {
        continue;
      }

      const { abi } = matchedContract;
      contractAbi = abi;
      const nestedDecodedResult = await decodeRecursive({
        calldata: encodedCallData,
        abi: abi,
      });

      decodedCalls.push({
        contractAddress,
        decodedData: nestedDecodedResult.functionName,
      });

      const nonWhitelistedMethods = decodedCalls
        .filter(
          (item) =>
            !matchedContract.whitelistedMethods.includes(item.decodedData),
        )
        .map((item) => item.decodedData);

      if (nonWhitelistedMethods.length > 0) {
        return {
          error: true,
          statusCode: HttpStatus.BAD_REQUEST,
          message: `Contract method '${nonWhitelistedMethods.join(', ')}' is not whitelisted.`,
          result: null,
        };
      }
    }
    return {
      error: false,
      statusCode: HttpStatus.OK,
      data: rawArgs,
      abi: contractAbi,
    };
  } catch (error) {
    return {
      error: true,
      statusCode: HttpStatus.BAD_REQUEST,
      message: error.message || error.toString(),
      result: null,
    };
  }
}

export async function whitelistValidatorForRelayerV3(payload: any) {
  try {
    const res = await decodeRecursive({
      calldata: payload?.params?.data,
    });

    const { functionName, rawArgs } = res;

    const isMetaTx = functionName.startsWith('executeMetaTransaction');

    if (!isMetaTx) {
      return {
        error: true,
        statusCode: HttpStatus.BAD_REQUEST,
        message: `Only the executeMetaTransaction function can be called.`,
        result: null,
      };
    }
    return {
      error: false,
      statusCode: HttpStatus.OK,
      data: rawArgs,
      abi: DEFAULT_ABI,
    };
  } catch (error) {
    return {
      error: true,
      statusCode: HttpStatus.BAD_REQUEST,
      message: error.message || error.toString(),
      result: null,
    };
  }
}

export async function extractFunctionNames(
  decodedArray: any[],
  abi?: Interface,
) {
  const functionNames: { [key: string]: string[] } = {};
  for (let i = 0; i < decodedArray[0].length; i++) {
    const key = decodedArray[0][i];
    const decoded = decodedArray[2][i];
    const mappedResults = await decoder(decoded, abi);

    if (!functionNames[key]) {
      functionNames[key] = [];
    }
    functionNames[key].push(mappedResults[0].fnName);
  }
  return functionNames;
}

export async function extractSingleFunctionName(
  decoded: any,
  abi?: Interface | Interface[],
) {
  const mappedResult = await decoder(decoded[2], abi);
  return { [`${decoded[0]}`]: [mappedResult[0]?.fnName] };
}

async function decoder(callData: string, abi?: Interface | Interface[]) {
  const signatureHash = sigHashFromCalldata(callData);
  const decodeResults = await decodeWithCalldata(signatureHash, callData, abi);

  return decodeResults.map((decoded: any) => ({
    fnName: decoded.fragment?.name,
    fnType: decoded.fragment?.type,
    decoded: decoded?.decoded,
    inputs: decoded.fragment?.inputs,
  }));
}
