interface LlamaParseResponse {
  id: string;
  status: string;
  result?: {
    text: string;
    metadata: {
      page_count: number;
      word_count: number;
    };
  };
  error?: string;
}

export class LlamaParseService {
  private apiKey: string;
  private baseUrl = "https://api.cloud.llamaindex.ai/api/v1";
  private isConfigured: boolean;
  
  constructor() {
    this.apiKey = process.env.LLAMA_CLOUD_API_KEY || process.env.LLAMACLOUD_API_KEY || "";
    this.isConfigured = !!this.apiKey;
    
    if (!this.apiKey) {
      console.warn("WARNING: LLAMA_CLOUD_API_KEY not configured. Document parsing will be disabled.");
      console.warn("For full functionality, please set LLAMA_CLOUD_API_KEY in Railway environment variables.");
    } else {
      console.log("LlamaParseService initialized successfully");
    }
  }

  private checkConfiguration(): void {
    if (!this.isConfigured) {
      throw new Error("Document parsing service is currently unavailable. Please try again later.");
    }
  }

  /**
   * Sanitizes third-party API errors to prevent exposing sensitive information
   */
  private sanitizeError(error: any, context: string): Error {
    // Log the actual error for debugging
    console.error(`LlamaParse ${context} error:`, error);
    
    // Return a generic user-friendly message
    if (error?.message?.includes('timeout') || error?.name === 'AbortError') {
      return new Error("Document parsing is taking longer than expected. Please try again.");
    }
    
    if (error?.message?.includes('401') || error?.message?.includes('403')) {
      return new Error("Document parsing service is temporarily unavailable. Please try again later.");
    }
    
    if (error?.message?.includes('429')) {
      return new Error("Document parsing service is busy. Please try again in a few moments.");
    }
    
    // Generic fallback message
    return new Error(`Document parsing failed. Please check your file format and try again.`);
  }

  async parseFile(fileBuffer: Buffer, filename: string): Promise<LlamaParseResponse> {
    this.checkConfiguration();
    
    try {
      const formData = new FormData();
      // Convert Buffer to Uint8Array which is compatible with BlobPart
      const uint8Array = new Uint8Array(fileBuffer);
      const blob = new Blob([uint8Array], { type: this.getContentType(filename) });
      formData.append("file", blob, filename);

      // Upload file and get job ID
      const uploadResponse = await fetch(`${this.baseUrl}/parsing/upload`, {
        method: "POST",
        headers: {
          "Authorization": `Bearer ${this.apiKey}`,
        },
        body: formData,
      });

      if (!uploadResponse.ok) {
        const errorText = await uploadResponse.text();
        console.error(`LlamaParse upload error: ${uploadResponse.status} - ${errorText}`);
        throw this.sanitizeError({ status: uploadResponse.status, message: errorText }, "upload");
      }

      const uploadResult = await uploadResponse.json();
      const jobId = uploadResult.id;

      if (!jobId) {
        throw new Error("Document parsing failed to start. Please try again.");
      }

      // Poll for completion
      let attempts = 0;
      const maxAttempts = 60;
      
      while (attempts < maxAttempts) {
        try {
          const statusResponse = await this.getParseStatus(jobId);
          console.log(`LlamaParse job ${jobId} status check ${attempts + 1}:`, JSON.stringify(statusResponse, null, 2));
          
          if (statusResponse.status === "SUCCESS") {
            // Get the markdown result
            const result = await this.getParseResult(jobId);
            console.log(`LlamaParse job ${jobId} result:`, JSON.stringify(result, null, 2));
            
            // Handle different response formats
            let parsedText = "";
            let pageCount = 1;
            
            if (typeof result === "string") {
              // Sometimes the result is just the markdown string
              parsedText = result;
            } else if (result.markdown) {
              parsedText = result.markdown;
              pageCount = result.metadata?.page_count || result.pages?.length || 1;
            } else if (result.text) {
              parsedText = result.text;
              pageCount = result.metadata?.page_count || result.pages?.length || 1;
            } else if (result.pages) {
              // Handle pages array format
              parsedText = result.pages.map((page: any) => page.md || page.text || "").join("\n\n");
              pageCount = result.pages.length;
            }
            
            if (!parsedText) {
              console.error("No parsable text found in result:", result);
              throw new Error("Document parsing completed but no readable content was extracted. Please check your file format.");
            }
            
            return {
              id: jobId,
              status: "SUCCESS", 
              result: {
                text: parsedText,
                metadata: {
                  page_count: pageCount,
                  word_count: this.countWords(parsedText)
                }
              }
            };
          } else if (statusResponse.status === "ERROR" || statusResponse.status === "FAILED") {
            const errorMessage = statusResponse.error || statusResponse.message || "Parsing failed";
            console.error(`LlamaParse job ${jobId} failed:`, errorMessage);
            throw new Error("Document parsing failed. Please check your file format and try again.");
          }
          
          // Wait 5 seconds before checking again
          await new Promise(resolve => setTimeout(resolve, 5000));
          attempts++;
        } catch (error) {
          if (attempts >= maxAttempts - 1) {
            throw this.sanitizeError(error, "status check");
          }
          // Continue polling on temporary errors
          attempts++;
          await new Promise(resolve => setTimeout(resolve, 5000));
        }
      }
      
      throw new Error("Document parsing is taking longer than expected. Please try uploading a smaller file or try again later.");
    } catch (error) {
      if (error instanceof Error && error.message.includes("Document parsing")) {
        // Already sanitized
        throw error;
      }
      throw this.sanitizeError(error, "general");
    }
  }

  async getParseStatus(jobId: string): Promise<any> {
    this.checkConfiguration();
    
    try {
      const response = await fetch(`${this.baseUrl}/parsing/job/${jobId}`, {
        headers: {
          "Authorization": `Bearer ${this.apiKey}`,
        },
      });

      if (!response.ok) {
        const errorText = await response.text();
        console.error(`LlamaParse status error: ${response.status} - ${errorText}`);
        throw this.sanitizeError({ status: response.status, message: errorText }, "status");
      }

      return await response.json();
    } catch (error) {
      throw this.sanitizeError(error, "status");
    }
  }

  async getParseResult(jobId: string): Promise<any> {
    this.checkConfiguration();
    
    try {
      const response = await fetch(`${this.baseUrl}/parsing/job/${jobId}/result/markdown`, {
        headers: {
          "Authorization": `Bearer ${this.apiKey}`,
        },
      });

      if (!response.ok) {
        const errorText = await response.text();
        console.error(`LlamaParse result error: ${response.status} - ${errorText}`);
        throw this.sanitizeError({ status: response.status, message: errorText }, "result");
      }

      return await response.json();
    } catch (error) {
      throw this.sanitizeError(error, "result");
    }
  }

  private countWords(text: string): number {
    return text.split(/\s+/).filter(word => word.length > 0).length;
  }

  private getContentType(filename: string): string {
    const ext = filename.toLowerCase().split('.').pop();
    const contentTypes: { [key: string]: string } = {
      // PDF
      'pdf': 'application/pdf',
      // Microsoft Office
      'doc': 'application/msword',
      'docx': 'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
      'ppt': 'application/vnd.ms-powerpoint',
      'pptx': 'application/vnd.openxmlformats-officedocument.presentationml.presentation',
      'xls': 'application/vnd.ms-excel',
      'xlsx': 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
      // Text files
      'txt': 'text/plain',
      'csv': 'text/csv',
      'html': 'text/html',
      'htm': 'text/html',
      'md': 'text/markdown',
      'rtf': 'application/rtf',
      'xml': 'application/xml',
      // Images
      'jpg': 'image/jpeg',
      'jpeg': 'image/jpeg',
      'png': 'image/png',
      'gif': 'image/gif',
      'bmp': 'image/bmp',
      'tiff': 'image/tiff',
      'tif': 'image/tiff',
      'webp': 'image/webp',
      // Other formats
      // 'epub': 'application/epub+zip' - No longer supported (compressed format)
    };
    
    return contentTypes[ext || ''] || 'application/octet-stream';
  }
}

export const llamaParseService = new LlamaParseService(); 