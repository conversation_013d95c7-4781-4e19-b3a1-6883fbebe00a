import {
  IsString,
  IsO<PERSON>al,
  IsBoolean,
  IsNumber,
  IsUUID,
  IsEmail,
  IsIn,
} from 'class-validator';
import { Transform, Type } from 'class-transformer';
import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';

// ==================== Main ChatAI DTOs ====================

export class CreateChatAiDto {
  @ApiProperty({ description: 'Name of the ChatAI project' })
  @IsString()
  name: string;

  @ApiPropertyOptional({ description: 'Description of the ChatAI project' })
  @IsOptional()
  @IsString()
  description?: string;

  @ApiProperty({ description: 'Application ID to associate with ChatAI' })
  @IsUUID()
  appId: string;
}

export class UpdateChatAiDto {
  @ApiProperty({ description: 'Application ID' })
  @IsUUID()
  appId: string;

  @ApiPropertyOptional({ description: 'Updated name of the ChatAI project' })
  @IsOptional()
  @IsString()
  name?: string;

  @ApiPropertyOptional({
    description: 'Updated description of the ChatAI project',
  })
  @IsOptional()
  @IsString()
  description?: string;
}

export class UpdateChatAiSettingDto {
  @ApiProperty({ description: 'Application ID' })
  @IsUUID()
  appId: string;

  @ApiPropertyOptional({ description: 'Is the ChatAI active?' })
  @IsOptional()
  @IsBoolean()
  @Transform(({ value }) => value === 'true' || value === true)
  isActive?: boolean;

  @ApiPropertyOptional({ description: 'Enable/disable notifications' })
  @IsOptional()
  @IsBoolean()
  @Transform(({ value }) => value === 'true' || value === true)
  notificationsEnabled?: boolean;

  @ApiPropertyOptional({ description: 'Email for notifications' })
  @IsOptional()
  @IsEmail()
  notificationEmail?: string;

  @ApiPropertyOptional({ description: 'Credits amount' })
  @IsOptional()
  @IsNumber()
  credits?: number;

  @ApiPropertyOptional({ description: 'Subscription status' })
  @IsOptional()
  @IsIn(['free', 'pro', 'enterprise'])
  subscriptionStatus?: string;
}

export class FetchSingleChatAiDto {
  @ApiProperty({ description: 'Application ID' })
  @IsUUID()
  appId: string;
}

export class FetchChatAisDto {
  @ApiPropertyOptional({ description: 'Search term' })
  @IsOptional()
  @IsString()
  search?: string;

  @ApiPropertyOptional({ description: 'Page number', default: 1 })
  @IsOptional()
  page?: any;

  @ApiPropertyOptional({ description: 'Items per page', default: 10 })
  @IsOptional()
  limit?: any;
}

export class RemoveChatAiDto {
  @ApiProperty({ description: 'Application ID' })
  @IsUUID()
  appId: string;
}

// ==================== Document Management DTOs ====================

export class CreateDocumentDto {
  @ApiProperty({ description: 'Application ID' })
  @IsUUID()
  appId: string;

  @ApiPropertyOptional({ description: 'Document title' })
  @IsOptional()
  @IsString()
  title?: string;

  @ApiPropertyOptional({ description: 'Document description' })
  @IsOptional()
  @IsString()
  description?: string;
}

export class UpdateDocumentDto {
  @ApiProperty({ description: 'Document ID' })
  @IsNumber()
  documentId: number;

  @ApiPropertyOptional({ description: 'Updated document title' })
  @IsOptional()
  @IsString()
  title?: string;

  @ApiPropertyOptional({ description: 'Updated document description' })
  @IsOptional()
  @IsString()
  description?: string;

  @ApiPropertyOptional({ description: 'Document status' })
  @IsOptional()
  @IsIn(['uploading', 'parsing', 'indexing', 'ready', 'error'])
  status?: string;
}

// ==================== Chat Management DTOs ====================

export class CreateChatMessageDto {
  @ApiProperty({ description: 'Application ID' })
  @IsUUID()
  appId: string;

  @ApiProperty({ description: 'Message content' })
  @IsString()
  message: string;

  @ApiPropertyOptional({
    description: 'Document ID if message relates to specific document',
  })
  @IsOptional()
  @IsUUID()
  documentId?: string;
}

export class ChatQueryDto {
  @ApiProperty({ description: 'Application ID' })
  @IsUUID()
  appId: string;

  @ApiProperty({ description: 'Query message' })
  @IsString()
  query: string;

  @ApiPropertyOptional({ description: 'Document ID to query against' })
  @IsOptional()
  @IsUUID()
  documentId?: string;

  @ApiPropertyOptional({ description: 'Include chat history in context' })
  @IsOptional()
  @IsBoolean()
  includeHistory?: boolean;
}

// ==================== Transaction Management DTOs ====================

export class GetChatAiTransactionDto {
  @ApiProperty({ description: 'Application ID' })
  @IsUUID()
  appId: string;

  @ApiPropertyOptional({ description: 'Page number', default: 1 })
  @IsOptional()
  @Type(() => Number)
  @IsNumber()
  page?: number = 1;

  @ApiPropertyOptional({ description: 'Items per page', default: 10 })
  @IsOptional()
  @Type(() => Number)
  @IsNumber()
  limit?: number = 10;

  @ApiPropertyOptional({ description: 'Transaction type filter' })
  @IsOptional()
  @IsString()
  transactionType?: string;
}
