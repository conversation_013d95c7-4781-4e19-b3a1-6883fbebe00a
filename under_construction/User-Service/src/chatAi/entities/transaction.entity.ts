import {
  <PERSON><PERSON><PERSON>,
  PrimaryGeneratedC<PERSON>umn,
  Column,
  CreateDateColumn,
  UpdateDateColumn,
  ManyToOne,
  JoinColumn,
} from "typeorm";
import { ChatAi } from "./chatAi.entity";

@Entity("chat_ai_api_transactions")
export class ChatAiApiTransaction {
  @PrimaryGeneratedColumn("uuid")
  id: string;

  @Column({ nullable: false })
  apiProvider: string; // 'llama_cloud', 'openrouter'

  @Column({ nullable: false })
  endpoint: string; // API endpoint called

  @Column({ nullable: false })
  requestType: string; // 'document_parse', 'chat_completion', 'embedding'

  @Column({ type: "jsonb", nullable: true })
  requestData: any; // Request payload

  @Column({ type: "jsonb", nullable: true })
  responseData: any; // Response data

  @Column({ nullable: false })
  status: string; // 'success', 'error', 'pending'

  @Column({ nullable: true })
  tokensUsed: number;

  @Column({ type: "decimal", precision: 10, scale: 6, nullable: true })
  cost: number; // API cost in USD

  @Column({ type: "text", nullable: true })
  errorMessage: string;

  @CreateDateColumn({ type: "timestamp", default: () => "CURRENT_TIMESTAMP" })
  createdAt: Date;

  @UpdateDateColumn({
    type: "timestamp",
    default: () => "CURRENT_TIMESTAMP",
    onUpdate: "CURRENT_TIMESTAMP",
  })
  updatedAt: Date;

  // Relationships
  @ManyToOne(() => ChatAi, {
    onDelete: "CASCADE",
  })
  @JoinColumn({ name: "chatAiId" })
  chatAi: ChatAi;

  @Column({ nullable: false })
  chatAiId: string;
}
