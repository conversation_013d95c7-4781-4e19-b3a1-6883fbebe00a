import {
  Body,
  Controller,
  Delete,
  Get,
  Patch,
  Post,
  Query,
  Req,
  UseGuards,
  Logger,
} from '@nestjs/common';
import { ApiBearerAuth, ApiTags } from '@nestjs/swagger';
import { PaymasterService } from './paymaster.service';
import {
  AppTransactionDto,
  CreateSmartContractDto,
  FetchPaymastersDto,
  FetchSinglePaymasterDto,
  GetAppTransactionDto,
  GetContractAbiDto,
  SetupGasTankDto,
  UpdatePaymasterDto,
  UpdateSmartContractDto,
  RemoveSmartContractDto,
  ListSmartContractsDto,
  CreatePaymasterDto,
  RemovePaymasterDto,
} from './dto/paymaster.dto';
import { JwtAuthGuard } from '../auth/guards/jwt-auth.guard';
import { User } from '../user/entities/user.entity';
import { EventPattern, Payload, Ctx, RmqContext } from '@nestjs/microservices';
import { BasePodService } from '../common/base-pod.service';
import { PodCoordinatorService } from '../pod-coordinator/pod-coordinator.service';

@ApiTags('Paymaster')
@Controller('users/app/paymaster')
export class PaymasterController extends BasePodService {
  private readonly logger = new Logger(PaymasterController.name);

  constructor(
    private readonly paymasterService: PaymasterService,
    protected readonly podCoordinatorService: PodCoordinatorService,
  ) {
    super(podCoordinatorService);
  }

  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth()
  @Post('setup-paymaster')
  async setupPaymaster(
    @Req() req: { user: User },
    @Body() createPaymasterDto: CreatePaymasterDto,
  ) {
    const userId = req.user.id;
    return await this.paymasterService.setupPaymaster(
      userId,
      createPaymasterDto,
    );
  }

  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth()
  @Patch('update-paymaster')
  async updateApp(
    @Req() req: { user: User },
    @Body() updatePaymasterDto: UpdatePaymasterDto,
  ) {
    const userId = req.user.id;
    return await this.paymasterService.updatePaymaster(
      userId,
      updatePaymasterDto,
    );
  }

  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth()
  @Get('get-single-paymaster')
  async getSingleApp(
    @Req() req: { user: User },
    @Query() query: FetchSinglePaymasterDto,
  ) {
    const userId = req.user.id;
    return await this.paymasterService.getSinglePaymaster(userId, query);
  }

  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth()
  @Get('get-all-paymasters')
  async getAllApp(
    @Req() req: { user: User },
    @Query() query: FetchPaymastersDto,
  ) {
    const userId = req.user.id;
    return await this.paymasterService.getAllPaymasters(userId, query);
  }

  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth()
  @Patch('setup-gas-tank')
  async setupGasTank(
    @Req() req: { user: User },
    @Body() setupGasTankDto: SetupGasTankDto,
  ) {
    const userId = req.user.id;
    return await this.paymasterService.setupGasTank(userId, setupGasTankDto);
  }

  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth()
  @Post('app-transaction')
  async appTransaction(
    @Req() req: { user: User },
    @Body() updatePaymasterAppDto: AppTransactionDto,
  ) {
    const userId = req.user.id;
    return this.paymasterService.startAppTransaction(
      userId,
      updatePaymasterAppDto,
    );
  }

  @EventPattern('handle_paymaster_transaction')
  async appDepositHandler(
    @Payload()
    payload: {
      userId: number;
      query: AppTransactionDto;
    },
    @Ctx() context: RmqContext,
  ) {
    const channel = context.getChannelRef();
    const originalMsg = context.getMessage();

    try {
      this.logger.log('Processing paymaster transaction');
      await this.paymasterService.handleAppTransaction(
        payload.userId,
        payload.query,
      );
      channel.ack(originalMsg);
    } catch (error) {
      this.logger.error('Error processing paymaster transaction:', error);
      channel.nack(originalMsg, false, true);
    }
  }

  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth()
  @Get('get-transactions')
  async getTransactions(
    @Query() query: GetAppTransactionDto,
    @Req() req: { user: User },
  ) {
    const userId = req.user.id;
    return await this.paymasterService.getAllTransactions(userId, query);
  }

  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth()
  @Get('get-contract-abi')
  async getContractAbi(
    @Query() query: GetContractAbiDto,
    @Req() req: { user: User },
  ) {
    const userId = req.user.id;
    return await this.paymasterService.getContractAbi(userId, query);
  }

  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth()
  @Post('add-smart-contract')
  async addSmartContract(
    @Req() req: { user: User },
    @Body() createSmartContractDto: CreateSmartContractDto,
  ) {
    const userId = req.user.id;
    return await this.paymasterService.createSmartContract(
      userId,
      createSmartContractDto,
    );
  }

  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth()
  @Patch('update-smart-contract')
  async updateSmartContract(
    @Req() req: { user: User },
    @Body() updateSmartContractDto: UpdateSmartContractDto,
  ) {
    const userId = req.user.id;
    return await this.paymasterService.updateSmartContract(
      userId,
      updateSmartContractDto,
    );
  }

  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth()
  @Post('remove-smart-contract')
  async removeSmartContract(
    @Req() req: { user: User },
    @Body() removeSmartContractDto: RemoveSmartContractDto,
  ) {
    const userId = req.user.id;
    return await this.paymasterService.removeSmartContract(
      userId,
      removeSmartContractDto,
    );
  }

  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth()
  @Get('get-smart-contracts')
  async getAllSmartContract(
    @Req() req: { user: User },
    @Query() getSmartContractDto: ListSmartContractsDto,
  ) {
    const userId = req.user.id;
    return await this.paymasterService.getAllSmartContract(
      userId,
      getSmartContractDto,
    );
  }

  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth()
  @Delete('delete-paymaster')
  async deleteApiKey(
    @Req() req: { user: User },
    @Query() payload: RemovePaymasterDto,
  ) {
    const userId = req.user.id;
    return this.paymasterService.deleteApplicationService(userId, payload);
  }
}
