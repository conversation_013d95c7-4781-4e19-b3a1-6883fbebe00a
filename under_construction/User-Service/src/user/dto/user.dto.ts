import {
  IsE<PERSON>,
  <PERSON>Enum,
  IsNotEmpty,
  Length,
  Matches,
  MaxLength,
  MinLength,
} from 'class-validator';
import { ApiProperty } from '@nestjs/swagger';
import { Regexs } from '../../utils/constants';

export enum Plan {
  FREE = 'free',
}

export enum Service {
  PAYMASTER = 'paymaster',
  BUNDLER = 'bundler',
}
export class CreateUserDto {
  @ApiProperty({
    description: 'First name of user',
    minLength: 3,
    maxLength: 20,
  })
  @IsNotEmpty()
  @MinLength(3)
  @MaxLength(20)
  @Matches(Regexs.OnlyAlphabetsWithSpace, {
    message: 'First name must contain only alphabetic characters',
  })
  firstName: string;

  @ApiProperty({
    description: 'Last name of user',
    minLength: 3,
    maxLength: 20,
  })
  @IsNotEmpty()
  @MinLength(3)
  @MaxLength(20)
  @Matches(Regexs.OnlyAlphabetsWithSpace, {
    message: 'Last name must contain only alphabetic characters',
  })
  lastName: string;

  @ApiProperty({
    description: 'Email of user',
  })
  @IsNotEmpty({ message: 'Email is required' })
  @IsEmail({}, { message: 'Invalid email format' })
  @Matches(Regexs.email, {
    message: 'Invalid email format (Should be like that ex: <EMAIL>)',
  })
  email: string;

  @ApiProperty({
    description: 'Username of user',
    minLength: 3,
    maxLength: 20,
  })
  @IsNotEmpty()
  @MinLength(3)
  @MaxLength(20)
  @Matches(Regexs.Username, {
    message:
      "Username must start with an alphabetic character and may contain alphabetic characters, numbers, dashes, dots, underscores, and the '@' symbol.",
  })
  username: string;

  @ApiProperty({
    description:
      'Password must contain at least 8 characters, one uppercase, one number and one special case character',
    minLength: 6,
    maxLength: 20,
  })
  @IsNotEmpty()
  @Length(8, 20)
  @Matches(Regexs.Password, {
    message: `Password must have 8 to 20 characters including at least one uppercase letter (A-Z), lowercase letter (a-z), numbers (0-9), and special characters (!@#$%^&*()_+{}[\]|:;'"<>?,./)`,
  })
  password: string;
}

export class CheckUsernameDto {
  @ApiProperty({
    description: 'username of user',
  })
  @IsNotEmpty()
  @MinLength(3)
  @MaxLength(20)
  @Matches(Regexs.Username, {
    message:
      "Username must start with an alphabetic character and may contain alphabetic characters, numbers, dashes, dots, underscores, and the '@' symbol.",
  })
  username: string;
}

export class LoginUserDto {
  @ApiProperty({
    description: 'Email or username of user',
  })
  @IsNotEmpty()
  email: string;

  @ApiProperty({
    description: 'password of user',
  })
  @IsNotEmpty()
  password: string;
}

export class ChangePasswordDto {
  @ApiProperty({
    description: 'Old password of user',
  })
  @IsNotEmpty()
  oldPassword: string;

  @ApiProperty({
    description:
      'New password must contain at least 8 characters, one uppercase, one number and one special case character',
    minLength: 6,
    maxLength: 20,
  })
  @IsNotEmpty()
  @Length(8, 20)
  @Matches(Regexs.Password, {
    message:
      'New password must have 8 to 20 characters including at least one uppercase letter (A-Z), lowercase letter (a-z), numbers (0-9), and special characters (@$!)',
  })
  newPassword: string;
}

export class VerifyEmailDto {
  @ApiProperty({
    description: 'Email verification token of user',
  })
  @IsNotEmpty()
  token: string;
}

export class SendMailDto {
  @ApiProperty({
    description: 'Email of user',
  })
  @IsNotEmpty({ message: 'Email is required' })
  @IsEmail({}, { message: 'Invalid email format' })
  @Matches(Regexs.email, {
    message: 'Invalid email format (Should be like that ex: <EMAIL>)',
  })
  email: string;
}

export class ResetPasswordDto {
  @ApiProperty({
    description: 'Email verification token of user',
  })
  @IsNotEmpty()
  token: string;

  @ApiProperty({
    description:
      'New password must contain at least 8 characters, one uppercase, one number and one special case character',
    minLength: 6,
    maxLength: 20,
  })
  @IsNotEmpty()
  @Length(8, 20)
  @Matches(Regexs.Password, {
    message:
      'New password must have 8 to 20 characters including at least one uppercase letter (A-Z), lowercase letter (a-z), numbers (0-9), and special characters (@$!)',
  })
  newPassword: string;
}

export class GetStartedDto {
  @ApiProperty({
    description: 'Enroll plan for service',
    enum: ['free'],
  })
  @IsNotEmpty()
  @IsEnum(Plan)
  plan: string;

  // @ApiProperty({
  //   description: 'Service for your plan',
  //   enum: ['paymaster', 'bundler'],
  // })
  // @IsNotEmpty()
  // @IsEnum(Service)
  // service: string;
}
