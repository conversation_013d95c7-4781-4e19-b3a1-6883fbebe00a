import { Application } from 'src/application/entities/application.entity';
import {
  Entity,
  Column,
  PrimaryGeneratedColumn,
  OneToMany,
  Unique,
  CreateDateColumn,
  UpdateDateColumn,
} from 'typeorm';

@Entity()
@Unique(['email', 'username'])
export class User {
  @PrimaryGeneratedColumn()
  id: number;

  @Column({ nullable: false })
  firstName: string;

  @Column({ nullable: false })
  lastName: string;

  @Column({ nullable: false })
  email: string;

  @Column({ nullable: false })
  username: string;

  @Column({ nullable: false })
  password: string;

  @Column({ nullable: true })
  verifyToken: string;

  @Column({ nullable: true })
  plan: string;

  @Column({ nullable: true })
  resetPassToken: string;

  @Column({ nullable: false, default: false })
  isEmailVerified: boolean;

  @Column({ nullable: false, default: false })
  isActive: boolean;

  @Column({ nullable: false, default: 0 })
  loginAttempts: number;

  @Column({ nullable: true, type: 'timestamp' })
  blockedUntil: Date;

  @OneToMany(() => Application, (application) => application.user)
  applications: Application[];

  @CreateDateColumn({ type: 'timestamp', default: () => 'CURRENT_TIMESTAMP' })
  createdAt: Date;

  @UpdateDateColumn({
    type: 'timestamp',
    default: () => 'CURRENT_TIMESTAMP',
    onUpdate: 'CURRENT_TIMESTAMP',
  })
  updatedAt: Date;
}
