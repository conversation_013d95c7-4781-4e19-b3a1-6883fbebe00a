import { HttpException, HttpStatus, Injectable, Logger } from '@nestjs/common';
import { JwtService } from '@nestjs/jwt';
import * as jwt from 'jsonwebtoken';
import * as bcrypt from 'bcrypt';
import { CommonMessage } from '../CommonMessages/CommonMessages';
import { UserService } from '../user/user.service';
import { DataSource } from 'typeorm';
import { MailService } from 'src/mail/mail.service';
import { formatDate } from 'src/utils/common.service';

@Injectable()
export class AuthService {
  private readonly logger = new Logger(AuthService.name, { timestamp: true });
  constructor(
    private readonly userService: UserService,
    private readonly dataSource: DataSource,
    private readonly jwtService: JwtService,
    private readonly mailService: MailService,
  ) {}

  async validateUser(email: string, password: string) {
    const queryRunner = this.dataSource.createQueryRunner();
    await queryRunner.connect();
    await queryRunner.startTransaction();

    try {
      const user = await this.userService.findUser(email);
      let isMatch = false;
      if (!user) {
        return null;
      }
      if (!user.email) {
        return null;
      }
      if (user && user.password) {
        isMatch = await bcrypt.compare(password, user.password);
      }
      if (!isMatch) {
        if (!user.isActive) {
          throw new HttpException(
            {
              error: true,
              message: CommonMessage.UserBlockedUntil(
                formatDate(user.blockedUntil),
              ),
              statusCode: HttpStatus.BAD_REQUEST,
            },
            HttpStatus.BAD_REQUEST,
          );
        }
        if (user.loginAttempts === 4) {
          user.loginAttempts = 0;
          user.isActive = false;
          user.blockedUntil = new Date(
            new Date().getTime() + 24 * 60 * 60 * 1000,
          );
          const updatedUserData = await this.userService.updateUser(
            user,
            queryRunner,
          );
          const sentResult = await this.mailService.sendEmail(
            user.email,
            'userBlock',
            [user.blockedUntil],
          );
          if (!sentResult) {
            await queryRunner.rollbackTransaction();
            this.logger.verbose(
              'rolled back the query runner for register when failed sent verification email',
            );
            return null;
          }
          if (updatedUserData.error) {
            return null;
          }
          await queryRunner.commitTransaction();
          this.logger.verbose(
            'commited transaction of the query runner for loginAttempts updated',
          );
          throw new HttpException(
            {
              error: true,
              message: CommonMessage.UserBlockedUntil(
                formatDate(user.blockedUntil),
              ),
              statusCode: HttpStatus.BAD_REQUEST,
            },
            HttpStatus.BAD_REQUEST,
          );
        }
        user.loginAttempts++;
        const updatedUserData = await this.userService.updateUser(
          user,
          queryRunner,
        );
        if (updatedUserData.error) {
          return null;
        }
        await queryRunner.commitTransaction();
        this.logger.verbose(
          'commited transaction of the query runner for loginAttempts updated',
        );
        throw new HttpException(
          {
            error: true,
            message: CommonMessage.InvalidCredLimit(5 - user.loginAttempts),
            statusCode: HttpStatus.BAD_REQUEST,
          },
          HttpStatus.BAD_REQUEST,
        );
      }
      if (user && isMatch) {
        if (user) {
          if (
            user.blockedUntil &&
            user.blockedUntil.getTime() > new Date().getTime()
          ) {
            throw new HttpException(
              {
                error: true,
                message: CommonMessage.UserBlockedUntil(
                  formatDate(user.blockedUntil),
                ),
                statusCode: HttpStatus.BAD_REQUEST,
              },
              HttpStatus.BAD_REQUEST,
            );
          }
          if (user.isActive) {
            user.loginAttempts = 0;
            const updatedUserData = await this.userService.updateUser(
              user,
              queryRunner,
            );
            if (updatedUserData.error) {
              return null;
            }
            await queryRunner.commitTransaction();
            this.logger.verbose(
              'commited transaction of the query runner for loginAttempts updated',
            );
          }
          if (!user.isActive) {
            user.blockedUntil = null;
            user.isActive = true;
            user.loginAttempts = 0;
            const updatedUserData = await this.userService.updateUser(
              user,
              queryRunner,
            );
            if (updatedUserData.error) {
              return null;
            }
            await queryRunner.commitTransaction();
            this.logger.verbose(
              'commited transaction of the query runner for loginAttempts updated',
            );
          }
          delete user.password;
          return user;
        }
        return null;
      }
    } finally {
      this.logger.verbose('Releasing the query runner for register...');
      await queryRunner.release();
      this.logger.verbose('register query runner released!');
    }
  }

  async login(user: any) {
    const payload = {
      email: user.email,
    };
    const userData = {
      authToken: this.jwtService.sign(payload),
      userDetails: user,
    };
    return {
      error: false,
      statusCode: HttpStatus.ACCEPTED,
      message: CommonMessage.LoginSuccessfully,
      result: userData,
    };
  }

  async validateToken(token: string) {
    try {
      const decoded: any = jwt.verify(token, process.env.JWT_SECRET);
      const user = await this.userService.findUser(decoded.email);
      if (!user) {
        return { error: true, message: CommonMessage.InvalidCred, data: null };
      }
      return { error: false, data: decoded, message: CommonMessage.Sucess };
    } catch (error) {
      this.logger.error(
        'Error in catch at validateToken',
        JSON.stringify(error),
      );
      return { error: true, message: error.message, data: null };
    }
  }
}
