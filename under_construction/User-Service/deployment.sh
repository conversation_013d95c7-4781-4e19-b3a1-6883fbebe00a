#/bin/bash
ACCESS_KEY="********************"
SECRET_KEY="KxaWgSV6wkgLcoH5NBHnpvHXgv8e18mZIDavJwn0"
REGION="ap-southeast-1"

aws configure set aws_access_key_id $ACCESS_KEY
aws configure set aws_secret_access_key $SECRET_KEY
aws configure set default.region $REGION


docker system prune -af
aws ecr get-login-password --region ap-southeast-1 | docker login --username AWS --password-stdin 964783264466.dkr.ecr.ap-southeast-1.amazonaws.com

docker build --no-cache  -t user-service-abstraction .

docker tag user-service-abstraction:latest 964783264466.dkr.ecr.ap-southeast-1.amazonaws.com/user-service-abstraction:latest

aws ecr get-login-password --region ap-southeast-1 | docker login --username AWS --password-stdin 964783264466.dkr.ecr.ap-southeast-1.amazonaws.com

docker push 964783264466.dkr.ecr.ap-southeast-1.amazonaws.com/user-service-abstraction:latest

ssh -T  ec2-user@13.228.80.15  "/home/<USER>/stage-abstrxn/user.sh"