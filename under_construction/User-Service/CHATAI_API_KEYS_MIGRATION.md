# ChatAI API Keys Migration

## Overview
Moved `llamaCloudApiKey` and `openRouterApiKey` from per-project database storage to global environment variables.

## Changes Made

### 1. Entity Changes
- **File**: `src/chatAi/entities/chatAi.entity.ts`
- **Action**: Removed `llamaCloudApiKey` and `openRouterApiKey` columns
- **Impact**: Database columns will be automatically dropped due to `synchronize: true`

### 2. DTO Changes
- **File**: `src/chatAi/dto/chatAi.dto.ts`
- **Action**: Removed API key fields from `CreateChatAiDto` and `UpdateChatAiDto`
- **Impact**: API endpoints no longer accept these fields

### 3. Service Changes
- **File**: `src/chatAi/chatAi.service.ts`
- **Action**: Removed API key handling from create and update operations
- **Impact**: Service no longer stores/updates API keys in database

### 4. Environment Configuration
- **File**: `.env`
- **Action**: Added global API key configuration:
  ```env
  LLAMA_CLOUD_API_KEY=your-llamacloud-api-key-here
  OPENROUTER_API_KEY=your-openrouter-api-key-here
  ```

## Migration Notes

### Automatic Migration
- TypeORM `synchronize: true` will automatically drop the columns on next restart
- No manual migration needed for development

### Production Migration (if needed)
If manual migration is required, run this SQL:

```sql
-- Backup existing data (optional)
SELECT id, name, llamaCloudApiKey, openRouterApiKey 
FROM chat_ai_projects 
WHERE llamaCloudApiKey IS NOT NULL OR openRouterApiKey IS NOT NULL;

-- Drop columns
ALTER TABLE chat_ai_projects DROP COLUMN IF EXISTS llamaCloudApiKey;
ALTER TABLE chat_ai_projects DROP COLUMN IF EXISTS openRouterApiKey;
```

## Environment Setup Required

### Development
Update `.env` file with your API keys:
```env
LLAMA_CLOUD_API_KEY=your-actual-llamacloud-api-key
OPENROUTER_API_KEY=your-actual-openrouter-api-key
```

### Production
Set environment variables in your deployment platform:
- Railway: Project → Variables tab
- Other platforms: Follow platform-specific environment variable setup

## Benefits
1. **Simplified Configuration**: One set of API keys for all users
2. **Better Security**: API keys not stored in database
3. **Easier Management**: Centralized API key configuration
4. **Cost Control**: Single billing account for all AI services

## Breaking Changes
- API endpoints no longer accept `llamaCloudApiKey` or `openRouterApiKey` parameters
- Existing projects with stored API keys will lose those keys (use global ones instead)
- Frontend/client code needs to be updated to remove API key input fields

## Testing
1. Restart the application to apply schema changes
2. Verify API keys are loaded from environment variables
3. Test ChatAI project creation without API key parameters
4. Test document parsing and chat functionality with global API keys
