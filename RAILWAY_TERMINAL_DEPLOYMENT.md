# Railway Terminal Deployment Guide

## 🚀 Deploy from Terminal in 3 Steps

### 1. **Install Railway CLI**

Choose your preferred installation method:

```bash
# NPM (Recommended - you already have Node.js)
npm install -g @railway/cli

# Or via Homebrew (macOS)
brew install railway

# Or via shell script (Linux/macOS/WSL)
bash <(curl -fsSL cli.new)
```

### 2. **Authenticate with Railway**

```bash
# Login to Railway (opens browser)
railway login
```

**OR for SSH/remote environments:**
```bash
# Browserless login (gives you a pairing code)
railway login --browserless
```

### 3. **Deploy Your Project**

Choose one of these deployment approaches:

## 🎯 Deployment Options

### Option A: Deploy Main Project (Simple)
```bash
# From your main project directory
railway init                    # Create new project (or link existing)
railway up                      # Deploy
```

### Option B: Deploy Railway Build (Optimized)
```bash
# Build for Railway first
npm run build:railway

# Navigate to railway-build directory
cd railway-build

# Initialize & deploy
railway init                    # Create project
railway up                      # Deploy
```

## 🔐 Authentication Details

### **What Credentials Do You Need?**

**None!** 🎉 Railway CLI uses OAuth authentication:

1. **No API keys needed initially**
2. **No passwords to remember**
3. **Browser-based OAuth flow**
4. **Automatic token management**

### **Authentication Flow**

```bash
railway login
```

This will:
1. ✅ Open your browser to `https://railway.com/cli-login`
2. ✅ Ask you to sign in to Railway (GitHub, Google, etc.)
3. ✅ Automatically save authentication token locally
4. ✅ You're ready to deploy!

### **For SSH/Remote Environments**

If you're on a server without a browser:

```bash
railway login --browserless
```

This gives you:
- A URL to visit manually
- A 4-word pairing code to enter
- Same end result - authenticated CLI

## 🏗️ Complete Deployment Workflow

### **First Time Setup**
```bash
# 1. Install CLI
npm install -g @railway/cli

# 2. Login
railway login

# 3. Build your project
npm run build:railway

# 4. Deploy optimized build
cd railway-build
railway init --name "llamacontextgenie"
railway up
```

### **Subsequent Deployments**
```bash
# 1. Build latest changes
npm run build:railway

# 2. Deploy
cd railway-build
railway up
```

## 🔧 Environment Variables

After deploying, set these in Railway dashboard:

```bash
# Required Environment Variables
DATABASE_URL=postgresql://...          # Your database connection
SUPABASE_URL=https://...               # Supabase project URL  
SUPABASE_ANON_KEY=eyJ...               # Supabase anon key
JWT_SECRET=your-random-secret          # JWT signing secret

# Auto-set by Railway
PORT=8080                              # Automatically provided
NODE_ENV=production                    # Automatically provided
```

## 📋 Advanced CLI Commands

### **Project Management**
```bash
railway list                    # List all your projects
railway link                    # Link to existing project
railway status                  # Check project status
railway open                    # Open project in browser
```

### **Environment Management**
```bash
railway environment             # Switch environments
railway variables               # View environment variables
railway variables --set "KEY=VALUE"  # Set environment variable
```

### **Deployment Management**
```bash
railway up                      # Deploy current directory
railway up --detach            # Deploy without watching logs
railway logs                   # View deployment logs
railway down                   # Remove latest deployment
```

### **Local Development**
```bash
railway run npm start          # Run locally with Railway env vars
railway shell                  # Open shell with Railway env vars
```

## 🔑 Token-Based Authentication (CI/CD)

For automated deployments (GitHub Actions, etc.):

### **Get Project Token**
1. Go to Railway dashboard
2. Project Settings → Tokens
3. Create a new Project Token
4. Copy the token

### **Use Token**
```bash
# Set environment variable
export RAILWAY_TOKEN="your-project-token-here"

# Deploy without interactive login
railway up
```

Or for one-time use:
```bash
RAILWAY_TOKEN="your-token" railway up
```

## 🚨 Troubleshooting

### **Common Issues**

**"Command not found: railway"**
```bash
# Reinstall CLI
npm install -g @railway/cli
# Or add to PATH if needed
```

**"Not logged in"**
```bash
# Re-authenticate
railway login
```

**"No project linked"**
```bash
# Link to existing project
railway link

# Or create new project
railway init
```

**"Permission denied"**
- Make sure you're logged in: `railway whoami`
- Check if you have access to the project
- Verify you're in the right team/organization

### **Check Your Status**
```bash
railway whoami                  # Check if logged in
railway status                 # Check project status  
railway list                   # List available projects
```

## 🎯 Quick Commands Summary

```bash
# Setup (one time)
npm install -g @railway/cli
railway login

# Deploy workflow
npm run build:railway
cd railway-build
railway init  # or railway link
railway up

# Management
railway logs        # View logs
railway open        # Open in browser
railway variables   # Manage env vars
```

## ✅ Your Project is Railway-Ready!

Your current setup works perfectly with Railway. The CLI makes deployment seamless:

1. **No complex configuration needed**
2. **Your build scripts already work**
3. **Health check endpoint already exists**
4. **Static files served correctly**

Just `railway login` and `railway up` - you're deployed! 🚀 