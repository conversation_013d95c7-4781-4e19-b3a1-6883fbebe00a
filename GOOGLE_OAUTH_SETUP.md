# Google OAuth Setup Guide for DocChat AI

## Overview
This guide will walk you through setting up Google OAuth authentication in your Supabase project to enable one-click Google sign-in for your DocChat AI application.

## Prerequisites
- Supabase project created and configured
- Google Cloud Console account
- Your application running on a known domain

## Step 1: Configure Google Cloud Console

### 1.1 Create Google Cloud Project
1. Go to [Google Cloud Console](https://console.cloud.google.com/)
2. Create a new project or select an existing one
3. Enable the Google+ API (or Google Identity API)

### 1.2 Create OAuth 2.0 Credentials
1. Navigate to **APIs & Services** > **Credentials**
2. Click **Create Credentials** > **OAuth 2.0 Client IDs**
3. Select **Web application** as the application type
4. Configure the following:
   - **Name**: DocChat AI
   - **Authorized JavaScript origins**: 
     - `https://your-project-ref.supabase.co`
     - `http://localhost:5000` (for local development)
   - **Authorized redirect URIs**:
     - `https://your-project-ref.supabase.co/auth/v1/callback`

### 1.3 Get Your Credentials
1. Copy the **Client ID** and **Client Secret**
2. Keep these secure - you'll need them for Supabase

## Step 2: Configure Supabase

### 2.1 Enable Google Provider
1. Go to your Supabase dashboard
2. Navigate to **Authentication** > **Providers**
3. Find **Google** in the list
4. Toggle it **ON**

### 2.2 Add Google Credentials
1. Paste your **Client ID** from Google Cloud Console
2. Paste your **Client Secret** from Google Cloud Console
3. Click **Save**

### 2.3 Configure Redirect URLs (Optional)
1. Go to **Authentication** > **Settings**
2. Under **Site URL**, ensure your production URL is set
3. Under **Redirect URLs**, add any additional URLs if needed

## Step 3: Test the Integration

### 3.1 Test Google Sign-In
1. Open your DocChat AI application
2. Click the **Sign In** button
3. Click **Continue with Google**
4. You should be redirected to Google's OAuth consent screen
5. After granting permissions, you'll be redirected back to your app
6. You should be automatically signed in

### 3.2 Verify User Data
1. Check that the user appears in your Supabase **Authentication** > **Users** table
2. Verify that the user's Google profile information is populated
3. Test that the user can access protected features in your app

## Step 4: Troubleshooting

### Common Issues and Solutions

#### "Unsupported provider" Error
- **Cause**: Google provider not enabled in Supabase
- **Solution**: Follow Step 2.1 to enable the Google provider

#### "Invalid redirect URI" Error
- **Cause**: Redirect URI mismatch between Google Console and Supabase
- **Solution**: Ensure the redirect URI in Google Console exactly matches: `https://your-project-ref.supabase.co/auth/v1/callback`

#### "Access blocked" Error
- **Cause**: Google OAuth consent screen not properly configured
- **Solution**: Configure the OAuth consent screen in Google Cloud Console

#### User Not Redirected After Sign-In
- **Cause**: Site URL not properly configured in Supabase
- **Solution**: Set the correct Site URL in Supabase Authentication settings

## Step 5: Security Best Practices

### 5.1 OAuth Consent Screen
1. Configure a proper OAuth consent screen in Google Cloud Console
2. Add your app's privacy policy and terms of service URLs
3. Request only the minimum required scopes (email, profile)

### 5.2 Domain Verification
1. Verify your domain in Google Cloud Console
2. This removes the "unverified app" warning for users

### 5.3 Environment Variables
1. Never commit OAuth secrets to version control
2. Use Supabase's secure environment variable storage
3. Rotate secrets regularly

## Testing Checklist

- [ ] Google provider enabled in Supabase
- [ ] OAuth credentials configured correctly
- [ ] Redirect URIs match exactly
- [ ] Google sign-in button works
- [ ] User is redirected to Google OAuth
- [ ] User is redirected back to app after consent
- [ ] User is automatically signed in
- [ ] User data appears in Supabase Users table
- [ ] User can access protected features

## Support

If you encounter issues:
1. Check the browser developer console for error messages
2. Verify all URLs and credentials are correct
3. Test with a different Google account
4. Check Supabase logs for authentication errors

The Google OAuth integration is now fully configured and ready for production use!