-- Migration: Update Free User Credits from 7 to 30
-- Execute this in Supabase SQL Editor to update existing free users

-- First, let's see what we're working with (optional, for verification)
-- SELECT id, email, subscription_status, credits, total_credits_used 
-- FROM user_profiles 
-- WHERE subscription_status = 'free';

-- Update all free users to have 30 credits (from previous 7)
-- This accounts for credits they may have already used
UPDATE user_profiles 
SET credits = 30 + total_credits_used
WHERE subscription_status = 'free' 
  AND credits < 30 + total_credits_used;

-- Alternative approach: Set all free users to exactly 30 available credits
-- (This resets their available credits to 30 regardless of previous usage)
-- Uncomment the line below if you prefer this approach:

-- UPDATE user_profiles 
-- SET credits = 30 + total_credits_used
-- WHERE subscription_status = 'free';

-- Verify the update (optional)
-- SELECT id, email, subscription_status, credits, total_credits_used,
--        (credits - total_credits_used) as available_credits
-- FROM user_profiles 
-- WHERE subscription_status = 'free';

-- Summary of what this migration does:
-- 1. Updates all free users who currently have less than 30 available credits
-- 2. Sets their total credits to 30 + what they've already used
-- 3. This ensures they have exactly 30 available credits to use
-- 4. Does not affect Pro users or users who somehow already have 30+ credits

COMMIT; 