# ⚠️  SECURITY WARNING: <PERSON>VER COMMIT REAL SECRETS TO VERSION CONTROL ⚠️
# This file contains EXAMPLE values only. Use your own secrets in production.
# Real environment variables should be set directly in your deployment platform.

# Database Configuration (EXAMPLE - use your own database)
DATABASE_URL="postgresql://username:password@localhost:5432/database_name"

# External API Keys (EXAMPLE - get your own from respective services)
LLAMA_CLOUD_API_KEY=llx-your-llamacloud-api-key-here
OPENROUTER_API_KEY=sk-or-v1-your-openrouter-api-key-here

# Supabase Configuration (EXAMPLE - use your own Supabase project)
SUPABASE_SERVICE_ROLE_KEY=your-supabase-service-role-key
SUPABASE_URL=https://your-project-id.supabase.co
VITE_SUPABASE_ANON_KEY=your-supabase-anon-key
VITE_SUPABASE_URL=https://your-project-id.supabase.co

# Application URLs (update for your deployment)
APP_URL=http://localhost:8080
VITE_APP_URL=http://localhost:8080

# Development Configuration
VITE_HMR_HOST=localhost

# Security Keys (EXAMPLE - generate your own strong secrets)
EMBED_JWT_SECRET=your-secure-jwt-secret-at-least-32-characters-long
ADMIN_SECRET_KEY=your-admin-secret-key-for-admin-endpoints

# ============================================================
# PRODUCTION DEPLOYMENT INSTRUCTIONS:
# ============================================================
# 1. NEVER use these example values in production
# 2. Set environment variables directly in your hosting platform:
#    - Railway: Project Settings → Variables
#    - Vercel: Project Settings → Environment Variables  
#    - AWS: Use Parameter Store or Secrets Manager
#    - Docker: Use docker-compose.yml environment section
# 3. Generate new secrets using: openssl rand -hex 32
# 4. Rotate all API keys if they were previously exposed
# ============================================================

