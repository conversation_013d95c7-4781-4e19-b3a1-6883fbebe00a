# 🛡️ Iframe Embed Security Guide

## The Problem You're Facing

When you provide iframe embed codes to users, there's a real security concern: **anyone can copy the iframe code from browser inspector and use it on unauthorized websites**. This can lead to:

- Unauthorized usage of your service
- Increased API costs without proper attribution
- Content being embedded on malicious websites
- Loss of control over where your content appears

## 🔒 Comprehensive Security Solutions Implemented

### 1. **Domain Whitelisting** (Primary Defense)

**How it works:**
- Users must register trusted domains where embeds are allowed
- Server validates the `Referer` or `Origin` header on each request
- Only whitelisted domains can successfully load embed content

**Implementation:**
```javascript
// Check referrer domain against whitelist
async function isEmbedAllowed(documentId, projectId, referrer) {
  if (!referrer) return false;
  
  const referrerUrl = new URL(referrer);
  const domain = referrerUrl.hostname;
  
  // Check if domain is whitelisted for this user
  const whitelistedDomains = await storage.getEmbedDomains(ownerId);
  return whitelistedDomains.some(d => d.domain === domain && d.isActive);
}
```

### 2. **JWT Token Authentication** (Advanced Security)

**How it works:**
- Generate time-limited JWT tokens tied to specific content
- Tokens expire after 24 hours
- Each token is linked to specific document/project

**Implementation:**
```javascript
// Generate secure embed token
function generateEmbedToken(userId, documentId, projectId) {
  const payload = {
    userId,
    documentId,
    projectId,
    type: 'embed',
    exp: Math.floor(Date.now() / 1000) + (24 * 60 * 60) // 24 hours
  };
  
  return jwt.sign(payload, EMBED_JWT_SECRET);
}
```

### 3. **Rate Limiting** (Abuse Prevention)

**Configuration:**
- 100 requests per 15 minutes per IP
- Prevents automated scraping and abuse
- Protects against DoS attacks

```javascript
const embedRateLimit = rateLimit({
  windowMs: 15 * 60 * 1000, // 15 minutes
  max: 100, // limit each IP to 100 requests per windowMs
  message: { message: "Too many embed requests, please try again later" }
});
```

### 4. **Secure CORS Headers**

**Implementation:**
- Dynamic CORS based on referrer validation
- Restrictive headers prevent unauthorized cross-origin requests

```javascript
// Set CORS based on referrer validation
const referrer = req.get('Referer') || req.get('Origin');
if (referrer && isValidReferrer(referrer)) {
  const referrerUrl = new URL(referrer);
  res.setHeader('Access-Control-Allow-Origin', referrerUrl.origin);
} else {
  res.setHeader('Access-Control-Allow-Origin', 'null');
}

res.setHeader('X-Frame-Options', 'SAMEORIGIN');
```

### 5. **Usage Tracking & Analytics**

**Features:**
- Track which domains are using embeds
- Monitor usage patterns and detect anomalies
- Generate usage reports for administrators

```javascript
// Track embed usage
await storage.trackEmbedUsage(
  userId, 
  documentId, 
  projectId, 
  domain, 
  ipAddress, 
  userAgent
);
```

## 🌐 How Other Major Platforms Handle This

### **YouTube Embeds**
- **Domain Restrictions**: Can whitelist specific domains
- **API Keys**: Require API keys for embedded players
- **Referrer Validation**: Check referrer headers
- **Content Protection**: Disable embedding for copyrighted content

### **Google Maps Embeds**
- **API Key Restrictions**: Keys can be restricted to specific domains
- **Usage Quotas**: Rate limiting and usage monitoring
- **Referrer Restrictions**: HTTP referrer restrictions in API console

### **Twitter/X Embeds**
- **Intent URLs**: Use intent URLs that redirect through Twitter
- **Domain Validation**: Check referrer for sensitive content
- **Rate Limiting**: Aggressive rate limiting on embed endpoints

### **Vimeo Embeds**
- **Domain Whitelisting**: Pro accounts can restrict embed domains
- **Password Protection**: Embed-specific passwords
- **Player Customization**: Remove branding/controls based on account type

### **Typeform Embeds**
- **Domain Restrictions**: Can specify allowed domains
- **SSL Requirements**: HTTPS-only embedding
- **Usage Analytics**: Track where forms are embedded

### **Stripe Payment Elements**
- **Domain Verification**: Strict domain validation
- **API Key Restrictions**: Domain-restricted API keys
- **CSP Headers**: Content Security Policy requirements

## 🚀 Advanced Security Measures

### **Content Security Policy (CSP)**
Recommend users add CSP headers to their websites:

```html
<meta http-equiv="Content-Security-Policy" 
      content="frame-src https://yourdomain.com; object-src 'none';">
```

### **Subdomain Strategy**
Use dedicated subdomains for embeds:
- `embed.yourdomain.com` - Isolates embed traffic
- Easier to implement security policies
- Better analytics and monitoring

### **Token Rotation**
- Implement automatic token rotation
- Invalidate old tokens periodically
- Allow manual token revocation

### **Watermarking**
- Add subtle branding to embedded content
- Include source attribution
- Makes unauthorized usage more obvious

## 📊 Monitoring & Detection

### **Anomaly Detection**
- Unusual traffic patterns from unknown domains
- Spike in requests from specific IPs
- Geographic anomalies in usage

### **Alerts System**
- Email notifications for new domain usage
- Dashboard warnings for suspicious activity
- Usage threshold alerts

### **Regular Audits**
- Review whitelisted domains quarterly
- Remove inactive or suspicious domains
- Update security policies based on usage patterns

## 🛠️ Implementation Checklist

- [x] **Domain Whitelisting System**
  - [x] Database table for whitelisted domains
  - [x] API endpoints for domain management
  - [x] Referrer validation middleware

- [x] **JWT Token System**
  - [x] Token generation endpoint
  - [x] Token validation middleware
  - [x] Expiration handling

- [x] **Rate Limiting**
  - [x] IP-based rate limiting
  - [x] Configurable limits
  - [x] Error handling

- [x] **Secure Headers**
  - [x] Dynamic CORS headers
  - [x] X-Frame-Options
  - [x] Security headers

- [x] **Usage Tracking**
  - [x] Database schema for usage logs
  - [x] Analytics dashboard
  - [x] Reporting system

- [x] **User Interface**
  - [x] Domain management UI
  - [x] Token generation interface
  - [x] Security dashboard

## 🔧 Configuration

### **Environment Variables**
```bash
# JWT Secret for embed tokens
EMBED_JWT_SECRET=your-super-secret-key-here

# Rate limiting configuration
EMBED_RATE_LIMIT_WINDOW=900000  # 15 minutes
EMBED_RATE_LIMIT_MAX=100        # Max requests per window

# Security settings
EMBED_REQUIRE_HTTPS=true
EMBED_ALLOW_LOCALHOST=false     # For development only
```

### **Database Setup**
```sql
-- Run the migrations in create_tables.sql
-- Adds embed_domains and embed_usage tables
```

## 🎯 Best Practices for Users

### **For Embed Publishers (Your Users)**
1. **Whitelist Specific Domains**: Don't use wildcards unless necessary
2. **Use HTTPS**: Always embed over secure connections
3. **Regular Reviews**: Periodically review and clean up domain lists
4. **Monitor Usage**: Check embed analytics regularly
5. **Use Tokens**: For sensitive content, use token-based embeds

### **For Website Owners (Using Embeds)**
1. **CSP Headers**: Implement Content Security Policy
2. **Regular Updates**: Keep embed codes updated
3. **Test Regularly**: Ensure embeds work after site changes
4. **Respect Terms**: Follow the embed provider's terms of service

## 🆘 Incident Response

### **If Unauthorized Usage is Detected**
1. **Immediate Action**: Remove the domain from whitelist
2. **Revoke Tokens**: Invalidate any active tokens
3. **Investigate**: Check usage logs for patterns
4. **Notify**: Inform the account owner
5. **Document**: Record the incident for future prevention

### **Recovery Steps**
1. **Generate New Tokens**: Create fresh tokens for legitimate users
2. **Update Whitelist**: Add verified domains back
3. **Monitor**: Increased monitoring for 48 hours
4. **Review Policies**: Update security policies if needed

## 🔮 Future Enhancements

### **Machine Learning Detection**
- AI-powered anomaly detection
- Behavioral analysis of embed usage
- Automatic threat scoring

### **Advanced Analytics**
- Real-time usage dashboards
- Geographic usage mapping
- Performance impact analysis

### **Integration APIs**
- Webhook notifications for security events
- Third-party security tool integrations
- Automated response systems

## 📞 Support & Troubleshooting

### **Common Issues**
1. **"Embed not authorized for this domain"**
   - Solution: Add the domain to your whitelist

2. **"Token expired"**
   - Solution: Generate a new token

3. **Rate limit exceeded**
   - Solution: Implement proper caching, reduce request frequency

### **Debug Mode**
For development, you can temporarily disable domain restrictions:
```javascript
// Development only - NEVER in production
const isDevelopment = process.env.NODE_ENV === 'development';
if (isDevelopment && origin === 'http://localhost:8080') {
  isAuthorized = true;
}
```

---

**Remember**: Security is a layered approach. No single measure is foolproof, but combining multiple strategies creates a robust defense against unauthorized embed usage. 