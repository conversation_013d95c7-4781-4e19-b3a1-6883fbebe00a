-- Embed Security Tables Migration for Supabase (FIXED)
-- Copy and paste this entire script into Supabase SQL Editor

-- Embed domains table for security
CREATE TABLE IF NOT EXISTS public.embed_domains (
    id SERIAL PRIMARY KEY,
    user_id UUID NOT NULL,
    project_id INTEGER,
    domain VARCHAR(255) NOT NULL,
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW() NOT NULL,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW() NOT NULL,
    CONSTRAINT fk_embed_domains_user_id FOREIGN KEY (user_id) REFERENCES auth.users (id) ON DELETE CASCADE,
    CONSTRAINT fk_embed_domains_project_id FOREIGN KEY (project_id) REFERENCES public.projects (id) ON DELETE CASCADE,
    CONSTRAINT unique_user_project_domain UNIQUE(user_id, project_id, domain)
);

-- Embed usage tracking
CREATE TABLE IF NOT EXISTS public.embed_usage (
    id SERIAL PRIMARY KEY,
    user_id UUID NOT NULL,
    document_id INTEGER,
    project_id INTEGER,
    domain VARCHAR(255),
    ip_address INET,
    user_agent TEXT,
    requests_count INTEGER DEFAULT 1,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW() NOT NULL,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW() NOT NULL,
    CONSTRAINT fk_embed_usage_user_id FOREIGN KEY (user_id) REFERENCES auth.users (id) ON DELETE CASCADE,
    CONSTRAINT fk_embed_usage_document_id FOREIGN KEY (document_id) REFERENCES public.documents (id) ON DELETE CASCADE,
    CONSTRAINT fk_embed_usage_project_id FOREIGN KEY (project_id) REFERENCES public.projects (id) ON DELETE CASCADE
);

-- Enable Row Level Security
ALTER TABLE public.embed_domains ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.embed_usage ENABLE ROW LEVEL SECURITY;

-- Create RLS policies for embed_domains
CREATE POLICY "Users can manage their own embed domains" ON public.embed_domains
    FOR ALL USING (auth.uid() = user_id);

-- Create RLS policies for embed_usage
CREATE POLICY "Users can view their own embed usage" ON public.embed_usage
    FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "System can insert embed usage" ON public.embed_usage
    FOR INSERT WITH CHECK (true);

-- Indexes for faster domain lookups
CREATE INDEX IF NOT EXISTS idx_embed_domains_user_id ON public.embed_domains(user_id);
CREATE INDEX IF NOT EXISTS idx_embed_domains_project_id ON public.embed_domains(project_id);
CREATE INDEX IF NOT EXISTS idx_embed_domains_domain ON public.embed_domains(domain);
CREATE INDEX IF NOT EXISTS idx_embed_domains_active ON public.embed_domains(is_active);

-- Indexes for usage tracking
CREATE INDEX IF NOT EXISTS idx_embed_usage_user_id ON public.embed_usage(user_id);
CREATE INDEX IF NOT EXISTS idx_embed_usage_domain ON public.embed_usage(domain);
CREATE INDEX IF NOT EXISTS idx_embed_usage_created_at ON public.embed_usage(created_at);
CREATE INDEX IF NOT EXISTS idx_embed_usage_document_id ON public.embed_usage(document_id);
CREATE INDEX IF NOT EXISTS idx_embed_usage_project_id ON public.embed_usage(project_id);

-- Simple function to track embed usage (no complex unique constraints)
CREATE OR REPLACE FUNCTION public.track_embed_usage(
    p_user_id UUID,
    p_document_id INTEGER DEFAULT NULL,
    p_project_id INTEGER DEFAULT NULL,
    p_domain VARCHAR(255) DEFAULT NULL,
    p_ip_address INET DEFAULT NULL,
    p_user_agent TEXT DEFAULT NULL
)
RETURNS VOID
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
BEGIN
    -- Simply insert a new record for each usage
    -- We can aggregate later for analytics
    INSERT INTO public.embed_usage (
        user_id, 
        document_id, 
        project_id, 
        domain, 
        ip_address, 
        user_agent,
        requests_count
    )
    VALUES (
        p_user_id,
        p_document_id,
        p_project_id,
        p_domain,
        p_ip_address,
        p_user_agent,
        1
    );
        
EXCEPTION
    WHEN OTHERS THEN
        -- Silently fail to avoid breaking embed functionality
        NULL;
END;
$$;

-- Grant necessary permissions
GRANT USAGE ON SCHEMA public TO anon, authenticated;
GRANT SELECT, INSERT, UPDATE, DELETE ON public.embed_domains TO authenticated;
GRANT SELECT, INSERT ON public.embed_usage TO authenticated, anon;
GRANT EXECUTE ON FUNCTION public.track_embed_usage TO authenticated, anon; 