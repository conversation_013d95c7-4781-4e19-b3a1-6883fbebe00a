# LlamaContextGenie Architecture Analysis

## Table of Contents

1. [Overview](#overview)
2. [Database Schema & Relationships](#database-schema--relationships)
3. [Complete Flow Analysis](#complete-flow-analysis)
4. [Service Architecture](#service-architecture)
5. [Integration Points](#integration-points)
6. [Security Implementation](#security-implementation)
7. [Recommendations](#recommendations)

## Overview

LlamaContextGenie is an intelligent document Q&A application powered by LlamaIndex and OpenRouter. The system follows a dual-service architecture with a main Express.js application and plans for a separate NestJS ChatAI service integration.

### Key Technologies

- **Backend**: Express.js with TypeScript
- **Database**: PostgreSQL with Drizzle ORM
- **Authentication**: Supabase Auth
- **AI Services**: LlamaIndex (Vector DB), OpenRouter (LLM)
- **Document Processing**: LlamaParse
- **Frontend**: React with Vite

## Database Schema & Relationships

### Core Tables Structure

```sql
-- User Management
user_profiles (
  id TEXT PRIMARY KEY,           -- Supabase user ID
  email TEXT NOT NULL,
  subscription_status TEXT DEFAULT 'free',
  credits INTEGER DEFAULT 30,
  total_credits_used INTEGER DEFAULT 0,
  created_at TIMESTAMP,
  updated_at TIMESTAMP
)

-- Project Management
projects (
  id SERIAL PRIMARY KEY,
  name TEXT NOT NULL,
  description TEXT,
  user_id TEXT NOT NULL,         -- FK to user_profiles.id
  index_id TEXT,                 -- LlamaIndex Vector DB ID
  status TEXT DEFAULT 'active',
  created_at TIMESTAMP,
  updated_at TIMESTAMP
)

-- Document Management
documents (
  id SERIAL PRIMARY KEY,
  project_id INTEGER NOT NULL,   -- FK to projects.id
  user_id TEXT NOT NULL,         -- FK to user_profiles.id
  filename TEXT NOT NULL,
  filesize INTEGER NOT NULL,
  content_type TEXT NOT NULL,
  status TEXT DEFAULT 'uploading', -- uploading→parsing→indexing→ready
  parsed_data JSONB,             -- LlamaParse output
  summary JSONB,                 -- AI-generated summary
  page_count INTEGER,
  word_count INTEGER,
  error_message TEXT,
  created_at TIMESTAMP
)

-- Chat System
chat_messages (
  id SERIAL PRIMARY KEY,
  project_id INTEGER NOT NULL,   -- FK to projects.id
  document_id INTEGER,           -- FK to documents.id (optional)
  question TEXT NOT NULL,
  response TEXT NOT NULL,
  source_references JSONB,      -- Vector search results
  is_admin_debug BOOLEAN DEFAULT false,
  timestamp TIMESTAMP
)

-- Credit Tracking
credit_usage (
  id SERIAL PRIMARY KEY,
  user_id TEXT NOT NULL,         -- FK to user_profiles.id
  action_type TEXT NOT NULL,     -- 'project_create', 'document_upload', 'chat_message'
  action_id INTEGER,             -- Reference to project/document/message ID
  credits_used INTEGER DEFAULT 1,
  timestamp TIMESTAMP
)

-- Embed Security
embed_domains (
  id SERIAL PRIMARY KEY,
  user_id TEXT NOT NULL,         -- FK to user_profiles.id
  project_id INTEGER,            -- FK to projects.id
  domain VARCHAR(255) NOT NULL,
  is_active BOOLEAN DEFAULT true,
  created_at TIMESTAMP,
  updated_at TIMESTAMP
)

-- Embed Analytics
embed_usage (
  id SERIAL PRIMARY KEY,
  user_id TEXT NOT NULL,         -- FK to user_profiles.id
  document_id INTEGER,           -- FK to documents.id
  project_id INTEGER,            -- FK to projects.id
  domain VARCHAR(255),
  ip_address VARCHAR(45),
  user_agent TEXT,
  requests_count INTEGER DEFAULT 1,
  created_at TIMESTAMP,
  updated_at TIMESTAMP
)
```

### Relationship Diagram

```
user_profiles (1) ──→ (∞) projects
    │                     │
    │                     └──→ (∞) documents
    │                           │
    │                           └──→ (∞) chat_messages
    │
    ├──→ (∞) credit_usage
    ├──→ (∞) embed_domains
    └──→ (∞) embed_usage
```

## Complete Flow Analysis

### Phase 1: Project Creation

**Endpoint**: `POST /api/projects`

**Flow**:

1. **Authentication Check** → Validate Supabase JWT token
2. **Credit Validation** → Check user limits and available credits
3. **Credit Deduction** → Deduct 1 credit for project creation
4. **Database Insert** → Create project record with `index_id: null`
5. **Response** → Return project with auto-generated ID

**Key Code**:

```javascript
// Credit check and deduction
const limits = await storage.checkUserLimits(req.user!.id);
const creditsDeducted = await storage.deductCredits(req.user!.id, 'project_create');

// Project creation
const project = await storage.createProject({
  name: validated.name,
  description: validated.description,
  userId: req.user!.id,
});
```

### Phase 2: Document Upload & Processing

**Endpoint**: `POST /api/documents/upload`

**Processing Pipeline**:

```
File Upload → Validation → Credit Check → Database Insert → Background Processing
                                              ↓
                                         processDocumentAsync()
                                              ↓
                                    LlamaParse → LlamaIndex → Ready
```

**Detailed Steps**:

1. **File Upload & Validation**

   - Content type validation
   - Malicious file detection
   - File size limits

2. **Credit System**

   - Deduct 1 credit per document upload
   - Check subscription limits

3. **Database Operations**

   - Insert document record with `status: 'uploading'`
   - Return document ID immediately

4. **Background Processing** (`processDocumentAsync`)

   - **Parsing Phase**:

     - Update status to `'parsing'`
     - Use LlamaParse for PDF/Office files
     - Direct text extraction for plain text files
     - Store parsed content in `parsed_data` field

   - **Indexing Phase**:

     - Update status to `'indexing'`
     - Create LlamaIndex vector embedding
     - Generate unique `index_id`
     - Store `index_id` in projects table

   - **Completion Phase**:
     - Update status to `'ready'`
     - Generate AI summary (optional)
     - Store summary in `summary` field

**Key Code**:

```javascript
// Document processing pipeline
async function processDocumentAsync(documentId, fileBuffer, filename) {
  // Phase 1: Parsing
  await storage.updateDocumentStatus(documentId, "parsing");
  const parseResult = await llamaParseService.parseFile(fileBuffer, filename);

  // Phase 2: Indexing
  await storage.updateDocumentStatus(documentId, "indexing", {
    parsedData: parseResult.result,
    pageCount: parseResult.result.metadata.page_count,
    wordCount: parseResult.result.metadata.word_count,
  });

  // Phase 3: Vector Index Creation
  const indexResult = await llamaIndexService.createIndex(
    parseResult.result,
    filename
  );

  // Phase 4: Completion
  await storage.updateDocumentStatus(documentId, "ready", {
    indexId: indexResult.id,
    summary: documentSummary,
  });
}
```

### Phase 3: Chat & Vector Search

**Endpoint**: `POST /api/chat/ask`

**Chat Flow**:

```
User Question → Vector Search → Context Building → AI Generation → Response Storage
```

**Detailed Process**:

1. **Input Validation**

   - Validate question content
   - Check project/document ownership
   - Rate limiting checks

2. **Vector Search**

   - Query LlamaIndex with `index_id`
   - Retrieve relevant document chunks
   - Extract source references

3. **Context Building**

   - Combine retrieved chunks
   - Format context for LLM
   - Include metadata

4. **AI Response Generation**

   - Stream response from OpenRouter
   - Real-time response to client
   - Handle incomplete responses

5. **Message Storage**
   - Save question and response
   - Store source references
   - Update credit usage

**Key Code**:

```javascript
// Vector search and context building
const retrieveResult = await llamaIndexService.retrieve(
  project.indexId,
  question
);
const context = retrieveResult.nodes.map((node) => node.text).join("\n\n");

// Streaming AI response
const stream = await openRouterService.generateStreamResponse(
  question,
  context
);
for await (const chunk of stream) {
  fullResponse += chunk;
  res.write(`data: ${JSON.stringify({ content: chunk })}\n\n`);
}

// Save chat message
await storage.createChatMessage({
  projectId,
  documentId,
  question,
  response: fullResponse,
  sourceReferences: retrieveResult.nodes,
});
```

### Phase 4: Embed & SDK API

**Public Endpoints**:

- `GET /api/embed/projects/:id` - Public project information
- `POST /api/embed/chat/ask` - Public chat with domain restrictions
- `GET /api/embed/domains` - Domain management for users

**Embed Security Architecture**:

```
Embed Widget → Domain Check → Token Validation → Vector Search → AI Response → Usage Tracking
```

**Security Layers**:

1. **Domain Whitelist System**

   - Store allowed domains in `embed_domains` table
   - Validate referrer header against whitelist
   - Per-project domain restrictions

2. **JWT Token Authentication**

   - Generate time-limited tokens (24 hours)
   - Token tied to specific project/document
   - Enhanced security for sensitive content

3. **Rate Limiting**

   - IP-based rate limiting for embed endpoints
   - Prevent abuse and API flooding
   - Different limits for authenticated vs public access

4. **Usage Tracking**
   - Track embed usage in `embed_usage` table
   - Monitor domain, IP, user agent
   - Analytics for embed performance

**Key Code**:

```javascript
// Domain validation for embed
async function isEmbedAllowed(documentId, projectId, referrer) {
  const domains = await storage.getEmbedDomains(ownerId, projectId);
  return domains.some(
    (domain) => domain.isActive && referrer?.includes(domain.domain)
  );
}

// JWT token generation
function generateEmbedToken(userId, documentId, projectId) {
  const payload = {
    userId,
    documentId,
    projectId,
    type: "embed",
    exp: Math.floor(Date.now() / 1000) + 24 * 60 * 60,
  };
  return jwt.sign(payload, EMBED_JWT_SECRET);
}
```

## Service Architecture

### Current Architecture (Single Service)

```
┌─────────────────────────────────────────────────────────────┐
│                    Express.js Application                   │
├─────────────────────────────────────────────────────────────┤
│  Authentication │  Project Mgmt │  Document Processing      │
│  Chat System    │  Embed API    │  Credit Management        │
└─────────────────────────────────────────────────────────────┘
                              │
                              ▼
┌─────────────────────────────────────────────────────────────┐
│                    PostgreSQL Database                      │
│  user_profiles │ projects │ documents │ chat_messages       │
│  credit_usage  │ embed_domains │ embed_usage               │
└─────────────────────────────────────────────────────────────┘
                              │
                              ▼
┌─────────────────────────────────────────────────────────────┐
│                    External Services                        │
│  Supabase Auth │ LlamaIndex │ OpenRouter │ LlamaParse       │
└─────────────────────────────────────────────────────────────┘
```

### Proposed Dual-Service Architecture

```
┌─────────────────────────────┐    ┌─────────────────────────────┐
│     NestJS ChatAI Service   │    │    Express Embed Service    │
│         (Port 3000)         │    │         (Port 3001)         │
├─────────────────────────────┤    ├─────────────────────────────┤
│ • Project Management        │    │ • Public Embed API          │
│ • Document Upload           │    │ • SDK Endpoints             │
│ • Processing Pipeline       │    │ • Domain Validation         │
│ • User Authentication       │    │ • Rate Limiting             │
│ • Credit Management         │    │ • Usage Analytics           │
│ • Admin Dashboard           │    │ • Lightweight Responses     │
└─────────────────────────────┘    └─────────────────────────────┘
                │                                    │
                └────────────────┬───────────────────┘
                                 ▼
                ┌─────────────────────────────────────┐
                │        Shared PostgreSQL            │
                │                                     │
                │  NestJS: Full Read/Write Access     │
                │  Express: Read-Only + Limited Write │
                └─────────────────────────────────────┘
```

**Service Responsibilities**:

**NestJS ChatAI Service (Main Application)**:

- Complete CRUD operations
- User authentication & authorization
- Document processing pipeline
- Credit system management
- Admin features
- Complex business logic

**Express Embed Service (Public API)**:

- High-performance public endpoints
- Embed widget serving
- SDK API endpoints
- Domain-based security
- Read-only database access
- Usage analytics

## Integration Points

### 1. LlamaIndex Vector Database Integration

**Storage Strategy**:

- `projects.index_id` stores the LlamaIndex vector database identifier
- Each project can have multiple documents indexed together
- Vector search uses `index_id` to query relevant content

**Implementation Flow**:

```javascript
// During document processing
const indexResult = await llamaIndexService.createIndex(parsedData, filename);
await storage.updateProject(projectId, { indexId: indexResult.id });

// During chat queries
const searchResults = await llamaIndexService.retrieve(
  project.indexId,
  question
);
```

### 2. Credit System Integration

**Credit Deduction Points**:

- Project Creation: 1 credit
- Document Upload: 1 credit
- Chat Message: 1 credit

**Implementation**:

```javascript
// Atomic credit deduction
await storage.deductCredits(userId, "document_upload", documentId, 1);

// Credit validation
const limits = await storage.checkUserLimits(userId);
if (!limits.canCreateProject) {
  throw new Error("Insufficient credits");
}
```

### 3. Supabase Authentication Integration

**User Management**:

- Supabase handles authentication
- `user_profiles.id` matches Supabase user ID
- JWT tokens validated against Supabase

**Row Level Security**:

```sql
-- RLS Policy Example
CREATE POLICY "Users can only see their own data"
ON projects FOR ALL
USING (auth.uid()::text = user_id);
```

### 4. Document Processing Pipeline

**Status Flow**:

```
uploading → parsing → indexing → ready
     ↓         ↓         ↓        ↓
  File Save → LlamaParse → LlamaIndex → Complete
```

**Key Storage Points**:

- `parsed_data`: Raw text from LlamaParse
- `summary`: AI-generated document summary
- `index_id`: LlamaIndex vector database ID
- `source_references`: Vector search results in chat

## Security Implementation

### Authentication & Authorization

1. **Supabase JWT Validation**

   ```javascript
   const {
     data: { user },
     error,
   } = await supabase.auth.getUser(token);
   if (error || !user) {
     return res.status(401).json({ message: "Invalid token" });
   }
   ```

2. **Row Level Security (RLS)**
   - Database-level access control
   - Users can only access their own data
   - Automatic filtering based on user ID

### Embed Security

1. **Domain Whitelist**

   - Validate referrer header
   - Per-project domain restrictions
   - Active/inactive domain management

2. **JWT Embed Tokens**

   - Time-limited access (24 hours)
   - Tied to specific content
   - Enhanced security option

3. **Rate Limiting**
   ```javascript
   const embedRateLimit = rateLimit({
     windowMs: 15 * 60 * 1000, // 15 minutes
     max: 100, // limit each IP to 100 requests per windowMs
     message: "Too many embed requests",
   });
   ```

### Data Protection

1. **Input Validation**

   - Zod schema validation
   - File content verification
   - Malicious file detection

2. **Error Sanitization**
   - No sensitive data in error messages
   - Structured error logging
   - Client-safe error responses

## Recommendations

### For NestJS Integration

1. **Entity Mapping**

   - Map Drizzle schema to TypeORM entities
   - Maintain relationship consistency
   - Use same field names and types

2. **Service Architecture**

   - Implement repository pattern
   - Separate business logic from data access
   - Use DTOs for data validation

3. **Database Strategy**

   - Share same PostgreSQL database
   - NestJS: Full CRUD operations
   - Express: Read-only + limited writes

4. **API Design**

   ```typescript
   // NestJS Endpoints
   POST /api/chat-ai/projects          // Create project
   POST /api/chat-ai/documents/upload  // Upload document
   POST /api/chat-ai/chat              // Internal chat

   // Express Endpoints
   POST /api/embed/chat/ask            // Public chat
   GET /api/embed/projects/:id         // Public project info
   ```

### Performance Optimization

1. **Caching Strategy**

   - Redis for session management
   - Cache vector search results
   - CDN for embed widgets

2. **Database Optimization**

   - Index on user_id fields
   - Optimize vector search queries
   - Connection pooling

3. **Monitoring**
   - API response times
   - Credit usage patterns
   - Embed performance metrics

### Scalability Considerations

1. **Horizontal Scaling**

   - Stateless service design
   - Load balancer configuration
   - Database read replicas

2. **Service Separation**

   - Independent deployment
   - Different scaling requirements
   - Fault isolation

3. **Resource Management**
   - Background job queues
   - Async document processing
   - Rate limiting per service

---

**Document Generated**: December 2024
**Version**: 1.0
**Author**: AI Architecture Analysis

## Summary

This document provides a comprehensive analysis of the LlamaContextGenie architecture, covering:

- **Database Schema**: Complete PostgreSQL schema with relationships
- **Processing Pipeline**: Document upload → parsing → indexing → ready
- **Vector Integration**: LlamaIndex storage and retrieval patterns
- **Security Implementation**: Authentication, authorization, and embed security
- **Service Architecture**: Current single-service and proposed dual-service design
- **Integration Points**: Key areas for NestJS ChatAI service integration

The architecture demonstrates a well-designed system with proper separation of concerns, robust security measures, and scalable design patterns suitable for enterprise-level document Q&A applications.
