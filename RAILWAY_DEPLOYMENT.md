# Railway Deployment Guide

## Overview

This guide covers deploying your DocChat AI application to Railway. Railway provides a simple and efficient way to deploy full-stack applications with minimal configuration.

## Prerequisites

1. [Railway account](https://railway.app)
2. GitHub repository with your code
3. Required API keys and configurations

## Deployment Steps

### 1. Create Railway Project

1. Go to [Railway](https://railway.app) and sign in
2. Click "New Project"
3. Select "Deploy from GitHub repo"
4. Choose your repository
5. Railway will automatically detect it's a Node.js project

### 2. Environment Variables Configuration

⚠️ **CRITICAL**: Set the `APP_URL` environment variable to your Railway deployment URL to avoid localhost URL issues.

In your Railway project dashboard, go to the "Variables" tab and add these environment variables:

#### Required Variables
```bash
# Application URL - MUST be set to your Railway deployment URL
APP_URL=https://your-app-name.railway.app

# Database
DATABASE_URL=postgresql://postgres:[password]@host:port/database
SUPABASE_URL=https://your-project.supabase.co
SUPABASE_SERVICE_ROLE_KEY=your-service-role-key

# Frontend (for client-side Supabase)
VITE_SUPABASE_URL=https://your-project.supabase.co
VITE_SUPABASE_ANON_KEY=your-anon-key

# AI Services
LLAMA_CLOUD_API_KEY=your-llama-cloud-key
OPENROUTER_API_KEY=your-openrouter-key

# Embed Security
EMBED_JWT_SECRET=your-super-secret-key-for-embed-tokens-here

# Production Environment
NODE_ENV=production
```

#### Important Notes:
- Replace `https://your-app-name.railway.app` with your actual Railway deployment URL
- You can find your Railway URL in the project dashboard after deployment
- **DO NOT** set `VITE_HMR_HOST` in production (only needed for local development)

### 3. Railway Configuration

Create a `railway.json` file in your project root (if not already present):

```json
{
  "$schema": "https://railway.app/railway.schema.json",
  "build": {
    "builder": "NIXPACKS"
  },
  "deploy": {
    "numReplicas": 1,
    "sleepApplication": false,
    "restartPolicyType": "ON_FAILURE",
    "restartPolicyMaxRetries": 10
  }
}
```

### 4. Build Configuration

Your `package.json` should have these scripts:
```json
{
  "scripts": {
    "build": "npm run build:client && npm run build:server",
    "build:client": "vite build",
    "build:server": "tsc",
    "start": "node dist/index.js",
    "dev": "tsx watch server/index.ts --ignore client/",
    "db:push": "drizzle-kit push"
  }
}
```

### 5. Database Setup

If using Supabase:
1. Create your Supabase project
2. Run the SQL migrations in your Supabase SQL editor
3. Copy the connection details to your Railway environment variables

### 6. Deploy

1. Push your code to GitHub
2. Railway will automatically build and deploy
3. Monitor the build logs in Railway dashboard
4. Once deployed, your app will be available at `https://your-app-name.railway.app`

## Post-Deployment Setup

### 1. Update APP_URL

After your first deployment:
1. Copy your Railway deployment URL (e.g., `https://your-app-name.railway.app`)
2. Update the `APP_URL` environment variable in Railway dashboard
3. Trigger a redeployment to apply the changes

### 2. Test the Application

1. Visit your Railway URL
2. Test authentication
3. Try uploading a document
4. Test the chat functionality

### 3. Custom Domain (Optional)

1. Go to Railway project settings
2. Click "Domains"
3. Add your custom domain
4. Update DNS records as instructed
5. Update `APP_URL` environment variable to use your custom domain

## Troubleshooting

### Build Failures

Check the build logs in Railway dashboard. Common issues:
- Missing environment variables
- TypeScript compilation errors
- Missing dependencies

### Runtime Issues

1. **localhost URLs in production**: Ensure `APP_URL` is set to your Railway URL
2. **Database connection**: Verify `DATABASE_URL` is correct
3. **API keys**: Check all required API keys are set

### Environment Variable Issues

If you see localhost URLs in your deployed app:
1. Check that `APP_URL` is set correctly in Railway
2. Redeploy the application
3. Clear browser cache

### Health Check

Your app includes a health endpoint at `/health`. You can check:
```bash
curl https://your-app-name.railway.app/health
```

Should return:
```json
{
  "status": "ok",
  "timestamp": "2024-01-01T00:00:00.000Z"
}
```

## Monitoring

Railway provides built-in monitoring:
1. View logs in the Railway dashboard
2. Monitor resource usage
3. Set up alerts for failures

## Scaling

Railway automatically handles:
- Auto-scaling based on traffic
- Load balancing
- Zero-downtime deployments

## Security Considerations

1. Never commit `.env` files to git
2. Use Railway's environment variables for all secrets
3. Enable Railway's security features
4. Regularly rotate API keys
5. Monitor access logs

## Cost Optimization

1. Use Railway's sleep feature for development environments
2. Monitor resource usage
3. Optimize build times
4. Use appropriate instance sizes

## Support

- Railway Documentation: https://docs.railway.app
- Railway Community: https://railway.app/discord
- DocChat AI Issues: Create an issue in your repository

## Continuous Deployment

Railway automatically deploys when you push to your main branch. To disable:
1. Go to project settings
2. Uncheck "Auto Deploy"
3. Deploy manually when needed 