-- Migration script to make embed domains project-specific
-- Run this AFTER running the main supabase_embed_security_migration.sql

-- Step 1: Add project_id column if it doesn't exist (safe operation)
DO $$ 
BEGIN 
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.columns 
        WHERE table_name = 'embed_domains' 
        AND column_name = 'project_id'
    ) THEN
        ALTER TABLE public.embed_domains ADD COLUMN project_id INTEGER;
        
        -- Add foreign key constraint
        ALTER TABLE public.embed_domains 
        ADD CONSTRAINT fk_embed_domains_project_id 
        FOREIGN KEY (project_id) REFERENCES public.projects (id) ON DELETE CASCADE;
        
        -- Add index for project_id
        CREATE INDEX IF NOT EXISTS idx_embed_domains_project_id ON public.embed_domains(project_id);
    END IF;
END $$;

-- Step 2: Drop the old unique constraint and add the new one
DO $$
BEGIN
    -- Drop old constraint if it exists
    IF EXISTS (
        SELECT 1 FROM information_schema.table_constraints 
        WHERE constraint_name = 'unique_user_domain' 
        AND table_name = 'embed_domains'
    ) THEN
        ALTER TABLE public.embed_domains DROP CONSTRAINT unique_user_domain;
    END IF;
    
    -- Add new constraint if it doesn't exist
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.table_constraints 
        WHERE constraint_name = 'unique_user_project_domain' 
        AND table_name = 'embed_domains'
    ) THEN
        ALTER TABLE public.embed_domains 
        ADD CONSTRAINT unique_user_project_domain UNIQUE(user_id, project_id, domain);
    END IF;
END $$;

-- Step 3: Optional - Migrate existing domains to be project-specific
-- This step is commented out by default. Uncomment if you want to assign
-- existing global domains to all projects of each user.

/*
-- Migrate existing global domains (project_id = NULL) to all user projects
INSERT INTO public.embed_domains (user_id, project_id, domain, is_active, created_at)
SELECT DISTINCT 
    ed.user_id,
    p.id as project_id,
    ed.domain,
    ed.is_active,
    ed.created_at
FROM public.embed_domains ed
CROSS JOIN public.projects p
WHERE ed.project_id IS NULL 
    AND p.user_id = ed.user_id
    AND NOT EXISTS (
        -- Avoid duplicates
        SELECT 1 FROM public.embed_domains ed2 
        WHERE ed2.user_id = ed.user_id 
            AND ed2.project_id = p.id 
            AND ed2.domain = ed.domain
    );

-- Remove the old global domains after migration
-- DELETE FROM public.embed_domains WHERE project_id IS NULL;
*/

-- Step 4: Add helpful comments
COMMENT ON COLUMN public.embed_domains.project_id IS 'Project ID for project-specific domain restrictions. NULL means global domain (legacy).';
COMMENT ON TABLE public.embed_domains IS 'Domain whitelist for embed security. Domains can be project-specific or global (legacy).';

-- Grant necessary permissions (in case they were removed)
GRANT USAGE ON SCHEMA public TO anon, authenticated;
GRANT SELECT, INSERT, UPDATE, DELETE ON public.embed_domains TO authenticated;
GRANT SELECT, INSERT ON public.embed_usage TO authenticated, anon;
GRANT EXECUTE ON FUNCTION public.track_embed_usage TO authenticated, anon;

-- Show current state
SELECT 
    'Migration completed successfully!' as status,
    COUNT(*) as total_domains,
    COUNT(CASE WHEN project_id IS NULL THEN 1 END) as global_domains,
    COUNT(CASE WHEN project_id IS NOT NULL THEN 1 END) as project_specific_domains
FROM public.embed_domains; 