-- Migration: Add summary column to documents table
-- Date: 2024-12-xx

-- Add summary column to documents table
ALTER TABLE documents 
ADD COLUMN summary JSONB;

-- Add comment to explain the summary structure
COMMENT ON COLUMN documents.summary IS 'AI-generated summary containing: { "main_summary": "...", "key_points": [...], "topics": [...], "entities": [...], "suggested_questions": [...] }';

-- Add index for better performance when filtering/searching summaries
CREATE INDEX idx_documents_summary_gin ON documents USING GIN(summary);

-- Update existing ready documents to have empty summary (will be generated)
UPDATE documents 
SET summary = NULL 
WHERE status = 'ready' AND summary IS NULL; 