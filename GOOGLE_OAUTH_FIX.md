# Fix Google OAuth 403 Error

## The Problem
You're getting a 403 error because Supabase's default Google OAuth has restrictions. You need to create your own Google OAuth credentials.

## Quick Fix Steps

### Step 1: Create Google Cloud Project
1. Go to [Google Cloud Console](https://console.cloud.google.com/)
2. Create a new project or select existing one
3. Name it "DocChat AI" or similar

### Step 2: Enable APIs
1. Go to **APIs & Services** → **Library**
2. Search for "Google+ API" or "Google Identity API"
3. Click **Enable**

### Step 3: Configure OAuth Consent Screen
1. Go to **APIs & Services** → **OAuth consent screen**
2. Choose **External** user type
3. Fill in required fields:
   - App name: "DocChat AI"
   - User support email: your email
   - Developer contact: your email
4. Click **Save and Continue** through all steps

### Step 4: Create OAuth Credentials
1. Go to **APIs & Services** → **Credentials**
2. Click **Create Credentials** → **OAuth 2.0 Client IDs**
3. Application type: **Web application**
4. Name: "DocChat AI Web Client"
5. **Authorized redirect URIs**: Add this exact URL:
   ```
   https://ktoykvkoxwpktxrgcnkn.supabase.co/auth/v1/callback
   ```
6. Click **Create**
7. Copy the **Client ID** and **Client Secret**

### Step 5: Update Supabase
1. Go back to your Supabase dashboard
2. **Authentication** → **Providers** → **Google**
3. Paste your **Client ID** and **Client Secret**
4. Click **Save**

### Step 6: Test
Try the Google sign-in again - it should now work without the 403 error!

## Your Project Details
- Supabase Project: ktoykvkoxwpktxrgcnkn
- Redirect URI: https://ktoykvkoxwpktxrgcnkn.supabase.co/auth/v1/callback