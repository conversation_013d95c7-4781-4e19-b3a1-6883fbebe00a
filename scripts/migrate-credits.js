#!/usr/bin/env node

/**
 * Migration Script: Update Free User Credits from 7 to 30
 * 
 * This script updates all existing free users to have 30 total credits
 * instead of the previous 7 credits.
 * 
 * Usage:
 * node scripts/migrate-credits.js
 */

const { createClient } = require('@supabase/supabase-js');
require('dotenv').config();

// Initialize Supabase client
const supabase = createClient(
  process.env.NEXT_PUBLIC_SUPABASE_URL,
  process.env.SUPABASE_SERVICE_ROLE_KEY // Use service role key for admin operations
);

async function migrateFreeUserCredits() {
  console.log('🚀 Starting migration: Update free user credits from 7 to 30...');
  
  try {
    // First, get all free users to see what we're working with
    const { data: freeUsers, error: fetchError } = await supabase
      .from('user_profiles')
      .select('id, email, subscription_status, credits, total_credits_used')
      .eq('subscription_status', 'free');

    if (fetchError) {
      throw new Error(`Failed to fetch free users: ${fetchError.message}`);
    }

    console.log(`📊 Found ${freeUsers.length} free users`);
    
    if (freeUsers.length === 0) {
      console.log('✅ No free users found to migrate');
      return;
    }

    // Show current state
    console.log('\n📋 Current free users state:');
    freeUsers.forEach(user => {
      const availableCredits = user.credits - user.total_credits_used;
      console.log(`   ${user.email}: ${availableCredits} available credits (${user.credits} total - ${user.total_credits_used} used)`);
    });

    // Filter users who need migration (have less than 30 available credits)
    const usersToMigrate = freeUsers.filter(user => {
      const availableCredits = user.credits - user.total_credits_used;
      return availableCredits < 30;
    });

    if (usersToMigrate.length === 0) {
      console.log('✅ All free users already have 30+ available credits. No migration needed.');
      return;
    }

    console.log(`\n🔄 Migrating ${usersToMigrate.length} users who have less than 30 available credits...`);

    // Update users to have 30 total available credits
    for (const user of usersToMigrate) {
      const newTotalCredits = 30 + user.total_credits_used;
      
      const { error: updateError } = await supabase
        .from('user_profiles')
        .update({ credits: newTotalCredits })
        .eq('id', user.id);

      if (updateError) {
        console.error(`❌ Failed to update user ${user.email}: ${updateError.message}`);
        continue;
      }

      const oldAvailable = user.credits - user.total_credits_used;
      console.log(`   ✅ ${user.email}: ${oldAvailable} → 30 available credits`);
    }

    // Verify the migration by fetching updated data
    console.log('\n🔍 Verifying migration results...');
    const { data: updatedUsers, error: verifyError } = await supabase
      .from('user_profiles')
      .select('id, email, subscription_status, credits, total_credits_used')
      .eq('subscription_status', 'free');

    if (verifyError) {
      console.error(`❌ Failed to verify migration: ${verifyError.message}`);
      return;
    }

    console.log('\n📊 Updated free users state:');
    updatedUsers.forEach(user => {
      const availableCredits = user.credits - user.total_credits_used;
      console.log(`   ${user.email}: ${availableCredits} available credits (${user.credits} total - ${user.total_credits_used} used)`);
    });

    console.log('\n✅ Migration completed successfully!');
    console.log(`📈 Summary:`);
    console.log(`   - Total free users: ${freeUsers.length}`);
    console.log(`   - Users migrated: ${usersToMigrate.length}`);
    console.log(`   - Users with 30+ credits: ${updatedUsers.filter(u => u.credits - u.total_credits_used >= 30).length}`);

  } catch (error) {
    console.error('❌ Migration failed:', error.message);
    process.exit(1);
  }
}

// Run the migration
if (require.main === module) {
  migrateFreeUserCredits()
    .then(() => {
      console.log('\n🎉 Migration script completed!');
      process.exit(0);
    })
    .catch(error => {
      console.error('💥 Migration script failed:', error);
      process.exit(1);
    });
}

module.exports = { migrateFreeUserCredits }; 