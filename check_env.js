#!/usr/bin/env node

import dotenv from 'dotenv';

// Load environment variables
console.log('🔍 Loading .env file...');
const result = dotenv.config();

if (result.error) {
  console.error('❌ Error loading .env:', result.error);
} else {
  console.log('✅ .env file loaded successfully');
}

console.log('\n📋 ALL ENVIRONMENT VARIABLES:');
console.log('='.repeat(50));

// Sort environment variables for easier reading
const sortedEnv = Object.keys(process.env).sort();

sortedEnv.forEach(key => {
  const value = process.env[key];
  
  // Mask sensitive values
  if (key.includes('KEY') || key.includes('SECRET') || key.includes('PASSWORD') || key.includes('TOKEN')) {
    console.log(`${key}: ${value ? '[SET - ' + value.length + ' chars]' : '[NOT SET]'}`);
  } else {
    console.log(`${key}: ${value || '[NOT SET]'}`);
  }
});

console.log('\n🎯 KEY DATABASE VARIABLES:');
console.log('='.repeat(50));

const dbVars = [
  'DATABASE_URL',
  'SUPABASE_URL', 
  'SUPABASE_SERVICE_ROLE_KEY',
  'VITE_SUPABASE_URL',
  'VITE_SUPABASE_ANON_KEY'
];

dbVars.forEach(key => {
  const value = process.env[key];
  if (value) {
    if (key.includes('URL')) {
      console.log(`✅ ${key}: ${value}`);
    } else {
      console.log(`✅ ${key}: [SET - ${value.length} characters]`);
    }
  } else {
    console.log(`❌ ${key}: [NOT SET]`);
  }
});

console.log('\n🧪 TESTING VARIABLE PARSING:');
console.log('='.repeat(50));

// Test if DATABASE_URL can be parsed
const dbUrl = process.env.DATABASE_URL;
if (dbUrl) {
  try {
    const url = new URL(dbUrl);
    console.log('✅ DATABASE_URL parsing:');
    console.log(`   Protocol: ${url.protocol}`);
    console.log(`   Host: ${url.hostname}`);
    console.log(`   Port: ${url.port || 'default'}`);
    console.log(`   Database: ${url.pathname.slice(1)}`);
    console.log(`   Username: ${url.username}`);
    console.log(`   Password: ${url.password ? '[SET]' : '[NOT SET]'}`);
  } catch (error) {
    console.log('❌ DATABASE_URL parsing failed:', error.message);
  }
} else {
  console.log('❌ DATABASE_URL not found');
}

console.log('\n🌐 NODE ENVIRONMENT:');
console.log('='.repeat(50));
console.log(`NODE_ENV: ${process.env.NODE_ENV || '[NOT SET]'}`);
console.log(`NODE_VERSION: ${process.version}`);
console.log(`Platform: ${process.platform}`);
console.log(`Architecture: ${process.arch}`);
console.log(`Working Directory: ${process.cwd()}`); 