import { pgTable, text, serial, integer, boolean, timestamp, jsonb } from "drizzle-orm/pg-core";
import { createInsertSchema } from "drizzle-zod";
import { z } from "zod";

// User profiles table for subscription and credits tracking
export const userProfiles = pgTable("user_profiles", {
  id: text("id").primaryKey(), // Supabase user ID
  email: text("email").notNull(),
  subscriptionStatus: text("subscription_status").notNull().default("free"), // free, pro
  subscriptionStartDate: timestamp("subscription_start_date"),
  subscriptionEndDate: timestamp("subscription_end_date"),
  credits: integer("credits").notNull().default(30), // Free credits: 30 total
  totalCreditsUsed: integer("total_credits_used").notNull().default(0),
  createdAt: timestamp("created_at").defaultNow().notNull(),
  updatedAt: timestamp("updated_at").defaultNow().notNull(),
});

// Credit usage tracking table
export const creditUsage = pgTable("credit_usage", {
  id: serial("id").primaryKey(),
  userId: text("user_id").notNull(),
  actionType: text("action_type").notNull(), // 'project_create', 'document_upload', 'chat_message'
  actionId: integer("action_id"), // Reference to project/document/message ID
  creditsUsed: integer("credits_used").notNull().default(1),
  timestamp: timestamp("timestamp").defaultNow().notNull(),
});

export const projects = pgTable("projects", {
  id: serial("id").primaryKey(),
  name: text("name").notNull(),
  description: text("description"),
  userId: text("user_id").notNull(),
  indexId: text("index_id"), // LlamaIndex vector DB embedding ID for the entire project
  status: text("status").notNull().default("active"), // active, archived, deleted
  createdAt: timestamp("created_at").defaultNow().notNull(),
  updatedAt: timestamp("updated_at").defaultNow().notNull(),
});

export const documents = pgTable("documents", {
  id: serial("id").primaryKey(),
  projectId: integer("project_id").references(() => projects.id).notNull(),
  userId: text("user_id").notNull(),
  filename: text("filename").notNull(),
  filesize: integer("filesize").notNull(),
  contentType: text("content_type").notNull(),
  status: text("status").notNull().default("uploading"), // uploading, parsing, indexing, ready, error
  parsedData: jsonb("parsed_data"),
  summary: jsonb("summary"), // AI-generated summary and key insights
  pageCount: integer("page_count"),
  wordCount: integer("word_count"),
  errorMessage: text("error_message"),
  createdAt: timestamp("created_at").defaultNow().notNull(),
});

export const chatMessages = pgTable("chat_messages", {
  id: serial("id").primaryKey(),
  projectId: integer("project_id").references(() => projects.id).notNull(),
  documentId: integer("document_id").references(() => documents.id), // optional - if question relates to specific doc
  question: text("question").notNull(),
  response: text("response").notNull(),
  sourceReferences: jsonb("source_references"), // array of page references from multiple docs
  isAdminDebug: boolean("is_admin_debug").default(false), // Mark admin debug sessions
  timestamp: timestamp("timestamp").defaultNow().notNull(),
});

export const insertUserProfileSchema = createInsertSchema(userProfiles).pick({
  id: true,
  email: true,
  subscriptionStatus: true,
  subscriptionStartDate: true,
  subscriptionEndDate: true,
  credits: true,
  totalCreditsUsed: true,
});

export const insertCreditUsageSchema = createInsertSchema(creditUsage).pick({
  userId: true,
  actionType: true,
  actionId: true,
  creditsUsed: true,
});

export const insertProjectSchema = createInsertSchema(projects).pick({
  name: true,
  description: true,
  userId: true,
});

export const insertDocumentSchema = createInsertSchema(documents).pick({
  projectId: true,
  userId: true,
  filename: true,
  filesize: true,
  contentType: true,
});

export const insertChatMessageSchema = createInsertSchema(chatMessages).pick({
  projectId: true,
  documentId: true,
  question: true,
  response: true,
  sourceReferences: true,
  isAdminDebug: true,
});

export const questionSchema = z.object({
  projectId: z.number().optional(),
  documentId: z.number().optional(),
  question: z.string().min(1, "Question is required"),
}).refine(data => data.projectId || data.documentId, {
  message: "Either projectId or documentId must be provided",
});

export const embedQuestionSchema = z.object({
  projectId: z.number().optional(),
  documentId: z.number().optional(),
  question: z.string().min(1, "Question is required"),
  token: z.string().optional(), // JWT token for enhanced security
}).refine(data => data.projectId || data.documentId, {
  message: "Either projectId or documentId must be provided",
});

export const createProjectSchema = z.object({
  name: z.string().min(1, "Project name is required").max(100, "Project name too long"),
  description: z.string().max(500, "Description too long").optional(),
});

export type UserProfile = typeof userProfiles.$inferSelect;
export type InsertUserProfile = z.infer<typeof insertUserProfileSchema>;
export type CreditUsage = typeof creditUsage.$inferSelect;
export type InsertCreditUsage = z.infer<typeof insertCreditUsageSchema>;

export type InsertProject = z.infer<typeof insertProjectSchema>;
export type Project = typeof projects.$inferSelect;
export type ProjectWithCounts = Project & {
  documentCount: number;
  readyDocumentCount: number;
  chatCount: number;
};
export type InsertDocument = z.infer<typeof insertDocumentSchema>;
export type Document = typeof documents.$inferSelect;
export type InsertChatMessage = z.infer<typeof insertChatMessageSchema>;
export type ChatMessage = typeof chatMessages.$inferSelect;
export type QuestionInput = z.infer<typeof questionSchema>;
export type EmbedQuestionInput = z.infer<typeof embedQuestionSchema>;
export type CreateProjectInput = z.infer<typeof createProjectSchema>;

export type UserProfileWithUsage = UserProfile & {
  creditsRemaining: number;
  projectsCreated: number;
  documentsUploaded: number;
  chatMessagesUsed: number;
};
