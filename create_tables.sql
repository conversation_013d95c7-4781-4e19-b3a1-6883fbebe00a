-- LlamaContextGenie Database Schema - Subscription & Credits Update
-- Execute this in Supabase SQL Editor to create the new subscription tables

-- Create user_profiles table
CREATE TABLE IF NOT EXISTS public.user_profiles (
  id TEXT PRIMARY KEY, -- Supabase user ID
  email TEXT NOT NULL,
  subscription_status TEXT NOT NULL DEFAULT 'free',
  subscription_start_date TIMESTAMP WITH TIME ZONE,
  subscription_end_date TIMESTAMP WITH TIME ZONE,
  credits INTEGER NOT NULL DEFAULT 30,
  total_credits_used INTEGER NOT NULL DEFAULT 0,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW() NOT NULL,
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW() NOT NULL
);

-- Create credit_usage table
CREATE TABLE IF NOT EXISTS public.credit_usage (
  id SERIAL PRIMARY KEY,
  user_id TEXT NOT NULL,
  action_type TEXT NOT NULL, -- 'project_create', 'document_upload', 'chat_message'
  action_id INTEGER, -- Reference to project/document/message ID
  credits_used INTEGER NOT NULL DEFAULT 1,
  timestamp TIMESTAMP WITH TIME ZONE DEFAULT NOW() NOT NULL
);

-- Enable Row Level Security
ALTER TABLE public.user_profiles ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.credit_usage ENABLE ROW LEVEL SECURITY;

-- Create RLS policies for user_profiles
CREATE POLICY "Users can only see their own profile" ON public.user_profiles
  FOR ALL USING (auth.uid()::text = id);

-- Create RLS policies for credit_usage
CREATE POLICY "Users can only see their own credit usage" ON public.credit_usage
  FOR ALL USING (auth.uid()::text = user_id);

-- Create indexes for better performance
CREATE INDEX IF NOT EXISTS idx_user_profiles_email ON public.user_profiles(email);
CREATE INDEX IF NOT EXISTS idx_user_profiles_subscription_status ON public.user_profiles(subscription_status);
CREATE INDEX IF NOT EXISTS idx_credit_usage_user_id ON public.credit_usage(user_id);
CREATE INDEX IF NOT EXISTS idx_credit_usage_action_type ON public.credit_usage(action_type);
CREATE INDEX IF NOT EXISTS idx_credit_usage_timestamp ON public.credit_usage(timestamp);

-- Optional: Create a function to deduct credits atomically
CREATE OR REPLACE FUNCTION deduct_user_credits(
  p_user_id TEXT,
  p_action_type TEXT,
  p_action_id INTEGER DEFAULT NULL,
  p_credits_used INTEGER DEFAULT 1
)
RETURNS BOOLEAN
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
BEGIN
  -- Update user profile credits
  UPDATE public.user_profiles 
  SET 
    total_credits_used = total_credits_used + p_credits_used,
    updated_at = NOW()
  WHERE id = p_user_id;
  
  -- Insert credit usage record
  INSERT INTO public.credit_usage (user_id, action_type, action_id, credits_used)
  VALUES (p_user_id, p_action_type, p_action_id, p_credits_used);
  
  RETURN TRUE;
EXCEPTION
  WHEN OTHERS THEN
    RETURN FALSE;
END;
$$;

-- Embed domains table for security
CREATE TABLE IF NOT EXISTS embed_domains (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    user_id INTEGER NOT NULL,
    project_id INTEGER,
    domain VARCHAR(255) NOT NULL,
    is_active BOOLEAN DEFAULT true,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users (id) ON DELETE CASCADE,
    FOREIGN KEY (project_id) REFERENCES projects (id) ON DELETE CASCADE,
    UNIQUE(user_id, project_id, domain)
);

-- Index for faster domain lookups
CREATE INDEX IF NOT EXISTS idx_embed_domains_user_id ON embed_domains(user_id);
CREATE INDEX IF NOT EXISTS idx_embed_domains_project_id ON embed_domains(project_id);
CREATE INDEX IF NOT EXISTS idx_embed_domains_domain ON embed_domains(domain);

-- Embed usage tracking
CREATE TABLE IF NOT EXISTS embed_usage (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    user_id INTEGER NOT NULL,
    document_id INTEGER,
    project_id INTEGER,
    domain VARCHAR(255),
    ip_address VARCHAR(45),
    user_agent TEXT,
    requests_count INTEGER DEFAULT 1,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users (id) ON DELETE CASCADE,
    FOREIGN KEY (document_id) REFERENCES documents (id) ON DELETE CASCADE,
    FOREIGN KEY (project_id) REFERENCES projects (id) ON DELETE CASCADE
);

-- Index for usage tracking
CREATE INDEX IF NOT EXISTS idx_embed_usage_user_id ON embed_usage(user_id);
CREATE INDEX IF NOT EXISTS idx_embed_usage_domain ON embed_usage(domain);
CREATE INDEX IF NOT EXISTS idx_embed_usage_created_at ON embed_usage(created_at);
