# Railway Environment Variables Setup Checklist

## 🚨 Critical Fix for localhost URL Issues

This checklist ensures your app uses the correct URLs when deployed on Railway instead of falling back to localhost.

## ✅ Changes Made to Codebase

### 1. Updated Environment Configuration
- ✅ Added `APP_URL` to `example.env` 
- ✅ Added `VITE_HMR_HOST` to `example.env` for development
- ✅ Updated `vite.config.ts` to use `process.env.VITE_HMR_HOST`
- ✅ Updated `server/vite.ts` to use `process.env.VITE_HMR_HOST`

### 2. Existing Code Already Uses Environment Variables
- ✅ `server/services/openrouter.ts` already uses `process.env.APP_URL || "http://localhost:5000"`
- ✅ Localhost detection in `server/routes.ts` is only for development mode

## 📋 Railway Deployment Checklist

### Step 1: Set Environment Variables in Railway Dashboard

Go to your Railway project → Variables tab and add:

```bash
# 🔥 MOST IMPORTANT - Replace with your actual Railway URL
APP_URL=https://your-app-name.railway.app

# Database & Supabase
DATABASE_URL=postgresql://postgres:[password]@host:port/database
SUPABASE_URL=https://your-project.supabase.co
SUPABASE_SERVICE_ROLE_KEY=your-service-role-key
VITE_SUPABASE_URL=https://your-project.supabase.co
VITE_SUPABASE_ANON_KEY=your-anon-key

# AI Services
LLAMA_CLOUD_API_KEY=your-llama-cloud-key
OPENROUTER_API_KEY=your-openrouter-key

# Security
EMBED_JWT_SECRET=your-super-secret-key-for-embed-tokens-here

# Environment
NODE_ENV=production
```

### Step 2: Important Notes

⚠️ **DO NOT SET** these variables in production:
- `VITE_HMR_HOST` (only for local development)
- `PORT` (Railway sets this automatically)

### Step 3: Find Your Railway URL

1. Deploy your app to Railway first
2. Go to Railway dashboard → your project
3. Copy the generated URL (e.g., `https://web-production-abc123.up.railway.app`)
4. Update `APP_URL` environment variable with this URL
5. Redeploy or restart your service

### Step 4: Verify the Fix

After deployment, check these URLs in your browser:
- `https://your-app-name.railway.app/health` - Should return JSON status
- Open browser dev tools → Network tab
- All API calls should use `https://your-app-name.railway.app` instead of localhost

## 🔧 Local Development Setup

For local development, create a `.env` file:

```bash
# Copy from example.env and update values
APP_URL=http://localhost:5000
VITE_HMR_HOST=localhost

# Add your actual API keys...
DATABASE_URL=your-local-db-url
LLAMA_CLOUD_API_KEY=your-key
OPENROUTER_API_KEY=your-key
# etc...
```

## 🐛 Troubleshooting

### If you still see localhost URLs:

1. **Check APP_URL is set correctly in Railway**
   ```bash
   # Should be your Railway URL, not localhost
   APP_URL=https://your-app-name.railway.app
   ```

2. **Redeploy after changing environment variables**
   - Change the environment variable
   - Go to Deployments tab
   - Click "Deploy Latest"

3. **Clear browser cache**
   - Hard refresh (Ctrl+F5 or Cmd+Shift+R)
   - Or open in incognito/private mode

4. **Check the specific files that were updated:**
   - `server/services/openrouter.ts` line 71
   - `vite.config.ts` line 33
   - `server/vite.ts` line 27

### Verification Commands

```bash
# Check your deployed app health
curl https://your-app-name.railway.app/health

# Should return:
# {"status":"ok","timestamp":"2024-01-01T00:00:00.000Z"}
```

## 📝 Summary

The main issue was that hardcoded `localhost` URLs were being used in production. The fix:

1. **Environment Variables**: Use `APP_URL` environment variable for all URL references
2. **Railway Configuration**: Set `APP_URL` to your actual Railway deployment URL
3. **Development vs Production**: Different configurations for local development and production

After these changes, your app will correctly use the Railway deployment URL instead of localhost when deployed. 